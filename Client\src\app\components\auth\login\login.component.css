.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: white;
}

.login-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  border: 1px solid black;

  max-width: 600px;
}

h1 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 8px;
}

h2 {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 16px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}

.disclaimer {
  font-size: 12px;
  color: #666;
  margin: 16px 0;
}

.btn-primary {
  width: 100%;
  padding: 12px;
  background-color: #ff385c;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}

.btn-primary:disabled {
  background-color: #ff385c;
  cursor: not-allowed;
}

.divider {
  display: flex;
  align-items: center;
  margin: 16px 0;
  color: #666;
}

.divider::before,
.divider::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid #ddd;
}

.divider::before {
  margin-right: 16px;
}

.divider::after {
  margin-left: 16px;
}

.btn-social {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-social i {
  margin-right: 8px;
}

.alert {
  padding: 12px;
  border-radius: 8px;
  margin-top: 16px;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.signup-link {
  text-align: center;
  margin-top: 16px;
  font-size: 14px;
}

.signup-link a {
  color: #ff385c;
  text-decoration: none;
  cursor: pointer;
  font-weight: 500;
}

.error-message {
  color: #ff0000;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.form-control.invalid {
  border-color: #ff0000;
}


.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-toggle-btn {
  position: absolute;
  right: 10px;
  background: none;
  border: none;
  cursor: pointer;
  color: #777;
  padding: 0;
  font-size: 16px;
}

.password-toggle-btn:focus {
  outline: none;
}

.form-control {
  width: 100%;
  padding-right: 40px;
}

.forgot-password-link {
  text-align: right;
  margin-bottom: 16px;
  position: relative;
  transition: all 0.3s ease;
}

.forgot-password-link a {
  color: #ff385c;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  position: relative;
  padding-bottom: 2px;
  transition: all 0.3s ease;
}

.forgot-password-link a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: #ff385c;
  transition: width 0.3s ease;
}

.forgot-password-link a:hover {
  color: #d93250;
}

.forgot-password-link a:hover::after {
  width: 100%;
}

/**/