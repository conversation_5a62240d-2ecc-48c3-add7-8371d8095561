## A streamlined .gitignore for modern .NET projects
## including temporary files, build results, and
## files generated by popular .NET tools. If you are
## developing with Visual Studio, the VS .gitignore
## https://github.com/github/gitignore/blob/main/VisualStudio.gitignore
## has more thorough IDE-specific entries.
##
## Get latest from https://github.com/github/gitignore/blob/main/Dotnet.gitignore

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/
project.assets.json
project.nuget.cache

# ASP.NET Scaffolding
ScaffoldingReadMe.txt

# NuGet Packages
*.nupkg
# NuGet Symbol Packages
*.snupkg
packages/

# Ignore test results
TestResults/

# Others
~$*
*~
CodeCoverage/

# MSBuild Binary and Structured Log
*.binlog

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUnit
*.VisualState.xml
TestResult.xml
nunit-*.xml

# Visual Studio-specific files
.vs/
*.user
*.suo
*.sln.docstates

# Error logs
logs/

# Angular (Frontend)
# Ignore node_modules (dependencies can be reinstalled via npm install)
node_modules/

# Ignore Angular build output
dist/
coverage/

# Ignore Angular environment files with sensitive data
src/environments/environment.*.ts
!src/environments/environment.ts
!src/environments/environment.prod.ts

# Ignore npm/yarn logs and lock files (optional, depending on your preference)
npm-debug.log
yarn-error.log
package-lock.json
yarn.lock

# General
# Ignore OS-generated files
.DS_Store
Thumbs.db

# Ignore editor/IDE files
.vscode/
.idea/
Client/.vscode/
Client/.editorconfig

# Ignore local database files (if any)
*.mdf
*.ldf

# Ignore temporary files
*.tmp
*.log
