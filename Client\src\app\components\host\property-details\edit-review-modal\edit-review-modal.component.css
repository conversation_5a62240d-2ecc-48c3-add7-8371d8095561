/* Edit Review Modal - Airbnb style */
.review-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}

.review-modal-content {
    position: relative;
    width: 90%;
    max-width: 568px;
    background-color: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    animation: modal-appear 0.2s ease-out;
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close-modal {
    position: absolute;
    top: 24px;
    left: 24px;
    background: none;
    border: none;
    color: #222222;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.close-modal:hover {
    background-color: #F7F7F7;
}

.review-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding-top: 20px;
}

.edit-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #222222;
}

/* Emoji Container */
.emoji-container {
    margin-bottom: 4px;
    font-size: 3rem;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Rating Text */
.rating-text {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #222222;
}

/* Star Rating Styles */
.star-rating {
    display: flex;
    justify-content: center;
    gap: 0.25rem;
    margin: 1rem 0 1.5rem;
}

.star-btn {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: #DDDDDD;
    transition: all 0.2s;
}

.star-btn.active {
    color: #FF385C;
}

.star-btn:hover {
    transform: scale(1.05);
}

.star-icon {
    width: 2rem;
    height: 2rem;
}

/* Review Comment */
.review-comment-container {
    width: 100%;
    margin-bottom: 20px;
}

.review-comment-input {
    width: 100%;
    height: 120px;
    padding: 12px 16px;
    border: 1px solid #DDDDDD;
    border-radius: 8px;
    resize: none;
    font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
    font-size: 16px;
}

.review-comment-input:focus {
    outline: none;
    border-color: #222222;
}

/* Submit Button */
.submit-review-btn {
    background-color: #FF385C;
    color: white;
    font-weight: 500;
    font-size: 16px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    width: 100%;
    font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}

.submit-review-btn:hover {
    background-color: #E31C5F;
}

.submit-review-btn:disabled {
    background-color: #EBEBEB;
    color: #767676;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 743px) {
    .review-modal-content {
        padding: 48px 16px 16px;
    }

    .close-modal {
        top: 16px;
        left: 16px;
    }

    .edit-title {
        font-size: 20px;
    }
}