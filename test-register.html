<!DOCTYPE html>
<html>
<head>
    <title>Test Registration</title>
    <script>
        // Function to make a direct fetch request with proper error handling
        async function testRegister() {
            const email = document.getElementById('email').value;
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const password = document.getElementById('password').value;

            const payload = {
                email,
                firstName,
                lastName,
                password
            };

            document.getElementById('result').textContent = 'Sending request...';

            try {
                // Create a blob with the JSON data
                const blob = new Blob([JSON.stringify(payload)], {type: 'application/json'});

                // Create a FormData object
                const formData = new FormData();
                formData.append('json', blob);

                // Use fetch with proper JSON handling
                const response = await fetch('https://localhost:7228/api/Auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });

                const text = await response.text();

                if (response.ok) {
                    try {
                        const data = JSON.parse(text);
                        document.getElementById('result').textContent = 'Success! Response: ' + JSON.stringify(data, null, 2);
                    } catch (e) {
                        document.getElementById('result').textContent = 'Success, but could not parse response: ' + text;
                    }
                } else {
                    document.getElementById('result').textContent = 'Error: Status ' + response.status + '\n' + text;
                }
            } catch (error) {
                document.getElementById('result').textContent = 'Network error: ' + error.message;
            }
        }
    </script>
</head>
<body>
    <h1>Test Registration</h1>
    <form onsubmit="event.preventDefault(); testRegister();">
        <div>
            <label for="email">Email:</label>
            <input type="email" id="email" required>
        </div>
        <div>
            <label for="firstName">First Name:</label>
            <input type="text" id="firstName" required>
        </div>
        <div>
            <label for="lastName">Last Name:</label>
            <input type="text" id="lastName" required>
        </div>
        <div>
            <label for="password">Password:</label>
            <input type="password" id="password" required>
        </div>
        <button type="submit">Test Register</button>
    </form>
    <h2>Result:</h2>
    <pre id="result"></pre>
</body>
</html>
