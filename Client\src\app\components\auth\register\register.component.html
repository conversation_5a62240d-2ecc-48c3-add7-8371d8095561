<app-main-navbar></app-main-navbar>
<div class="register-container">
  <div class="register-card">
    <h1>Create an account</h1>
    <h2>Welcome to Airbnb</h2>

    <!-- Success message -->
    <div *ngIf="registrationSuccess" class="alert alert-success">
      Registration successful! Redirecting to login...
    </div>

    <!-- Registration form -->
    <form *ngIf="!registrationSuccess" [formGroup]="registerForm" (ngSubmit)="onSubmit()">

      <div class="form-group">
        <label>First Name</label>
        <input
          type="text"
          formControlName="firstName"
          class="form-control"
          [class.is-invalid]="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched"
          placeholder="First Name">
        <app-validation-error
          [errors]="registerForm.get('firstName')!.errors"
          [fieldName]="'First name'"
          *ngIf="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched">
        </app-validation-error>
      </div>

      <div class="form-group">
        <label>Last Name</label>
        <input
          type="text"
          formControlName="lastName"
          class="form-control"
          [class.is-invalid]="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched"
          placeholder="Last Name">
        <app-validation-error
          [errors]="registerForm.get('lastName')!.errors"
          [fieldName]="'Last name'"
          *ngIf="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched">
        </app-validation-error>
      </div>

      <div class="form-group">
        <label>Email</label>
        <input
          type="email"
          formControlName="email"
          class="form-control"
          [class.is-invalid]="registerForm.get('email')?.invalid && registerForm.get('email')?.touched"
          placeholder="Email">
        <app-validation-error
          [errors]="registerForm.get('email')!.errors"
          [fieldName]="'Email'"
          *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched">
        </app-validation-error>
      </div>

      <div class="form-group">
        <label>Password</label>
        <div class="password-input-container">
          <input
            [type]="showPassword ? 'text' : 'password'"
            formControlName="password"
            class="form-control"
            [class.is-invalid]="registerForm.get('password')?.invalid && registerForm.get('password')?.touched"
            placeholder="Enter your password">
          <button
            type="button"
            class="password-toggle-btn"
            (click)="togglePasswordVisibility()"
            [attr.aria-label]="showPassword ? 'Hide password' : 'Show password'">
            <i class="fa" [ngClass]="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
          </button>
        </div>
        <app-validation-error
          [errors]="registerForm.get('password')!.errors"
          [fieldName]="'Password'"
          *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched">
        </app-validation-error>
      </div>

      <div class="form-group">
        <label>Confirm Password</label>
        <div class="password-input-container">
          <input
            [type]="showPassword ? 'text' : 'password'"
            formControlName="confirmPassword"
            class="form-control"
            [class.is-invalid]="(registerForm.get('confirmPassword')?.invalid || registerForm.errors?.['passwordMismatch']) && registerForm.get('confirmPassword')?.touched"
            placeholder="Confirm your password">
        </div>
        <app-validation-error
          [errors]="registerForm.get('confirmPassword')!.errors"
          [fieldName]="'Confirm password'"
          *ngIf="registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched">
        </app-validation-error>
        <app-validation-error
          [errors]="registerForm.errors"
          *ngIf="registerForm.errors?.['passwordMismatch'] && registerForm.get('confirmPassword')?.touched">
        </app-validation-error>
      </div>

      <!-- Error message -->
      <div *ngIf="errorMessage" class="alert alert-danger">
        {{ errorMessage }}
      </div>

      <!-- Submit button with loading state -->
      <button type="submit" class="btn-primary" [disabled]="isLoading">
        <span *ngIf="isLoading">
          <i class="fa fa-spinner fa-spin"></i> Processing...
        </span>
        <span *ngIf="!isLoading">Continue</span>
      </button>
    </form>

    <div class="divider">
      <span>or</span>
    </div>

    <!-- Google sign-in button with loading state -->
    <button class="btn-social" (click)="signInWithGoogle()" [disabled]="isLoading">
      <span *ngIf="isLoading && !registrationSuccess">
        <i class="fa fa-spinner fa-spin"></i> Processing...
      </span>
      <span *ngIf="!isLoading || registrationSuccess">
        <i class="fab fa-google"></i> Continue with Google
      </span>
    </button>

    <!-- Facebook sign-in button (disabled for now) -->
    <button class="btn-social" disabled>
      <i class="fab fa-facebook"></i> Continue with Facebook
    </button>

    <div class="login-link">
      Already have an account? <a (click)="goToLogin()">Log in</a>
    </div>
  </div>
</div>