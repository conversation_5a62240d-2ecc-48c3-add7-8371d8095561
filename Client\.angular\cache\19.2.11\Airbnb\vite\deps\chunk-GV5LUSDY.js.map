{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/css-pixel-value-C_HEqLhI.mjs"], "sourcesContent": ["/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n  if (value == null) {\n    return '';\n  }\n  return typeof value === 'string' ? value : `${value}px`;\n}\nexport { coerceCssPixelValue as c };\n"], "mappings": ";AACA,SAAS,oBAAoB,OAAO;AAClC,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,SAAO,OAAO,UAAU,WAAW,QAAQ,GAAG,KAAK;AACrD;", "names": []}