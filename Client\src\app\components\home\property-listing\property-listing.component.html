<app-header class="header" (searchPerformed)="handleSearchResults($event)"></app-header>


<div class="filters-bar">
  <div class="filters-wrapper">
    <div class="filter-buttons">
      <!-- Loading state -->
      <div *ngIf="isLoading">Loading categories...</div>

      <!-- Filter buttons -->
      <ng-container *ngIf="!isLoading && categories.length > 0">
        <!-- All homes button -->
        <button
          class="filter-button"
          [class.active]="activeFilter === 'All homes'"
          (click)="applyFilter('All homes')"
        >
          <span class="material-icons">home</span>
          <span>All homes</span>
        </button>

        <!-- Category buttons -->
        <button
          class="filter-button"
          *ngFor="let category of categories"
          [class.active]="activeFilter === category.name"
          (click)="applyFilter(category)"
        >
          <img
            *ngIf="category.iconUrl"
            [src]="category.iconUrl"
            alt="{{ category.name }} icon"
            class="category-icon"
            style="width: 16px; height: 16px;"
          />
          <span class="material-icons" *ngIf="!category.iconUrl">apartment</span>
          <span>{{ category.name }}</span>
        </button>

        <!-- Prices button -->
        <button
          class="filter-button"
          [class.active]="activeFilter === 'Prices' || activeFilter.includes('$')"
          (click)="applyFilter('Prices')"
        >
          <span class="material-icons">attach_money</span>
          <span>{{ activeFilter === 'Prices' || activeFilter === 'All homes' ? 'Prices' : activeFilter }}</span>
        </button>
        
        <!-- Reset filters button (only visible when filters are applied) -->
        <button 
          *ngIf="activeFilter !== 'All homes'"
          class="filter-button reset-button"
          (click)="resetFilters()"
        >
          <span class="material-icons">close</span>
          <span>Clear filters</span>
        </button>
      </ng-container>

      <!-- No categories fallback -->
      <div *ngIf="!isLoading && categories.length === 0">
        No categories available
      </div>
    </div>
  </div>
</div>
<div class="container">
  
  
  <div class="property-grid">
    <div class="property-card" *ngFor="let property of properties" (click)="goToProperty(property.id)">
      <!-- Image Carousel -->
      <div class="image-container">
        <img [src]="getPropertyImage(property)" (error)="handleImageError(property)" alt="{{ property.title }}"
          class="property-image" />

        <!-- Navigation Arrows -->
        <button class="arrow left-arrow" (click)="prevImage(property.id, $event)"
          *ngIf="(property.images?.length ?? 0) > 1">
          <span class="material-icons">chevron_left</span>
        </button>
        <button class="arrow right-arrow" (click)="nextImage(property.id, $event)"
          *ngIf="(property.images?.length ?? 0) > 1">
          <span class="material-icons">chevron_right</span>
        </button>

        <!-- Guest Favorite Badge -->
        <span class="badge" *ngIf="property.isGuestFavorite">New</span>

        <!-- Favorite Heart Icon -->
        <button class="favorite" (click)="toggleFavorite(property.id, $event)">
          <span class="material-icons" [class.favorited]="isFavorite(property.id)">
            {{ isFavorite(property.id) ? 'favorite' : 'favorite_border' }}
          </span>
        </button>

        <!-- Image Dots -->
        <div class="image-dots" *ngIf="(property.images?.length ?? 0) > 1">
          <span *ngFor="let image of property.images; let i = index"
            [class.active]="i === currentImageIndices[property.id]"></span>
        </div>
      </div>

      <!-- Property Info -->
      <div class="property-info">
        <div class="property-location-container">
          <h3 class="property-location">{{ property.city }}, {{ property.country }}</h3>
          <div class="property-rating" *ngIf="property.averageRating">
            <span class="material-icons">star</span>
            <span>{{ property.averageRating | number:'1.1-1' }}</span>
            <span *ngIf="property.isGuestFavorite" class="new-tag">New</span>
          </div>
        </div>
        
        <div class="property-distance">
          <span>{{property.title}}</span>
        </div>
        
        <!-- <div class="property-dates">
          <span>{{ generateRandomMonth() }} {{ generateRandomDateRange() }}</span>
        </div> -->
        
        <div class="property-price">
          <span class="price">${{ property.pricePerNight | number }}</span>
          <span class="per-night">night</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination Controls -->
  <div class="pagination" *ngIf="totalPages > 1">
    <button class="pagination-button" [disabled]="currentPage === 1" (click)="goToPage(currentPage - 1)">
      <span class="material-icons">chevron_left</span>
    </button>
    <div class="pagination-numbers">
      <button *ngFor="let page of getPageNumbers()" 
              class="pagination-number" 
              [class.active]="page === currentPage"
              (click)="goToPage(page)">
        {{ page }}
      </button>
    </div>
    <button class="pagination-button" [disabled]="currentPage === totalPages" (click)="goToPage(currentPage + 1)">
      <span class="material-icons">chevron_right</span>
    </button>
  </div>

  <!-- Loading Indicator -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner"></div>
    <p>Loading properties...</p>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!isLoading && properties.length === 0">
    <span class="material-icons">search_off</span>
    <h3>No properties found</h3>
    <p>Try adjusting your search or filters</p>
  </div>
</div>

<!-- Price filter dialog -->
<div class="price-filter-dialog" *ngIf="showPriceFilter">
  <div class="price-filter-content">
    <h3>Filter by price</h3>
    <div class="price-inputs">
      <div class="price-input-group">
        <label for="minPrice">Minimum price</label>
        <div class="input-with-icon">
          <span class="currency-symbol">$</span>
          <input type="number" id="minPrice" #minPriceInput placeholder="Min price" min="0">
        </div>
      </div>
      <div class="price-input-group">
        <label for="maxPrice">Maximum price</label>
        <div class="input-with-icon">
          <span class="currency-symbol">$</span>
          <input type="number" id="maxPrice" #maxPriceInput placeholder="Max price" min="0">
        </div>
      </div>
    </div>
    <div class="price-filter-actions">
      <button class="cancel-button" (click)="showPriceFilter = false">Cancel</button>
      <button class="apply-button" (click)="applyPriceFilter(minPriceInput.value ? +minPriceInput.value : null, maxPriceInput.value ? +maxPriceInput.value : null)">Apply</button>
    </div>
  </div>
</div>

<app-sticky-nav class="sticky-nav"></app-sticky-nav>