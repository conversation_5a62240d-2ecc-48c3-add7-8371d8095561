.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px 24px;
  background: linear-gradient(to bottom, #f8f9fa, #ffffff);
  min-height: 100vh;
}

/* Empty State Styles */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  text-align: center;
  background-color: #fff;
  border-radius: 12px;
  margin: 20px 0;
}

.empty-state-content {
  max-width: 400px;
  padding: 40px 20px;
}

.empty-state i {
  font-size: 48px;
  color: #717171;
  margin-bottom: 24px;
}

.empty-state h2 {
  font-size: 24px;
  font-weight: 600;
  color: #222;
  margin-bottom: 12px;
}

.empty-state p {
  font-size: 16px;
  color: #717171;
  margin-bottom: 24px;
}

.empty-state .btn-primary {
  background-color: #FF385C;
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.empty-state .btn-primary:hover {
  background-color: #E31C5F;
}

.property-grid {
  margin-top: 20px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding-bottom: 40px;
  position: relative;
}

.property-card {
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  max-width: 100%;
  position: relative;
}

.property-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.image-container {
  position: relative;
  width: 100%;
  padding-top: 66.67%; /* 3:2 aspect ratio */
  border-radius: 12px 12px 0 0;
  overflow: hidden;
}

.property-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.property-card:hover .property-image {
  transform: scale(1.08);
}

/* Status Badge */
.status-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background-color: #4CAF50;
  color: white;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.status-badge.status-pending {
  background-color: #FFA000;
}

.arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.95);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #222;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.2s, transform 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.property-card:hover .arrow {
  opacity: 1;
}

.arrow:hover {
  transform: translateY(-50%) scale(1.1);
}

.left-arrow {
  left: 10px;
}

.right-arrow {
  right: 10px;
}

.image-dots {
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 5px;
  z-index: 2;
}

.image-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
  opacity: 0.6;
  transition: opacity 0.2s, transform 0.2s;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.image-dots span.active {
  opacity: 1;
  transform: scale(1.3);
}

.property-info {
  padding: 14px 12px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
  background-color: #ffffff;
  border-radius: 0 0 12px 12px;
}

.property-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #222;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.btn-light {
  background-color: white;
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  z-index: 10;
  border: none;
}

.btn-light:hover {
  background-color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-light .material-icons {
  font-size: 20px;
  color: #222;
}

.dropdown-menu {
  position: absolute;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 8px;
  padding: 8px;
  min-width: 150px;
  z-index: 1000;
  background: white;
  right: 0;
  top: 100%;
  margin-top: 5px;
  transform-origin: top right;
}

.dropdown-item {
  padding: 10px 15px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0px;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  transform: translateX(5px);
}

.dropdown-item.text-danger:hover {
  background-color: #fff5f5;
  color: #dc3545 !important;
}

.dropdown-item .material-icons {
  font-size: 18px;
}

/* Loading spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
  color: #FF385C;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .property-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .property-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .container {
    padding: 12px;
  }
  
  .property-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .empty-state {
    min-height: 300px;
  }

  .empty-state-content {
    padding: 20px;
  }

  .empty-state h2 {
    font-size: 20px;
  }

  .empty-state p {
    font-size: 14px;
  }
} 