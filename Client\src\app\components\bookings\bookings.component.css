/* Container and general styles */
.container {
  max-width: 1120px;
  padding: 1.5rem;
  margin: 0 auto;
}

h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #222222;
  margin-bottom: 0.25rem;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}

p {
  color: #717171;
  margin-top: 0.25rem;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
  font-weight: 400;
}

/* Grid layout */
.grid {
  display: grid;
  gap: 1rem;
}

@media (min-width: 550px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 950px) {
  .grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1128px) {
  .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Booking card */
.booking-card {
  border-radius: 12px;
  overflow: hidden;
  background-color: #FFFFFF;
  border: 1px solid #DDDDDD;
  transition: box-shadow 0.2s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.booking-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* Image container */
.booking-card .relative {
  position: relative;
  height: 0;
  padding-bottom: 66.67%;
  overflow: hidden;
}

.booking-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

/* Cancel button - Airbnb style */
.booking-card .cancel-btn {
  position: absolute;
  right: 0.75rem;
  top: 0.75rem;
  background-color: #FF385C;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  border: none;
  z-index: 10;
  cursor: pointer;
  transition: background-color 0.2s;
  font-weight: 500;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}

.cancel-btn:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.cancel-btn svg {
  height: 1rem;
  width: 1rem;
  margin-right: 0.25rem;
}

/* Content area */
.booking-card .mt-3 {
  padding: 1rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

/* Text styling - Airbnb font and style */
.booking-card h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #222222;
  margin-top: 0;
  margin-bottom: 0.25rem;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.3;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}

.text-gray-700 {
  color: #717171;
}

.text-gray-600 {
  color: #717171;
}

.font-medium {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.mt-1 {
  margin-top: 0.25rem;
}

/* Price display */
.booking-card .font-semibold.text-gray-900 {
  margin-top: auto;
  padding-top: 0.5rem;
  font-size: 1rem;
  color: #222222;
  font-weight: 600;
}

/* Loading spinner - Airbnb uses a simple spinner */
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25rem solid #FF385C;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

.text-primary {
  color: #FF385C;
}

.text-center {
  text-align: center;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

/* Empty state */
.btn-primary {
  background-color: #FF385C;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  display: inline-block;
  margin-top: 1rem;
  font-weight: 500;
  transition: background-color 0.2s;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
  border: none;
}

.btn-primary:hover {
  background-color: #FF385C;
}

/* Error message */
.alert-danger {
  background-color: #FFF8F6;
  color: #FF385C;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  font-weight: 500;
  border: 1px solid #FFAFA3;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}

/* Image placeholder */
.bg-gray-200 {
  background-color: #F7F7F7;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-gray-400 {
  color: #717171;
}

/* Location and date formatting */
.booking-card .text-gray-700 {
  margin-bottom: 0.25rem;
}

/* Review button styling - Airbnb style */
.review-btn {
  position: absolute;
  right: 0.75rem;
  top: 0.75rem;
  background-color: #FF385C;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  border: none;
  z-index: 10;
  cursor: pointer;
  transition: background-color 0.2s;
  font-weight: 500;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}

.review-btn:hover {
  background-color: #FF385C;
}

/* Reviewed badge styling */
.reviewed-badge {
  position: absolute;
  right: 0.75rem;
  top: 0.75rem;
  background-color: #155724;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  z-index: 10;
  font-weight: 500;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}

/* Review Modal - Airbnb style */
.review-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}

.review-modal-content {
  position: relative;
  width: 90%;
  max-width: 568px;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  animation: modal-appear 0.2s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.close-modal {
  position: absolute;
  top: 24px;
  left: 24px;
  background: none;
  border: none;
  color: #222222;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-modal:hover {
  background-color: #F7F7F7;
}

.review-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding-top: 20px;
}

/* Emoji Container */
.emoji-container {
  margin-bottom: 4px;
  font-size: 3rem;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Rating Text */
.rating-text {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #222222;
}

/* Star Rating Styles */
.star-rating {
  display: flex;
  justify-content: center;
  gap: 0.25rem;
  margin: 1rem 0 1.5rem;
}

.star-btn {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: #DDDDDD;
  transition: all 0.2s;
  
  &.active {
    color: #FF385C;
  }
  
  &:hover {
    transform: scale(1.05);
  }
}

.star-icon {
  width: 2rem;
  height: 2rem;
}

/* Review Comment */
.review-comment-container {
  width: 100%;
  margin-bottom: 20px;
}

.review-comment-input {
  width: 100%;
  height: 120px;
  padding: 12px 16px;
  border: 1px solid #DDDDDD;
  border-radius: 8px;
  resize: none;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
  font-size: 16px;
}

.review-comment-input:focus {
  outline: none;
  border-color: #222222;
}

/* Submit Button */
.submit-review-btn {
  background-color: #FF385C;
  color: white;
  font-weight: 500;
  font-size: 16px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}

.submit-review-btn:hover {
  background-color: #FF385C;
}

.submit-review-btn:disabled {
  background-color: #EBEBEB;
  color: #767676;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 743px) {
  .container {
    padding: 1rem;
  }
  
  h1 {
    font-size: 1.5rem;
  }
  
  .close-modal {
    top: 16px;
    left: 16px;
  }
  
  .review-modal-content {
    padding: 48px 16px 16px;
  }
}/* Toast Notification Styles */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
}

.toast {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 250px;
  max-width: 450px;
  animation: slideIn 0.3s ease-out forwards;
}

.toast-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.toast-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.toast-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.toast-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
}

.toast-icon .icon {
  width: 24px;
  height: 24px;
}

.toast-message {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Add these new styles for the cancellation modal */
.cancellation-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cancellation-modal-content {
  background-color: white;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.cancellation-content {
  padding: 24px;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: #222;
}

.booking-summary {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.booking-summary h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.policy-info {
  margin-bottom: 24px;
}

.policy-info h4 {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 8px;
  color: #333;
}

.refund-details {
  background-color: #f7f7f7;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.highlight {
  font-weight: 600;
  color: #222;
}

.refund-amount {
  margin-top: 8px;
  font-size: 1.1rem;
}

.money {
  font-weight: 600;
  color: #008489;
}

.no-refund {
  display: flex;
  align-items: center;
  color: #e31c5f;
  font-weight: 600;
  margin-bottom: 8px;
}

.confirmation-question {
  margin-top: 24px;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  justify-content: center;
}

.cancel-action-btn {
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-action-btn.primary {
  background-color: #e31c5f;
  color: white;
  border: none;
}

.cancel-action-btn.primary:hover {
  background-color: #d01155;
}

.cancel-action-btn.secondary {
  background-color: white;
  color: #222;
  border: 1px solid #ddd;
}

.cancel-action-btn.secondary:hover {
  background-color: #f7f7f7;
}

@media (max-width: 640px) {
  .action-buttons {
    flex-direction: column;
  }
}

.policy-error {
  background-color: #fff4f4;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #e31c5f;
  margin-bottom: 16px;
}

.policy-error h4 {
  color: #e31c5f;
  margin-bottom: 8px;
}

.policy-error-note {
  display: flex;
  align-items: center;
  margin-top: 12px;
  font-style: italic;
  color: #666;
}

/* Cancellation policy info box - updated style */
.policy-info-box {
  background-color: #F7F7F7;
  border-radius: 12px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  position: relative;
  border: 1px solid #EBEBEB;
}

.policy-info-box h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #222222;
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-family: "Circular", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
}

.policy-info-box p {
  color: #484848;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.info-message {
  display: flex;
  align-items: flex-start;
  padding: 0.75rem;
  background-color: rgba(0, 132, 137, 0.08);
  border-radius: 8px;
  margin-top: 0.5rem;
}

.info-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
  flex-shrink: 0;
  color: #008489;
}

.info-message span {
  font-size: 0.875rem;
  color: #484848;
  line-height: 1.4;
}

/* Add pagination styles at the end of the file */

/* Pagination Container */
.pagination-container {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #eaeaea;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not([disabled]) {
  background-color: #f7f7f7;
  border-color: #ccc;
}

.pagination-btn[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn svg {
  width: 20px;
  height: 20px;
  color: #484848;
}

.pagination-info {
  font-size: 16px;
  color: #484848;
  font-weight: 500;
}

.pagination-details {
  margin-top: 10px;
  font-size: 14px;
  color: #717171;
}

/* Add styling for the empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-state svg {
  color: #888;
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.empty-state p {
  font-size: 1rem;
  color: #666;
  margin-bottom: 1.5rem;
  max-width: 400px;
}
