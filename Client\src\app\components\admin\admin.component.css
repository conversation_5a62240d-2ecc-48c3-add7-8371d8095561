.admin-container {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
  position: relative;
  max-width: 100%;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
  position: fixed;
  top: 5px;
  left: 20px;
  z-index: 1000;
  width: 40px;
  height: 40px;
  background-color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 30px;
}

.sidebar-toggle:hover {
  background-color: #e8eaf6;
  transform: scale(1.05);
}

.sidebar-toggle i {
  font-size: 1.2rem;
  color: #3f51b5;
}

/* Logo Section */
.logo-section {
  margin-top: 10px;
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: all 0.3s ease;
}

.airbnb-icon {
  width: 30px;
  height: 30px;
  margin-right: 8px;
  transition: all 0.3s ease;
}

/* Sidebar Styles */
.sidebar {
  width: 230px;
  background-color: #ffffff;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  padding: 0;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 100;
}

.sidebar.collapsed {
  width: 80px;
}

.admin-profile {
  display: flex;
  align-items: center;
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #eee;
  flex-direction: column;
  margin-bottom: 5px;
  margin-top: 40px;
}

.sidebar.collapsed .admin-profile {
  padding: 15px 10px;
  margin-top: 35px;
  margin-bottom: 5px;
}

.airbnb-icon {
  width: 30px;
  height: 30px;
  transition: all 0.3s ease;
}

.sidebar.collapsed .airbnb-icon {
  width: 25px;
  height: 25px;
  margin: 0;
}

.admin-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  margin: 15px 0;
  transition: all 0.3s ease;
}
.material-icons profile-icon{
  margin-top: 20px;
  font-size: 2rem;
  color: #333;
}
.admin-name {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.sidebar-nav {
  padding: 20px 0;
  flex: 1;
  margin-top: 5px;
}

.nav-section {
  margin-bottom: 10px;
}

.nav-item {
  padding: 12px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #666;
  transition: all 0.3s ease;
  position: relative;
}

.sidebar.collapsed .nav-item {
  padding: 12px 0;
  justify-content: center;
}

.nav-item:hover {
  background-color: #e8eaf6;
  color: #3f51b5;
}

.nav-item i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.sidebar.collapsed .nav-item i {
  margin-right: 0;
  font-size: 1.2rem;
}

.nav-submenu {
  background-color: #f8f9fa;
  padding-left: 20px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.sidebar.collapsed .nav-submenu {
  padding-left: 0;
}

.nav-submenu.show {
  max-height: 500px;
}

.nav-submenu .nav-item {
  padding: 10px 20px;
  font-size: 0.9rem;
}

.sidebar.collapsed .nav-submenu .nav-item {
  padding: 10px 0;
}

/* Main Content Styles */
.main-content {
  width: 1024px;
  flex: 1;
  margin-left: 220px;
  padding: 10px;
  transition: all 0.3s ease;
}

.main-content.expanded {
  margin-left: 90px;
  width: 100px;

}

.content-section {
  background-color: #ffffff;
  width: 100%;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-section h2 {
  margin-bottom: 20px;
  color: #3f51b5;
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: 0.5px;
}

/* Active State Styles */
.nav-item.active {
  background-color: #e8eaf6;
  color: #3f51b5;
  font-weight: 500;
}

.nav-item.active i {
  color: #3f51b5;
}

/* Logout Section */
.logout-section {
  margin-top: auto;
  padding: 20px;
  border-top: 1px solid #eee;
}

.sidebar.collapsed .logout-section {
  padding: 15px 0;
}

.logout-button {
  width: 100%;
  padding: 12px 20px;
  background-color: #3f51b5;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.sidebar.collapsed .logout-button {
  padding: 12px 0;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin: 0 auto;
}

.logout-button:hover {
  background-color: #303f9f;
}

.logout-button i {
  margin-right: 10px;
}

.sidebar.collapsed .logout-button i {
  margin-right: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    /* justify-content: j; */
    padding: 10px;
  }

  .sidebar.collapsed {
    display: flex;
    flex-direction: row;
    width: 100%;
  }

  .admin-profile {
    margin: 0;
    padding: 10px;
    border-bottom: none;
    border-right: 1px solid #eee;
    flex-direction: row;
    gap: 10px;
  }

  .admin-avatar {
    width: 40px;
    height: 40px;
    margin: 0;
  }

  .sidebar-nav {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0;
    margin: 0;
    gap: 15px;
    flex: 1;
    justify-content: center;
  }

  .nav-section {
    margin: 0;
  }

  .nav-item {
    padding: 8px 15px;
  }

  .logout-section {
    margin: 0;
    padding: 10px;
    border-top: none;
    border-left: 1px solid #eee;
  }

  .main-content {
    margin-left: 0;
  }

  .main-content.expanded {
    margin-left: 0;
  }

  .admin-container {
    flex-direction: column;
  }
}

/* Tooltip for collapsed sidebar */
.nav-item {
  position: relative;
}

.sidebar.collapsed .nav-item:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  white-space: nowrap;
  z-index: 1000;
  margin-left: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.sidebar.collapsed .nav-item:hover::before {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: transparent #333 transparent transparent;
  margin-left: 0;
  z-index: 1000;
}

.admin-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Loading and Error States */
.loading-spinner {
  text-align: center;
  padding: 20px;
  font-size: 1.2em;
  color: #666;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 10px;
  margin: 10px 0;
  border-radius: 4px;
  text-align: center;
}

/* Navigation Tabs */
.admin-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 10px;
}

.admin-tabs button {
  padding: 10px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 1em;
  color: #666;
  transition: all 0.3s ease;
}

.admin-tabs button.active {
  color: #2196f3;
  border-bottom: 2px solid #2196f3;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-buttons {
  display: flex;
  gap: 10px;
}

.filter-buttons button {
  padding: 8px 16px;
  border: 1px solid #2196f3;
  background: white;
  color: #2196f3;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-buttons button:hover {
  background: #2196f3;
  color: white;
}

/* Grid Layouts */
.hosts-grid,
.guests-grid,
.properties-grid,
.bookings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* Cards */
.host-card,
.guest-card,
.property-card,
.booking-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 20px;
  transition: transform 0.3s ease;
}

.host-card:hover,
.guest-card:hover,
.property-card:hover,
.booking-card:hover {
  transform: translateY(-5px);
}


/* Profile Pictures */
.profile-picture {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 10px;
}

/* Card Information */
.host-info,
.guest-info,
.property-info,
.booking-info {
  h3 {
    margin: 0 0 10px 0;
    color: #333;
  }

  p {
    margin: 5px 0;
    color: #666;
  }
}

/* Actions */
.actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.actions button.blocked {
  background-color: #f44336;
  color: white;
}

.suspend-btn{
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f44336;
  color: white;
}

.actions button:not(.blocked) {
  background-color: #4caf50;
  color: white;
}

.actions select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-tabs {
    flex-wrap: wrap;
  }

  .admin-tabs button {
    flex: 1 1 calc(50% - 10px);
  }

  .hosts-grid,
  .guests-grid,
  .properties-grid,
  .bookings-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: 10px;
  }

  .filter-buttons {
    width: 100%;
    justify-content: center;
  }
}

/* Status Colors */
.status-pending {
  color: #ff9800;
}

.status-confirmed {
  color: #4caf50;
}

.status-cancelled {
  color: #f44336;
}

.status-completed {
  color: #2196f3;
}

/* Table Styles */
.table-responsive {
  overflow-x: auto;
  margin: 20px 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
}

.data-table th,
.data-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
  font-size: 1rem;
}

.data-table th {
  background-color: #e8eaf6;
  font-weight: 700;
  color: #3f51b5;
  letter-spacing: 0.5px;
}

.data-table tbody tr:hover {
  background-color: #f5f5f5;
}

.profile-picture-small {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

/* Button Styles */
.block-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.block-btn:hover {
  background-color: #d32f2f;
}

.unblock-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.unblock-btn:hover {
  background-color: #388e3c;
}

.approve-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 5px;
  transition: background-color 0.2s;
}

.approve-btn:hover {
  background-color: #388e3c;
}

.reject-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.reject-btn:hover {
  background-color: #d32f2f;
}

.status-select {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
}

.status-select:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Content Section Styles */
.content-section {
  padding: 20px;
}

.content-section h2 {
  margin-bottom: 20px;
  color: #3f51b5;
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: 0.5px;
}

/* Users/Properties Analytics Table Section */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.95em;
  font-weight: 600;
  background: #f8f9fa;
  color: #888;
  border: 1px solid #eee;
}

.status-verified {
  background: #e8eaf6;
  color: #3f51b5;
  border-color: #c5cae9;
}

.status-unverified, .status-suspended {
  background: #ffebee;
  color: #f44336;
  border-color: #ffcdd2;
}

.verify-btn{
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.95em;
  font-weight: 600;
  background-color: #3f51b5;
  color: white;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

/* --- Analytics Section --- */
.analytics-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
  margin-top: 20px;
}

.summary-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: flex-start;
}

.summary-cards .card {
  flex: 1 1 180px;
  min-width: 180px;
  max-width: 240px;
  background: linear-gradient(135deg, #ffffff 60%, #e8eaf6 100%);
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(63, 81, 181, 0.07);
  border: 1.5px solid rgba(63, 81, 181, 0.15);
  padding: 24px 20px 20px 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  overflow: hidden;
}

.summary-cards .card:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow: 0 8px 32px rgba(63, 81, 181, 0.13);
  background: linear-gradient(135deg, #e8eaf6 60%, #ffffff 100%);
}

.summary-cards .card h3 {
  font-size: 1.1rem;
  color: #3f51b5;
  margin-bottom: 8px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.summary-cards .card p {
  font-size: 2.1rem;
  font-weight: 700;
  color: #222;
  margin: 0;
  letter-spacing: 1px;
}

.summary-cards .card .card-icon {
  position: absolute;
  top: 12px;
  right: 12px;
  font-size: 2.7rem;
  color: #3f51b5 !important;
  opacity: 0.18;
  pointer-events: none;
  z-index: 0;
}

.summary-cards .card .highlight {
  color: #303f9f;
  font-size: 1.2rem;
  font-weight: 600;
}

.analytics-container .filter-section {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analytics-container .filter-section label {
  color: #3f51b5;
  font-weight: 500;
  font-size: 0.95rem;
}

.analytics-container .filter-section select,
.analytics-container .filter-section input {
  padding: 8px 15px;
  border: 1.5px solid rgba(63, 81, 181, 0.15);
  border-radius: 20px;
  background-color: #f5f5f5;
  color: #484848;
  font-size: 0.9rem;
  outline: none;
  transition: all 0.3s ease;
  min-width: 150px;
}

.analytics-container .filter-section select:focus,
.analytics-container .filter-section input:focus {
  border-color: #3f51b5;
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.15);
}

.analytics-container .filter-section select:hover,
.analytics-container .filter-section input:hover {
  border-color: #3f51b5;
}

.analytics-container .filter-section button {
  padding: 8px 20px;
  background-color: #3f51b5;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.analytics-container .filter-section button:hover {
  background-color: #303f9f;
  transform: translateY(-1px);
}

/* Responsive styles for filter section */
@media (max-width: 768px) {
  .analytics-container .filter-section {
    flex-wrap: wrap;
    gap: 10px;
    padding: 12px;
  }

  .analytics-container .filter-section select,
  .analytics-container .filter-section input {
    flex: 1;
    min-width: 120px;
  }

  .analytics-container .filter-section button {
    width: 100%;
  }
}

.charts-section {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  justify-content: flex-start;
}

.charts-section .chart {
  flex: 1 1 350px;
  min-width: 320px;
  max-width: 480px;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 2px 16px rgba(63, 81, 181, 0.07);
  border: 1.5px solid rgba(63, 81, 181, 0.15);
  padding: 24px 18px 18px 18px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.2s;
}

.charts-section .chart:hover {
  background: linear-gradient(135deg, #ffffff 60%, #e8eaf6 100%);
  box-shadow: 0 8px 32px rgba(63, 81, 181, 0.13);
}

.charts-section .chart h3 {
  font-size: 1.1rem;
  color: #3f51b5;
  margin-bottom: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.charts-section canvas {
  width: 100% !important;
  max-width: 400px;
  height: 260px !important;
  margin: 0 auto;
  background: transparent;
  border-radius: 8px;
  transition: box-shadow 0.2s;
}

/* Top Items Section */
.top-items-section {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: flex-start;
}

.top-item {
  flex: 1 1 300px;
  min-width: 280px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 16px rgba(63, 81, 181, 0.07);
  border: 1.5px solid rgba(63, 81, 181, 0.15);
  padding: 20px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.top-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 24px rgba(63, 81, 181, 0.1);
}

.top-item h3 {
  font-size: 1.1rem;
  color: #3f51b5;
  margin-bottom: 12px;
  font-weight: 600;
}

.top-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.top-item ul li {
  padding: 8px 0;
  font-size: 0.95rem;
  color: #333;
  border-bottom: 1px solid #eee;
}

.top-item ul li:last-child {
  border-bottom: none;
}

/* Responsive for top items */
@media (max-width: 768px) {
  .top-items-section {
    flex-direction: column;
    gap: 10px;
  }
}

/* --- Analytics Dropdown --- */
.analytics-dropdown {
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.analytics-dropdown .dropdown-label {
  font-weight: 500;
  color: #3f51b5;
  margin-right: 6px;
}

.analytics-dropdown .dropdown-select {
  padding: 7px 14px;
  border-radius: 6px;
  border: 1.5px solid rgba(63, 81, 181, 0.15);
  background: #f5f5f5;
  font-size: 1rem;
  color: #3f51b5;
  outline: none;
  transition: border 0.2s;
}

.analytics-dropdown .dropdown-select:focus {
  border: 1.5px solid #3f51b5;
  background: #e8eaf6;
}

/* Dropdown arrow styling */
.analytics-dropdown .dropdown-select {
  appearance: none;
  -webkit-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,<svg width='16' height='16' fill='%233f51b5' xmlns='http://www.w3.org/2000/svg'><path d='M4 6l4 4 4-4'/></svg>");
  background-repeat: no-repeat;
  background-position: right 0.7em top 50%;
  background-size: 1.1em;
  padding-right: 2.2em;
}

/* Responsive for analytics dropdown */
@media (max-width: 700px) {
  .analytics-dropdown {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
}

/* Animation for summary cards */
@keyframes card-pop {
  0% { transform: scale(0.95); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}
.summary-cards .card {
  animation: card-pop 0.5s cubic-bezier(.68,-0.55,.27,1.55);
}

/* Responsive for analytics */
@media (max-width: 1100px) {
  .charts-section {
    flex-direction: column;
    gap: 24px;
  }
  .charts-section .chart {
    max-width: 100%;
    min-width: 0;
  }
  .summary-cards {
    flex-wrap: wrap;
    gap: 16px;
  }
}

@media (max-width: 700px) {
  .summary-cards {
    flex-direction: column;
    gap: 14px;
  }
  .charts-section .chart {
    padding: 14px 5px 10px 5px;
  }
  .charts-section canvas {
    height: 180px !important;
  }
}

/* Hover effect for chart cards */
.charts-section .chart:hover {
  background: linear-gradient(135deg, #ffffff 60%, #e8eaf6 100%);
  box-shadow: 0 8px 32px rgba(0,123,255,0.13);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .table-responsive {
    margin: 10px 0;
  }
  
  .data-table th,
  .data-table td {
    padding: 8px 5px;
  }
  
  .block-btn,
  .unblock-btn,
  .approve-btn,
  .reject-btn {
    padding: 4px 8px;
    font-size: 0.9rem;
  }
}
/* Property Image Styles for Summary Cards */
.summary-cards .card .property-image {
  width: 100%;
  max-width: 120px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid rgba(63, 81, 181, 0.15);
  transition: transform 0.2s;
}

.summary-cards .card:hover .property-image {
  transform: scale(1.05);
}

/* Property Image Styles for Top Items */
.top-item .property-image-small {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 6px;
  margin-right: 10px;
  vertical-align: middle;
  border: 1px solid rgba(63, 81, 181, 0.15);
  transition: transform 0.2s;
}

.top-item:hover .property-image-small {
  transform: scale(1.05);
}

.top-item ul li {
  display: flex;
  align-items: center;
  padding: 8px 0;
  font-size: 0.95rem;
  color: #333;
  border-bottom: 1px solid #eee;
}

.top-item .property-details {
  flex: 1;
}

/* Responsive Adjustments */
@media (max-width: 700px) {
  .summary-cards .card .property-image {
    max-width: 100px;
    height: 70px;
  }

  .top-item .property-image-small {
    width: 50px;
    height: 35px;
  }
}

.clickable {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.clickable:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.clickable-property {
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 5px;
  border-radius: 5px;
}

.clickable-property:hover {
  background-color: rgba(63, 81, 181, 0.05);
}

.property-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 10px;
}

.property-image-small {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 10px;
  vertical-align: middle;
}

/* Countries Panel */
.panel.countries-panel {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(63, 81, 181, 0.1);
  border: 1.5px solid rgba(63, 81, 181, 0.15);
  padding: 24px;
  margin-top: 30px;
  max-width: 100%;
}

.panel.countries-panel h3 {
  font-size: 1.3rem;
  color: #3f51b5;
  margin-bottom: 20px;
  font-weight: 600;
  text-align: center;
}

.panel.countries-panel canvas {
  border: none;
  box-shadow: none;
  background: transparent;
  max-height: 300px;
  margin: 0 auto;
  display: block;
}

/* New formal theme styles for analytics */
.summary-cards.formal {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: flex-start;
}

.summary-cards.formal .card {
  flex: 1 1 180px;
  min-width: 180px;
  max-width: 240px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;
}

.summary-cards.formal .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.summary-cards.formal .card h3 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 8px;
  font-weight: 600;
}

.summary-cards.formal .card p {
  font-size: 2rem;
  font-weight: 700;
  color: #3f51b5;
  margin: 0;
}

.summary-cards.formal .card .highlight {
  color: #3f51b5;
  font-weight: 600;
}

/* Formal filter section */
.filter-section.formal {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;
}

.filter-section.formal label {
  color: #333;
  font-weight: 500;
}

.filter-section.formal select {
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  color: #333;
  font-size: 0.9rem;
  outline: none;
  transition: all 0.3s ease;
}

.filter-section.formal select:focus {
  border-color: #3f51b5;
  box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.2);
}

/* Charts container for side-by-side layout */
.charts-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;
  padding: 20px;
}

.chart-panel h3 {
  font-size: 1.1rem;
  color: #3f51b5;
  margin-bottom: 15px;
  font-weight: 600;
  text-align: center;
}

.main-chart {
  flex: 3;
  min-width: 300px;
}

.countries-panel {
  flex: 2;
  min-width: 250px;
}

/* Top items section in formal style */
.top-items-section.formal {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.top-items-section.formal .top-item {
  flex: 1 1 300px;
  min-width: 280px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;
  padding: 20px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.top-items-section.formal .top-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.top-items-section.formal .top-item h3 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 12px;
  font-weight: 600;
  text-align: left;
}

.top-items-section.formal .top-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.top-items-section.formal .top-item ul li {
  padding: 8px 0;
  font-size: 0.95rem;
  color: #333;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
}

/* Responsive for charts container */
@media (max-width: 992px) {
  .charts-container {
    flex-direction: column;
  }
  
  .main-chart, .countries-panel {
    flex: 1 1 100%;
  }
}

.view-details-btn {
  background-color: #4f46e5;
  color: white;
  border: none;
  padding: 8px 14px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 2px 5px rgba(79, 70, 229, 0.2);
}

.view-details-btn:hover {
  background-color: #4338ca;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
}

.view-details-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(79, 70, 229, 0.2);
}

.view-details-btn i {
  font-size: 14px;
}