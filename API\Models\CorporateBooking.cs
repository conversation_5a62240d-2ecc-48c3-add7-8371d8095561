using System.ComponentModel.DataAnnotations;

namespace API.Models
{
    public enum CompanyStatus
    {
        Active,
        Inactive,
        Pending,
        Suspended
    }

    public enum ApprovalStatus
    {
        Pending,
        Approved,
        Rejected,
        RequiresReview
    }

    public class Company
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Name { get; set; }
        
        [StringLength(100)]
        public string TaxId { get; set; }
        
        [StringLength(500)]
        public string Address { get; set; }
        
        [StringLength(100)]
        public string City { get; set; }
        
        [StringLength(100)]
        public string Country { get; set; }
        
        [StringLength(20)]
        public string PostalCode { get; set; }
        
        [StringLength(20)]
        public string PhoneNumber { get; set; }
        
        [StringLength(255)]
        public string Email { get; set; }
        
        [StringLength(255)]
        public string Website { get; set; }
        
        public CompanyStatus Status { get; set; } = CompanyStatus.Pending;
        
        public decimal CreditLimit { get; set; }
        
        public decimal AvailableCredit { get; set; }
        
        public int PaymentTerms { get; set; } = 30; // Days
        
        public bool RequiresApproval { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual ICollection<CorporateUser> CorporateUsers { get; set; } = new List<CorporateUser>();
        public virtual ICollection<CorporateRate> CorporateRates { get; set; } = new List<CorporateRate>();
        public virtual ICollection<CorporateBooking> CorporateBookings { get; set; } = new List<CorporateBooking>();
        public virtual ICollection<ExpenseReport> ExpenseReports { get; set; } = new List<ExpenseReport>();
    }

    public class CorporateUser
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int CompanyId { get; set; }
        
        [StringLength(100)]
        public string EmployeeId { get; set; }
        
        [StringLength(100)]
        public string Department { get; set; }
        
        [StringLength(100)]
        public string JobTitle { get; set; }
        
        public bool CanApproveBookings { get; set; }
        
        public decimal BookingLimit { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual User User { get; set; }
        public virtual Company Company { get; set; }
        public virtual ICollection<BookingApproval> BookingApprovals { get; set; } = new List<BookingApproval>();
    }

    public class CorporateRate
    {
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int PropertyId { get; set; }
        
        public decimal DiscountPercentage { get; set; }
        
        public decimal FixedRate { get; set; }
        
        public DateTime ValidFrom { get; set; }
        
        public DateTime ValidTo { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Company Company { get; set; }
        public virtual Property Property { get; set; }
    }

    public class CorporateBooking
    {
        public int Id { get; set; }
        public int BookingId { get; set; }
        public int CompanyId { get; set; }
        public int CorporateUserId { get; set; }
        
        [StringLength(100)]
        public string ProjectCode { get; set; }
        
        [StringLength(100)]
        public string CostCenter { get; set; }
        
        [StringLength(500)]
        public string BusinessPurpose { get; set; }
        
        public ApprovalStatus ApprovalStatus { get; set; } = ApprovalStatus.Pending;
        
        public int? ApprovedBy { get; set; }
        
        public DateTime? ApprovedAt { get; set; }
        
        [StringLength(500)]
        public string ApprovalNotes { get; set; }
        
        public bool RequiresReceipt { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Booking Booking { get; set; }
        public virtual Company Company { get; set; }
        public virtual CorporateUser CorporateUser { get; set; }
        public virtual CorporateUser ApprovedByUser { get; set; }
        public virtual ICollection<BookingApproval> Approvals { get; set; } = new List<BookingApproval>();
    }

    public class BookingApproval
    {
        public int Id { get; set; }
        public int CorporateBookingId { get; set; }
        public int ApproverId { get; set; }
        
        public ApprovalStatus Status { get; set; }
        
        [StringLength(500)]
        public string Comments { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual CorporateBooking CorporateBooking { get; set; }
        public virtual CorporateUser Approver { get; set; }
    }

    public class ExpenseReport
    {
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int UserId { get; set; }
        
        [StringLength(100)]
        public string ReportNumber { get; set; }
        
        public DateTime ReportDate { get; set; }
        
        public decimal TotalAmount { get; set; }
        
        public ApprovalStatus Status { get; set; } = ApprovalStatus.Pending;
        
        [StringLength(500)]
        public string Description { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Company Company { get; set; }
        public virtual User User { get; set; }
        public virtual ICollection<ExpenseReportItem> Items { get; set; } = new List<ExpenseReportItem>();
    }

    public class ExpenseReportItem
    {
        public int Id { get; set; }
        public int ExpenseReportId { get; set; }
        public int BookingId { get; set; }
        
        public decimal Amount { get; set; }
        
        [StringLength(200)]
        public string Description { get; set; }
        
        public DateTime ExpenseDate { get; set; }

        // Navigation Properties
        public virtual ExpenseReport ExpenseReport { get; set; }
        public virtual Booking Booking { get; set; }
    }
}
