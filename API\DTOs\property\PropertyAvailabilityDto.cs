//namespace API.DTOs
//{
//    public class PropertyAvailabilityDto
//    {
//        public int Id { get; set; }
//        public int PropertyId { get; set; }
//        public DateTime StartDate { get; set; }
//        public DateTime EndDate { get; set; }
//        public bool IsAvailable { get; set; }
//        public decimal? SpecialPrice { get; set; }
//        public string Notes { get; set; }
//    }
//} 