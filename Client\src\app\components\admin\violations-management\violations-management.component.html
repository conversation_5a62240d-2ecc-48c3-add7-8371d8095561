<div class="violations-container">
  <h2 class="section-title">Violation Reports</h2>
  
  <div class="filters">
    <div class="filter-group">
      <label for="statusFilter">Filter by Status:</label>
      <select id="statusFilter" [(ngModel)]="statusFilter" (change)="onStatusFilterChange()">
        <option value="all">All Statuses</option>
        <option *ngFor="let status of violationStatuses" [value]="status.value">{{ status.label }}</option>
      </select>
    </div>
  </div>
  
  <div class="violations-list">
    <div *ngIf="loading" class="loading">Loading violations...</div>
    <div *ngIf="error" class="error-message">{{ error }}</div>
    
    <div *ngIf="!loading && !error && filteredViolations.length === 0" class="empty-state">
      <p>No violation reports found.</p>
    </div>
    
    <table *ngIf="!loading && !error && filteredViolations.length > 0" class="violations-table">
      <thead>
        <tr>
          <th>ID</th>
          <th>Type</th>
          <th>Reporter</th>
          <th>Reported Entity</th>
          <th>Date</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let violation of filteredViolations">
          <td>{{ violation.id }}</td>
          <td>{{ violation.violationType }}</td>
          <td>{{ violation.reporterName }}</td>
          <td>
            <span *ngIf="violation.reportedPropertyId">
              Property: {{ violation.reportedPropertyTitle }}
            </span>
            <span *ngIf="violation.reportedHostId">
              Host: {{ violation.reportedHostName }}
            </span>
          </td>
          <td>{{ violation.createdAt | date:'short' }}</td>
          <td>
            <span class="status-badge" [ngClass]="violation.status.toLowerCase()">
              {{ violation.status }}
            </span>
          </td>
          <td>
            <button class="view-btn" (click)="viewDetails(violation)">View Details</button>
          </td>
        </tr>
      </tbody>
    </table>
    
    <!-- Pagination -->
    <div class="pagination" *ngIf="totalItems > pageSize">
      <button 
        class="page-btn"
        [disabled]="currentPage === 1"
        (click)="onPageChange(currentPage - 1)">
        Previous
      </button>
      
      <span class="page-info">
        Page {{ currentPage }} of {{ Math.ceil(totalItems / pageSize) }}
      </span>
      
      <button 
        class="page-btn"
        [disabled]="currentPage === Math.ceil(totalItems / pageSize)"
        (click)="onPageChange(currentPage + 1)">
        Next
      </button>
    </div>
  </div>
</div>

<!-- Violation Details Modal -->
<div class="modal-overlay" *ngIf="showDetailsModal">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Violation Details</h3>
      <button (click)="closeDetailsModal()" class="close-btn">&times;</button>
    </div>
    
    <div class="modal-body" *ngIf="selectedViolation">
      <div *ngIf="error" class="error-message">{{ error }}</div>
      <div *ngIf="success" class="success-message">{{ success }}</div>
      
      <div class="detail-section">
        <h4>General Information</h4>
        <div class="detail-row">
          <span class="label">ID:</span>
          <span class="value">{{ selectedViolation.id }}</span>
        </div>
        <div class="detail-row">
          <span class="label">Type:</span>
          <span class="value">{{ selectedViolation.violationType }}</span>
        </div>
        <div class="detail-row">
          <span class="label">Status:</span>
          <span class="value status-badge" [ngClass]="selectedViolation.status.toLowerCase()">
            {{ selectedViolation.status }}
          </span>
        </div>
        <div class="detail-row">
          <span class="label">Reported On:</span>
          <span class="value">{{ selectedViolation.createdAt | date:'medium' }}</span>
        </div>
        <div class="detail-row" *ngIf="selectedViolation.resolvedAt">
          <span class="label">Resolved On:</span>
          <span class="value">{{ selectedViolation.resolvedAt | date:'medium' }}</span>
        </div>
      </div>
      
      <div class="detail-section">
        <h4>Involved Parties</h4>
        <div class="detail-row">
          <span class="label">Reported By:</span>
          <span class="value">{{ selectedViolation.reporterName }}</span>
        </div>
        <div class="detail-row" *ngIf="selectedViolation.reportedPropertyId">
          <span class="label">Property:</span>
          <span class="value">{{ selectedViolation.reportedPropertyTitle }}</span>
        </div>
        <div class="detail-row" *ngIf="selectedViolation.reportedHostId">
          <span class="label">Host:</span>
          <span class="value">{{ selectedViolation.reportedHostName }}</span>
        </div>
      </div>
      
      <div class="detail-section">
        <h4>Description</h4>
        <p class="description">{{ selectedViolation.description }}</p>
      </div>
      
      <div class="detail-section" *ngIf="selectedViolation.status !== 'Resolved' && selectedViolation.status !== 'Dismissed'">
        <h4>Admin Actions</h4>
        <div class="form-group">
          <label for="adminNotes">Admin Notes</label>
          <textarea 
            id="adminNotes" 
            [(ngModel)]="adminNotes" 
            rows="3" 
            placeholder="Add notes about this violation..."></textarea>
        </div>
        
        <div class="action-buttons">
          <button 
            class="status-btn under-review" 
            [disabled]="loading || selectedViolation.status === 'UnderReview'"
            (click)="updateStatus('UnderReview')">
            Mark as Under Review
          </button>
          
          <button 
            class="status-btn dismissed" 
            [disabled]="loading || selectedViolation.status === 'Dismissed'"
            (click)="updateStatus('Dismissed')">
            Dismiss Report
          </button>
          
          <button 
            class="status-btn resolved" 
            [disabled]="loading || selectedViolation.status === 'Resolved'"
            (click)="updateStatus('Resolved')">
            Mark as Resolved
          </button>
          
          <button 
            *ngIf="selectedViolation.reportedHostId"
            class="block-host-btn" 
            [disabled]="loading"
            (click)="blockHost()">
            Block Host
          </button>
          
          <button 
            class="related-bookings-btn" 
            [disabled]="loadingBookings"
            (click)="loadRelatedBookings()">
            {{ showRelatedBookings ? 'Hide Related Bookings' : 'Show Related Bookings' }}
          </button>
        </div>
      </div>
      
      <!-- Related Bookings Section -->
      <div class="detail-section" *ngIf="showRelatedBookings">
        <h4>Related Bookings</h4>
        
        <div *ngIf="loadingBookings" class="loading">Loading related bookings...</div>
        
        <div *ngIf="!loadingBookings && relatedBookings.length === 0" class="empty-state">
          <p>No related bookings found within the last 90 days.</p>
        </div>
        
        <div *ngIf="!loadingBookings && relatedBookings.length > 0" class="related-bookings">
          <table class="bookings-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Property</th>
                <th>Guest</th>
                <th>Dates</th>
                <th>Status</th>
                <th>Payment</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let booking of relatedBookings">
                <td>{{ booking.id }}</td>
                <td>{{ booking.propertyTitle }}</td>
                <td>{{ booking.guestName }}</td>
                <td>
                  {{ booking.checkInDate | date:'mediumDate' }} - 
                  {{ booking.checkOutDate | date:'mediumDate' }}
                </td>
                <td>{{ booking.status }}</td>
                <td>
                  <ng-container *ngIf="booking.paymentId">
                    ${{ booking.paymentAmount?.toFixed(2) }} 
                    <span class="payment-status" [ngClass]="booking.paymentStatus?.toLowerCase()">
                      ({{ booking.paymentStatus }})
                    </span>
                  </ng-container>
                  <span *ngIf="!booking.paymentId">No payment</span>
                </td>
                <td>
                  <button 
                    *ngIf="booking.canBeRefunded"
                    class="refund-btn" 
                    (click)="openRefundModal(booking)">
                    Refund
                  </button>
                  <span *ngIf="!booking.canBeRefunded">
                    Not refundable
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Refund Modal -->
<div class="modal-overlay" *ngIf="showRefundModal">
  <div class="modal-container refund-modal">
    <div class="modal-header">
      <h3>Process Refund</h3>
      <button (click)="closeRefundModal()" class="close-btn">&times;</button>
    </div>
    
    <div class="modal-body" *ngIf="selectedBooking">
      <div *ngIf="error" class="error-message">{{ error }}</div>
      <div *ngIf="success" class="success-message">{{ success }}</div>
      
      <div class="detail-section">
        <h4>Booking Details</h4>
        <div class="detail-row">
          <span class="label">Booking ID:</span>
          <span class="value">{{ selectedBooking.id }}</span>
        </div>
        <div class="detail-row">
          <span class="label">Property:</span>
          <span class="value">{{ selectedBooking.propertyTitle }}</span>
        </div>
        <div class="detail-row">
          <span class="label">Guest:</span>
          <span class="value">{{ selectedBooking.guestName }}</span>
        </div>
        <div class="detail-row">
          <span class="label">Total Paid:</span>
          <span class="value">${{ selectedBooking.paymentAmount?.toFixed(2) }}</span>
        </div>
      </div>
      
      <div class="detail-section">
        <h4>Refund Information</h4>
        <div class="form-group">
          <label for="refundAmount">Refund Amount</label>
          <div class="input-with-prefix">
            <span class="prefix">$</span>
            <input 
              type="number" 
              id="refundAmount" 
              [(ngModel)]="refundAmount" 
              step="0.01" 
              min="0.01" 
              [max]="selectedBooking.paymentAmount || 0"
              placeholder="Enter refund amount" />
          </div>
          <small *ngIf="selectedBooking.paymentAmount">
            Maximum refund available: ${{ selectedBooking.paymentAmount?.toFixed(2) }}
          </small>
        </div>
        
        <div class="form-group">
          <label for="refundReason">Refund Reason</label>
          <select id="refundReason" [(ngModel)]="refundReason">
            <option *ngFor="let reason of refundReasons" [value]="reason.value">{{ reason.label }}</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="refundNotes">Additional Notes</label>
          <textarea 
            id="refundNotes" 
            [(ngModel)]="adminNotes" 
            rows="3" 
            placeholder="Add notes about this refund..."></textarea>
        </div>
      </div>
      
      <div class="action-buttons">
        <button 
          class="cancel-btn" 
          [disabled]="processingRefund"
          (click)="closeRefundModal()">
          Cancel
        </button>
        
        <button 
          class="refund-process-btn" 
          [disabled]="processingRefund || !refundAmount || refundAmount <= 0 || refundAmount > (selectedBooking.paymentAmount || 0)"
          (click)="processRefund()">
          {{ processingRefund ? 'Processing...' : 'Process Refund' }}
        </button>
      </div>
    </div>
  </div>
</div> 