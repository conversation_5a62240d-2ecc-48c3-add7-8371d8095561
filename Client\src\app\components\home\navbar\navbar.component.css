.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 10px;
  background-color: #fff;
  width: 150px;
  max-width: 1200px;
  transition: all 0.3s ease;
}

.navbar-left,
.navbar-right {
  display: flex;
  align-items: center;
  transition: transform 0.3s ease;
  gap: 15px;
}

.globe-icon {
  font-size: 24px;
  cursor: pointer;
  color: #484848;
  transition: font-size 0.3s ease, color 0.3s ease;
}

:host-context(.header.scrolled) .globe-icon {
  font-size: 20px;
  color: #484848;
}

/* Original menu styles */
.menu-profile {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 10px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  cursor: pointer;
  background-color: #f7f7f7;
  transition: all 0.3s ease;
}

:host-context(.header.scrolled) .menu-profile {
  padding: 4px 8px;
  gap: 8px;
}

.menu-icon,
.profile-icon {
  font-size: 20px;
  color: #484848;
  transition: font-size 0.3s ease, color 0.3s ease;
}

:host-context(.header.scrolled) .menu-icon,
:host-context(.header.scrolled) .profile-icon {
  font-size: 18px;
  color: #484848;
}

.dropdown {
  position: absolute;
  top: 60px;
  right: 20px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 200px;
  z-index: 1000;
}

.dropdown ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dropdown li {
  padding: 10px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.dropdown li:last-child {
  border-bottom: none;
}

.dropdown a {
  text-decoration: none;
  color: #333;
  font-size: 14px;
}

.dropdown a:hover {
  color: #ff385c;
}

.navbar.scrolled {
  transform: scale(0.9);
  padding: 8px 8px;
}

a {
  cursor: pointer;
}

.profile-image {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.profile-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #d7d7d7;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-placeholder {
  font-size: 18px;
  font-weight: 600;
  color: #414141;
}

.chat-link {
  position: relative;
}

.unread-badge {
  position: absolute;
  top: -8px;
  right: -10px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 0.7rem;
  font-weight: bold;
}

.message-icon {
  font-size: 20px;
  color: #4a5568;
  transition: color 0.2s;
}

.message-icon {
  color: #3182ce;
}

.message-notification-badge {
  position: absolute;
  top: 10px;
  background-color: #ff5a5f;
  color: white;
  border-radius: 50%;
  font-size: 0.7rem;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3px;
  transform: translate(30%, -30%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(30%, -30%) scale(1);
  }

  50% {
    transform: translate(30%, -30%) scale(1.1);
  }

  100% {
    transform: translate(30%, -30%) scale(1);
  }
}