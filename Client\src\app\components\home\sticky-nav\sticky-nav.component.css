.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    transform: translateY(0);
    height: 70px;
    z-index: 1000;
}


.footer.hidden {
    transform: translateY(100%);
}

.footer ul {
    margin: 10px 10px;
    display: flex;
    justify-content: space-between;
    list-style: none;
}

.footer ul li {
    display: flex;
    flex-direction: column;
    align-items: center;
}

@media (min-width: 768px) {
    .footer {
        display: none;
    }
}
