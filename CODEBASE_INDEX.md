# Airbnb Clone - Codebase Index

## Project Overview
A full-stack Airbnb clone application built with <PERSON><PERSON> (frontend) and ASP.NET Core (backend), featuring property listings, booking management, user authentication, payments, and real-time chat.

## Technology Stack
- **Frontend**: Angular 18, TypeScript, Bootstrap, Chart.js, Socket.IO
- **Backend**: ASP.NET Core 8, Entity Framework Core, SQL Server
- **Authentication**: JWT, Google OAuth
- **Payments**: Stripe
- **Real-time**: SignalR
- **Maps**: Leaflet
- **Email**: SMTP

---

## Backend (API) Structure

### 📁 Controllers
Core API endpoints for different features:

- **AuthController.cs** - Authentication (login, register, logout, password reset)
- **PropertiesController.cs** - Property CRUD operations, search, filtering
- **BookingController.cs** - Booking management, availability checking
- **ProfileController.cs** - User profile management
- **AdminController.cs** - Admin dashboard operations
- **ChatController.cs** - Real-time messaging
- **PayoutController.cs** - Host payout management
- **ReviewsController.cs** - Property reviews and ratings
- **NotificationController.cs** - User notifications
- **StripeWebhookController.cs** - Payment webhook handling
- **ViolationsController.cs** - Content moderation
- **AIController.cs** - AI-powered features
- **AmenityController.cs** - Property amenities
- **AvailabilityController.cs** - Property availability
- **BookingPaymentController.cs** - Payment processing
- **HostVerificationController.cs** - Host verification
- **PromotionController.cs** - Promotional campaigns
- **PropertyCategoryController.cs** - Property categories

### 📁 Models
Database entities representing core business objects:

- **User.cs** - User accounts and authentication
- **Property.cs** - Property listings
- **Booking.cs** - Booking records
- **Host.cs** - Host profiles
- **Review.cs** - Property reviews
- **Message.cs** - Chat messages
- **Conversation.cs** - Chat conversations
- **Notification.cs** - User notifications
- **BookingPayment.cs** - Payment records
- **PropertyImage.cs** - Property photos
- **Amenity.cs** - Property amenities
- **PropertyAvailability.cs** - Availability calendar
- **CancellationPolicy.cs** - Booking policies
- **Promotion.cs** - Marketing promotions
- **Violation.cs** - Content violations
- **Favourite.cs** - User favorites
- **PropertyCategory.cs** - Property types
- **HostVerification.cs** - Host verification data
- **HostPayout.cs** - Payout records
- **BookingPayout.cs** - Booking-specific payouts
- **UserUsedPromotion.cs** - Promotion usage tracking
- **AIConversation.cs** - AI chat history

### 📁 Services
Business logic and data access layers:

#### Authentication & Authorization
- **AuthRepo/AuthService.cs** - User authentication, JWT tokens
- **AuthRepo/IAuthService.cs** - Authentication interface

#### Property Management
- **PropertyRepo/PropertyService.cs** - Property CRUD, search, filtering
- **PropertyRepo/IPropertyService.cs** - Property service interface
- **PropertyAvailabilityRepo/** - Availability management
- **PropertyCategoryRepo/** - Category management

#### Booking System
- **BookingRepo/** - Booking management
- **BookingPaymentRepo/** - Payment processing

#### User Management
- **ProfileService.cs** - User profile operations
- **IProfileService.cs** - Profile service interface

#### Communication
- **ChatRepo/** - Real-time messaging
- **NotificationRepository/** - Notification system
- **EmailService/** - Email notifications

#### Payments & Finance
- **Payoutrepo/** - Host payout management

#### Content & Moderation
- **ReviewRepo/** - Review management
- **ViolationService.cs** - Content moderation
- **IViolationService.cs** - Violation service interface

#### AI & Analytics
- **AIRepo/** - AI-powered features
- **AdminRepo/** - Admin analytics

#### Infrastructure
- **GenericRepo/** - Generic repository pattern
- **AmenityRepo/** - Amenity management
- **PromotionRepo/** - Promotion system
- **HostVerificationRepo/** - Verification process

### 📁 DTOs (Data Transfer Objects)
Request/response models for API endpoints:

- **Auth/** - Authentication DTOs
- **property/** - Property-related DTOs
- **Booking/** - Booking DTOs
- **Profile/** - User profile DTOs
- **Admin/** - Admin dashboard DTOs
- **Chat/** - Messaging DTOs
- **Review/** - Review DTOs
- **Notification/** - Notification DTOs
- **BookingPayment/** - Payment DTOs
- **Amenity/** - Amenity DTOs
- **Promotion/** - Promotion DTOs
- **PropertyCategoryDTOs/** - Category DTOs
- **HostVerification/** - Verification DTOs
- **AI/** - AI feature DTOs
- **ViolationDto.cs** - Violation DTOs

### 📁 Data
Database configuration and Entity Framework setup:

- **AppDbContext.cs** - Main database context
- **Configurations/** - Entity configurations
- **ServiceExtension.cs** - Database service registration

### 📁 Hubs
Real-time communication:

- **ChatHub.cs** - SignalR hub for real-time messaging

### 📁 Middleware
Cross-cutting concerns:

- **ExceptionMiddleware.cs** - Global exception handling
- **RateLimitingMiddleware.cs** - API rate limiting

### 📁 Migrations
Database schema evolution:

- **20250501130801_addingResetTokenForUserTable.cs** - Password reset tokens
- **20250502065733_test.cs** - Test migration
- **AppDbContextModelSnapshot.cs** - Current schema snapshot

### 📁 Configuration Files
- **appsettings.json** - Application configuration
- **appsettings.Development.json** - Development settings
- **Program.cs** - Application startup and configuration
- **API.csproj** - Project dependencies

---

## Frontend (Client) Structure

### 📁 src/app/components

#### Authentication
- **auth/login/** - User login component
- **auth/register/** - User registration component
- **auth/forgot-password/** - Password reset component
- **auth/reset-password/** - Password reset form

#### Property Management
- **property-details/** - Individual property view and booking
- **property-gallary/** - Property image gallery
- **host/add-property/** - Property creation form
- **host/host-proprties/** - Host property management (Note: typo in original)
- **host/property-details/** - Host property details
- **host/edit-property/** - Property editing interface
- **host/image-upload/** - Property image management

#### Booking System
- **bookings/** - User booking management
- **checkout/** - Booking checkout process
- **payment-success/** - Payment confirmation
- **host/bookings/** - Host booking management
- **host/booking-details/** - Individual booking details
- **host/property-booking-details/** - Property-specific bookings

#### User Interface & Navigation
- **home/** - Landing page with property search
  - **header/** - Page header component
  - **footer/** - Page footer component
  - **navbar/** - Home navigation
  - **search-bar/** - Property search functionality
  - **property-listing/** - Property grid display
  - **sticky-nav/** - Sticky navigation bar
  - **notification/** - Notification display
- **main-navbar/** - Main application navigation
- **navbar/** - General navigation component
- **not-found/** - 404 error page

#### User Management
- **profile/** - User profile management
- **edit-profile/** - Profile editing interface
- **wishlist/** - User favorites/wishlist
- **verifications/** - User verification process
- **host-verification/** - Host-specific verification

#### Host Features
- **host/host-dashboard/** - Host analytics dashboard
- **host/payouts/** - Payout management
- **host/earnings-chart/** - Earnings visualization
- **host-payout/** - Payout component

#### Communication
- **chatting/** - Real-time messaging module
  - **chat-routing.module.ts** - Chat routing
  - **chat.module.ts** - Chat module definition
  - **components/** - Chat sub-components

#### Administration
- **admin/** - Admin dashboard
- **admin/host-details/** - Host management
- **admin/violations-management/** - Content moderation
- **admin/verifinghost/** - Host verification management

#### Utility Components
- **ai-chatbot/** - AI-powered chat assistant
- **date-picker/** - Custom date picker
- **map/** - Location mapping (Leaflet integration)
- **common/** - Shared components
  - **report-violation/** - Content reporting
  - **validation-error/** - Form validation display

#### Protected Routes
- **protected/** - Protected route components
  - **dashboard/** - User dashboard
  - **host/** - Host-specific protected routes

### 📁 src/app/services
Frontend business logic and API communication:

#### Core Services
- **auth.service.ts** - Authentication and user management
- **property.service.ts** - Property operations and search
- **property-crud.service.ts** - Property CRUD operations
- **booking.service.ts** - Booking management
- **profile.service.ts** - User profile operations
- **payment.service.ts** - Payment processing
- **payout.service.ts** - Host payout management

#### Communication Services
- **chat.service.ts** - Real-time messaging
- **chatSignal.service.ts** - SignalR chat integration
- **notification.service.ts** - User notifications
- **toast.service.ts** - Toast notification display

#### Admin & Moderation
- **admin-service.service.ts** - Admin operations
- **violation.service.ts** - Content moderation
- **host-service.service.ts** - Host management

#### Utility Services
- **ai-service.service.ts** - AI-powered features
- **firebaseAuth.service.ts** - Firebase authentication
- **password-reset.service.ts** - Password reset functionality
- **redirect.service.ts** - Navigation redirection
- **admin-redirect.service.ts** - Admin-specific redirects

### 📁 src/app/models
TypeScript interfaces and models:

- **user.model.ts** - User data structures and authentication
- **property.ts** - Property interfaces and types
- **messaging.model.ts** - Chat and messaging models
- **payment.model.ts** - Payment and transaction models
- **cancellation-policy.model.ts** - Booking policy models
- **ai.models.ts** - AI service models
- **test-booking-cancel.ts** - Booking cancellation testing

### 📁 src/app/guards
Route protection and access control:

- **auth.guard.ts** - Authentication guard
- **admin.guard.ts** - Admin route protection
- **role.guard.ts** - Role-based access control
- **home.guard.ts** - Home page access control
- **property-host.guard.ts** - Property host verification

### 📁 src/app/interceptors
HTTP request/response handling:

- **auth.interceptor.ts** - JWT token injection and management

### 📁 src/app/directives
Custom Angular directives:

- **report-violation.directive.ts** - Content reporting functionality
- **validated-form.directive.ts** - Form validation enhancement

### 📁 src/app/pipes
Custom data transformation pipes:

- **ceil.pipe.ts** - Ceiling mathematical operation
- **reverse.pipe.ts** - Array/string reversal
- **truncate.pipe.ts** - Text truncation

### 📁 src/app/validators
Custom form validators:

- **input-validators.ts** - Custom input validation rules

### 📁 Configuration Files
- **angular.json** - Angular CLI configuration
- **package.json** - Dependencies and scripts
- **tsconfig.json** - TypeScript configuration
- **proxy.conf.json** - Development proxy settings

---

## Database Schema

### Core Tables
- **Users** - User accounts and profiles
- **Properties** - Property listings
- **Bookings** - Booking records
- **HostProfules** - Host profiles (Note: typo in original)
- **Reviews** - Property reviews
- **Messages** - Chat messages
- **Conversations** - Chat conversations
- **Notifications** - User notifications
- **BookingPayments** - Payment records
- **PropertyImages** - Property photos
- **Amenities** - Available amenities
- **PropertyAvailabilities** - Availability calendar
- **CancellationPolicies** - Booking policies
- **Promotions** - Marketing campaigns
- **Violations** - Content violations
- **Favourites** - User favorites
- **PropertyCategories** - Property types

### Views
- **VwPropertyDetails** - Property details view
- **VwActivePromotions** - Active promotions view
- **VwHostPerformance** - Host performance metrics

---

## Key Features

### 🔐 Authentication & Authorization
- JWT-based authentication
- Google OAuth integration
- Role-based access control (Guest, Host, Admin)
- Password reset functionality

### 🏠 Property Management
- Property CRUD operations
- Image upload and management
- Availability calendar
- Amenity management
- Category-based filtering
- Search and filtering

### 📅 Booking System
- Real-time availability checking
- Booking creation and management
- Cancellation policies
- Payment integration

### 💳 Payment Processing
- Stripe integration
- Secure payment handling
- Webhook processing
- Payout management

### 💬 Real-time Communication
- SignalR-based chat system
- Real-time notifications
- Message history

### 📊 Analytics & Admin
- Host performance metrics
- Admin dashboard
- Content moderation
- Violation management

### 🤖 AI Features
- AI-powered recommendations
- Automated responses
- Content analysis

### 📱 Responsive Design
- Mobile-first approach
- Bootstrap integration
- Cross-browser compatibility

---

## Development Setup

### Prerequisites
- Node.js 18+
- .NET 8 SDK
- SQL Server
- Angular CLI

### Backend Setup
```bash
cd API
dotnet restore
dotnet run --launch-profile https
```

### Frontend Setup
```bash
cd Client
npm install
npm start
```

### Database Setup
1. Update connection string in `appsettings.json`
2. Run migrations: `dotnet ef database update`
3. Execute SQL scripts in `Scripts/` folder

---

## API Endpoints Summary

### Authentication
- `POST /api/Auth/register` - User registration
- `POST /api/Auth/login` - User login
- `POST /api/Auth/logout` - User logout
- `POST /api/Auth/refresh-token` - Token refresh
- `POST /api/Auth/forgot-password` - Password reset request
- `POST /api/Auth/reset-password` - Password reset

### Properties
- `GET /api/Properties` - List properties
- `POST /api/Properties` - Create property
- `GET /api/Properties/{id}` - Get property details
- `PUT /api/Properties/{id}` - Update property
- `DELETE /api/Properties/{id}` - Delete property

### Bookings
- `GET /api/Booking` - List bookings
- `POST /api/Booking` - Create booking
- `GET /api/Booking/{id}` - Get booking details
- `PUT /api/Booking/{id}` - Update booking
- `DELETE /api/Booking/{id}` - Cancel booking

### Admin
- `GET /api/Admin/dashboard` - Admin dashboard data
- `GET /api/Admin/users` - User management
- `GET /api/Admin/violations` - Content violations

---

## Security Features

### Backend Security
- JWT token authentication
- CORS configuration
- Rate limiting
- Input validation
- SQL injection prevention
- XSS protection

### Frontend Security
- Route guards
- Token management
- Secure HTTP communication
- Input sanitization

---

## Testing

### Backend Testing
- Unit tests for services
- Integration tests for controllers
- Database testing with in-memory provider

### Frontend Testing
- Component unit tests with Jasmine/Karma
- E2E testing setup
- Service testing

---

## Deployment Considerations

### Backend Deployment
- Docker containerization
- Environment-specific configurations
- Database migration strategies
- Logging and monitoring

### Frontend Deployment
- Build optimization
- CDN integration
- Progressive Web App features
- Performance monitoring

---

## Contributing Guidelines

### Code Standards
- Follow C# coding conventions
- Use TypeScript strict mode
- Implement proper error handling
- Write comprehensive tests
- Document API endpoints

### Git Workflow
- Feature branch development
- Pull request reviews
- Automated testing
- Continuous integration

---

## File Extensions & Technologies

### Backend File Types
- **.cs** - C# source files (Controllers, Services, Models)
- **.csproj** - Project configuration files
- **.json** - Configuration and settings files
- **.sql** - Database scripts and migrations

### Frontend File Types
- **.ts** - TypeScript source files
- **.html** - Angular component templates
- **.css/.scss** - Stylesheets and component styles
- **.json** - Configuration and package files
- **.spec.ts** - Unit test files

## Key Dependencies

### Backend Dependencies (API.csproj)
- **Microsoft.AspNetCore.App** - ASP.NET Core framework
- **Microsoft.EntityFrameworkCore.SqlServer** - SQL Server provider
- **Microsoft.AspNetCore.Authentication.JwtBearer** - JWT authentication
- **Microsoft.AspNetCore.SignalR** - Real-time communication
- **Stripe.net** - Payment processing
- **AutoMapper** - Object mapping
- **Swashbuckle.AspNetCore** - API documentation

### Frontend Dependencies (package.json)
- **@angular/core** - Angular framework
- **@angular/router** - Routing
- **@angular/forms** - Form handling
- **@angular/common/http** - HTTP client
- **bootstrap** - UI framework
- **chart.js** - Data visualization
- **socket.io-client** - Real-time communication
- **@stripe/stripe-js** - Payment integration
- **leaflet** - Map integration
- **moment** - Date manipulation
- **ngx-toastr** - Toast notifications

## Environment Configuration

### Backend Environments
- **appsettings.json** - Production settings
- **appsettings.Development.json** - Development settings

### Frontend Environments
- **src/environments/environment.ts** - Development environment
- **src/environments/environment.prod.ts** - Production environment

## Build & Deployment

### Backend Build
```bash
dotnet build
dotnet publish -c Release
```

### Frontend Build
```bash
ng build --prod
ng build --configuration production
```

## Database Migrations

### Entity Framework Commands
```bash
# Add migration
dotnet ef migrations add MigrationName

# Update database
dotnet ef database update

# Remove migration
dotnet ef migrations remove
```

## Testing Structure

### Backend Tests
- Unit tests for services
- Integration tests for controllers
- Repository pattern testing

### Frontend Tests
- Component unit tests (*.spec.ts)
- Service testing
- E2E testing setup

## Performance Considerations

### Backend Optimizations
- Entity Framework query optimization
- Caching strategies
- Async/await patterns
- Connection pooling

### Frontend Optimizations
- Lazy loading modules
- OnPush change detection
- Image optimization
- Bundle splitting

## Monitoring & Logging

### Backend Logging
- Built-in ASP.NET Core logging
- Structured logging with Serilog
- Application Insights integration

### Frontend Monitoring
- Error tracking
- Performance monitoring
- User analytics

---

This comprehensive codebase index provides developers with a complete overview of the Airbnb clone application structure, making it easier to navigate, understand, and contribute to the project architecture.
