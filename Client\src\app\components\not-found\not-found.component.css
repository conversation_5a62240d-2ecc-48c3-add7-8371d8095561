/* Add these styles to your component CSS file */

.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  padding: 20px;
}

.not-found-content {
  text-align: center;
  max-width: 600px;
  padding: 40px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.error-code {
  font-size: 120px;
  font-weight: 700;
  color: #ff385c;
  line-height: 1;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.error-divider {
  height: 4px;
  width: 60px;
  background-color: #ff385c;
  margin: 0 auto 24px;
  border-radius: 2px;
}

.error-message {
  font-size: 18px;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.5;
}

.primary-button {
  padding: 12px 28px;
  background-color: #ff385c;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-button:hover {
  background-color: #e61845;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 56, 92, 0.3);
}

.primary-button:active {
  transform: translateY(0);
}
