/* CHAT MAIN STYLES */
.chat-layout {
    display: flex;
    height: calc(100vh - 80px);
    position: relative;
    background-color: #ffffff;

    /* Prevent scrolling on the main container */
}

.chat-sidebar {
    width: 360px;
    min-width: 360px;
    /* Prevent shrinking */
    border-right: 1px solid #ebebeb;
    display: flex;
    flex-direction: column;
    background-color: white;
    height: 100%;
    overflow-y: auto;

    /* Allow conversation list to scroll if needed */
    .sidebar-header {
        padding: 24px;
        border-bottom: 1px solid #ebebeb;
        display: flex;
        align-items: center;
        position: fixed;


        h1 {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
            color: #222222;
        }

        .unread-badge {
            background-color: #FF385C;
            color: white;
            border-radius: 50%;
            min-width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-left: 12px;
            padding: 0 6px;
        }
    }
}

.chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    position: relative;
    /* For proper positioning of fixed elements */
    height: 100%;
    /* Control overflow in child elements */

    .empty-state {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #717171;
        text-align: center;
        padding: 24px;

        .icon {
            font-size: 64px;
            margin-bottom: 24px;
            color: #dddddd;
        }

        h2 {
            margin: 0 0 16px;
            font-size: 24px;
            font-weight: 600;
        }

        p {
            margin: 0;
            max-width: 400px;
            font-size: 16px;
        }
    }
}

.mobile-nav {
    display: none;
    background-color: white;
    padding: 16px;
    border-bottom: 1px solid #ebebeb;

    .back-btn {
        display: flex;
        align-items: center;
        background: none;
        border: none;
        color: #222222;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        padding: 0;

        i {
            margin-right: 12px;
            color: #717171;
        }

        .unread-badge {
            background-color: #FF385C;
            color: white;
            border-radius: 50%;
            min-width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-left: 12px;
        }
    }
}