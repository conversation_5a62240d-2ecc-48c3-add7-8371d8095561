.report-btn {
  background-color: transparent;
  color: #FF385C;
  border: 1px solid #FF385C;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.report-btn:hover {
  background-color: rgba(255, 56, 92, 0.1);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #222;
  font-size: 18px;
}

.close-btn {
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #717171;
}

.modal-body {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #222;
}

select, textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #dddddd;
  border-radius: 8px;
  font-size: 16px;
}

select:focus, textarea:focus {
  outline: none;
  border-color: #FF385C;
}

textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.cancel-btn {
  background-color: #fff;
  color: #222;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-btn {
  background-color: #FF385C;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background-color: #f7f7f7;
}

.submit-btn:hover {
  background-color: #e03151;
}

.submit-btn:disabled {
  background-color: #ffb3c0;
  cursor: not-allowed;
}

.error-message {
  background-color: #fff3f5;
  color: #e03151;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #ffb3c0;
}

.success-message {
  background-color: #f2fbf4;
  color: #1da75c;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #a8e5c2;
} 