.profile-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  
  font-family: 'Circular', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.loading, .error-message {
  text-align: center;
  padding: 40px;
  font-size: 18px;
}

.error-message {
  color: #ff385c;
}

.profile-layout {
  display: flex;
  gap: 40px;
}

/* Left sidebar */
.profile-sidebar {
  width: 300px;
  flex-shrink: 0;
  position: sticky;
  top: 24px;
  align-self: flex-start;
}
.fa-circle-check {
  color: #28a745;
  font-size: 20px;
  margin-right: 8px;
}
.fa-circle-xmark {
  color: #dc3545;
  font-size: 20px;
  margin-right: 8px;
}
.profile-header {
  display: flex;
  border-radius: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);
  background-color: white;
  padding: 24px 16px;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 24px;
}

.profile-image {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 16px;
}

.profile-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* .verified-badge {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background-color: #38b6ff;
  border-radius: 50%;
  width: 34px;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
} */

.profile-header h1 {
  font-size: 26px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.superhost-badge {
  display: inline-flex;
  align-items: center;
  background-color: #ff385c;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  gap: 4px;
}

.host-stats {
  display: flex;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-item h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.stat-item span {
  font-size: 14px;
  color: #717171;
}

.verified-info {
  background-color: #f7f7f7;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.verified-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.verified-info ul {
  list-style: none;
  padding: 0;
  margin: 0 0 16px 0;
}

.verified-info li {
  margin-bottom: 8px;
  font-size: 14px;
}

.learn-more {
  color: #222;
  font-size: 14px;
  text-decoration: underline;
  font-weight: 500;
}

.host-listings {
  margin-top: 24px;
}

.host-listings h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.listing-previews {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding-bottom: 8px;
}

.listing-preview {
  width: 120px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.listing-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Right content area */
.profile-content {
  flex: 1;
  min-width: 0;
}

.profile-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 24px;
}

.profile-tabs button {
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  padding: 16px 0;
  margin-right: 24px;
  color: #717171;
  cursor: pointer;
  position: relative;
}

.profile-tabs button.active {
  color: #222;
  font-weight: 600;
}

.profile-tabs button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #222;
}

/* About section */
.about-section h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 24px 0;
}

.about-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.about-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.icon {
  flex-shrink: 0;
  color: #717171;
}

.about-info {
  flex: 1;
  min-width: 0;
}

.about-info span {
  font-size: 16px;
  line-height: 1.5;
}

.about-description {
  font-size: 16px;
  line-height: 1.5;
  margin-top: 16px;
  padding-top: 10px;
}
/* Reviews section */
.reviews-section h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 24px 0;
}

.reviews-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.review-card {
  padding: 24px;
  border-radius: 12px;
  background-color: #f7f7f7;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.reviewer-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
}

.reviewer-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.reviewer-info {
  flex: 1;
}

.reviewer-name {
  font-weight: 600;
  font-size: 16px;
}

.review-date {
  color: #717171;
  font-size: 14px;
}

.review-content {
  font-size: 16px;
  line-height: 1.5;
}

.show-more {
  display: block;
  margin: 24px auto 0;
  background: none;
  border: 1px solid #222;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  color: #222;
  transition: all 0.2s;
}

.show-more:hover {
  background-color: #f7f7f7;
}

/* Ask about section */
.ask-about-section {
  margin-top: 40px;
}

.ask-about-section h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.interest-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.interest-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f7f7f7;
  padding: 8px 16px;
  border-radius: 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.interest-tag:hover {
  background-color: #e0e0e0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .profile-layout {
    flex-direction: column;
  }
  
  .profile-sidebar {
    width: 100%;
    position: static;
  }
  
  .about-content {
    grid-template-columns: 1fr;
  }
}


.profile-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.profile-image {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 15px;
  border: 3px solid #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.profile-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.verified-badge {
  position: absolute;
  bottom: 20px;
  right: 10px;
  background-color: #FF5A5F;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
}

.upload-section {
  text-align: center;
  width: 100%;
  max-width: 300px;
}

.upload-button {
  background-color: #FF5A5F;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  margin: 5px;
}
.editProfile-button {
  background-color: #FF5A5F;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  margin: 5px;
}

.upload-button:hover {
  background-color: #e04a50;
}

.upload-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.file-info {
  margin: 10px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.file-info span {
  margin-bottom: 8px;
  font-size: 14px;
  word-break: break-all;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  margin-top: 10px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #FF5A5F;
  transition: width 0.3s;
}

.error-message {
  color: #ff3333;
  font-size: 14px;
  margin-top: 10px;
}
/* Reviews Slider Section */
.reviews-slider-section {
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e0e0e0;
}

.reviews-slider-section h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
}

.reviews-slider {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding-bottom: 15px;
  scrollbar-width: none; /* Hide scrollbar for Firefox */
}

.reviews-slider::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Chrome/Safari */
}

.review-slide {
  min-width: 300px;
  background-color: #f7f7f7;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.review-text {
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 15px;
}

.review-text p {
  margin: 0;
}

.review-author {
  display: flex;
  flex-direction: column;
}

.review-author strong {
  font-weight: 600;
  color: #222;
}

.review-author span {
  font-size: 14px;
  color: #717171;
}

.slider-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.show-more-reviews {
  background: none;
  border: 1px solid #222;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  color: #222;
  transition: all 0.2s;
}

.show-more-reviews:hover {
  background-color: #f7f7f7;
}

.translation-note {
  font-size: 14px;
  color: #717171;
}

.show-original {
  color: #222;
  text-decoration: underline;
  font-weight: 500;
  margin-left: 5px;
}












/* Listings Section */
.listings-section {
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e0e0e0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.view-all {
  color: #222;
  font-weight: 500;
  text-decoration: underline;
  font-size: 14px;
}

/* Listings Slider */
.listings-slider {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding-bottom: 20px;
  scrollbar-width: none; /* Firefox */
}

.listings-slider::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}

.listing-card {
  min-width: 240px;
  flex-shrink: 0;
}

.property-type {
  font-size: 14px;
  color: #717171;
  margin-bottom: 8px;
}

.property-image {
  width: 100%;
  height: 160px;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 12px;
}

.property-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.property-image:hover img {
  transform: scale(1.05);
}

.property-info {
  padding: 0 4px;
}

.property-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 6px;
}

.stars {
  color: #FF385C;
  font-size: 14px;
}

.rating-value {
  font-size: 14px;
  font-weight: 500;
}

.property-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}








.review-rating {
  color: #ddd; /* Default color for empty stars */
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

.review-rating .filled {
  color: #ffb400; /* Color for filled stars */
}

/* If you're using Font Awesome, you might need this */
.review-rating i {
  margin-right: 2px;
}
.empty-profile-prompt {
  text-align: center;
  padding: 40px;
  background: #f7f7f7;
  border-radius: 12px;
  margin: 20px 0;
}

.empty-profile-message h3 {
  font-size: 24px;
  margin-bottom: 10px;
}

.empty-profile-message p {
  color: #666;
  margin-bottom: 20px;
}

.create-profile-button {
  background: #FF385C;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s;
}

.create-profile-button:hover {
  background: #e61e4d;
}
.message-btn {
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.message-btn:hover {
  background-color: #0069d9;
}