<!-- booking.component.html -->
<div class="container">
  <div class="mb-10">
    <h1>Bookings</h1>
    <p>Upcoming and past stays</p>
  </div>

  <div *ngIf="loading" class="text-center my-10">
    <div class="spinner-border text-primary" role="status"></div>
  </div>

  <div *ngIf="error" class="alert alert-danger">
    {{ error }}
  </div>

  <div *ngIf="!loading && bookings.length === 0" class="text-center my-10">
    <div class="empty-state">
      <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
        <line x1="16" y1="2" x2="16" y2="6"></line>
        <line x1="8" y1="2" x2="8" y2="6"></line>
        <line x1="3" y1="10" x2="21" y2="10"></line>
      </svg>
      <h3>No active bookings found</h3>
      <p>You don't have any active bookings at the moment.</p>
      <a routerLink="/home" class="btn btn-primary mt-4">Explore properties</a>
    </div>
  </div>

  <div *ngIf="!loading && bookings.length > 0" class="grid">
    <div *ngFor="let booking of bookings" class="booking-card">
      <!-- Cancel Button -->
      <button *ngIf="!isPastBooking(booking)" (click)="openCancellationModal(booking)" class="cancel-btn">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
        Cancel
      </button>

      <!-- Show Add Review button only for past bookings without reviews -->
      <button *ngIf="isPastBooking(booking) && !hasReview(booking)" (click)="addReview(booking)" class="review-btn">
        <i class="fa fa-star mr-1"></i>
        Review
      </button>

      <!-- Show Review badge if already reviewed -->
      <div *ngIf="isPastBooking(booking) && hasReview(booking)" class="reviewed-badge">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        Reviewed
      </div>

      <!-- Property Image -->
      <div class="relative">
        <img *ngIf="getFirstImageUrl(booking)" [src]="getFirstImageUrl(booking)">
        <div *ngIf="!booking.property?.images?.length"
          class="w-full h-full bg-gray-200 flex items-center justify-center">
          <span class="text-gray-400">No photo</span>
        </div>
      </div>

      <!-- Booking Details -->
      <div class="mt-3">
        <h3 *ngIf="booking.property">
          {{ booking.property.title }}
        </h3>
        <div *ngIf="booking.property" class="text-sm text-gray-700">
          {{ formatLocation(booking.property) }}
        </div>
        <div class="text-sm text-gray-600">
          {{ formatDateRange(booking.startDate, booking.endDate) }}
        </div>
        <div class="mt-1 text-gray-900 font-semibold">
          ${{ booking.totalAmount.toFixed(2) }}
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="!loading && totalCount > 0" class="pagination-container">
    <div class="pagination">
      <button [disabled]="currentPage === 1" (click)="prevPage()" class="pagination-btn">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <div class="pagination-info">
        Page {{ currentPage }} of {{ Math.ceil(totalCount / pageSize) }}
      </div>

      <button [disabled]="currentPage >= Math.ceil(totalCount / pageSize)" (click)="nextPage()" class="pagination-btn">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>

    <div class="pagination-details">
      Showing {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, totalCount) }} of {{ totalCount
      }} bookings
    </div>
  </div>
</div>

<!-- Review Modal -->
<div class="review-modal" *ngIf="showReviewModal">
  <div class="review-modal-content">
    <!-- Close button -->
    <button class="close-modal" (click)="closeReviewModal()">
      <i class="fa fa-times" aria-hidden="true"></i>
    </button>

    <!-- Modal Content -->
    <div class="review-content">
      <!-- Emoji based on rating -->
      <div class="emoji-container">
        <span *ngIf="reviewRating === 1">😕</span>
        <span *ngIf="reviewRating === 2">🙂</span>
        <span *ngIf="reviewRating === 3">😊</span>
        <span *ngIf="reviewRating === 4">👍</span>
        <span *ngIf="reviewRating === 5">❤️</span>
      </div>

      <!-- Rating description text -->
      <h2 class="rating-text">
        <span *ngIf="reviewRating === 1">Not great</span>
        <span *ngIf="reviewRating === 2">Okay</span>
        <span *ngIf="reviewRating === 3">Good</span>
        <span *ngIf="reviewRating === 4">Great</span>
        <span *ngIf="reviewRating === 5">Amazing</span>
      </h2>

      <!-- Star Rating -->
      <div class="star-rating">
        <button *ngFor="let star of [1,2,3,4,5]" (click)="setRating(star)" class="star-btn"
          [class.active]="star <= reviewRating">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="star-icon" fill="currentColor">
            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
          </svg>
        </button>
      </div>

      <!-- Review Text Area -->
      <div class="review-comment-container">
        <textarea [(ngModel)]="reviewComment" placeholder="Tell us about your stay"
          class="review-comment-input"></textarea>
      </div>

      <!-- Submit Button -->
      <button class="submit-review-btn" [disabled]="!reviewRating || !reviewComment.trim()" (click)="submitReview()">
        Submit review
      </button>
    </div>
  </div>
</div>
<!-- Toast Notification -->
<div *ngIf="showToast" class="toast-container">
  <div class="toast" [ngClass]="{'toast-success': toastType === 'success', 'toast-error': toastType === 'error'}">
    <div class="toast-content">
      <div class="toast-icon">
        <svg *ngIf="toastType === 'success'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
        <svg *ngIf="toastType === 'error'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="15" y1="9" x2="9" y2="15"></line>
          <line x1="9" y1="9" x2="15" y2="15"></line>
        </svg>
      </div>
      <div class="toast-message">{{ toastMessage }}</div>
    </div>
  </div>
</div>

<!-- Cancellation Modal -->
<div class="cancellation-modal" *ngIf="showCancellationModal">
  <div class="cancellation-modal-content">
    <!-- Close button -->
    <button class="close-modal" (click)="closeCancellationModal()">
      <i class="fa fa-times" aria-hidden="true"></i>
    </button>

    <!-- Modal Content -->
    <div class="cancellation-content">
      <h2 class="modal-title">Cancel your booking</h2>

      <div *ngIf="cancellationLoading" class="text-center">
        <div class="spinner-border text-primary" role="status"></div>
        <p>Calculating refund amount...</p>
      </div>

      <div *ngIf="!cancellationLoading">
        <div class="booking-summary">
          <div *ngIf="selectedBooking?.property">
            <h3>{{ selectedBooking?.property?.title || 'Booking' }}</h3>
            <p>{{ formatDateRange(selectedBooking?.startDate, selectedBooking?.endDate) }}</p>
          </div>
        </div>

        <div class="policy-info">
          <div *ngIf="refundInfo?.policyName !== 'Unknown'; else unknownPolicy">
            <h4>Cancellation Policy: {{ refundInfo?.policyName }}</h4>
            <p>{{ refundInfo?.policyDescription }}</p>
          </div>

          <ng-template #unknownPolicy>
            <div class="policy-info-box">
              <h4>Cancellation Information</h4>
              <p>{{ refundInfo?.policyDescription || 'This booking may be eligible for a refund based on the property\'s
                cancellation policy. If you proceed with cancellation, any applicable refund will be processed
                automatically.' }}</p>
              <div class="info-message">
                <svg xmlns="http://www.w3.org/2000/svg" class="info-icon" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>The system will automatically apply the property's cancellation policy when you cancel.</span>
              </div>
            </div>
          </ng-template>

          <div class="refund-details" *ngIf="refundInfo">
            <div *ngIf="refundInfo?.isEligibleForRefund === true; else noRefund">
              <p>
                <span class="highlight">{{ refundInfo?.daysUntilCheckIn || 0 }}</span> days until check-in
              </p>
              <p class="refund-amount">
                <span class="highlight">{{ refundInfo?.refundPercentage || 0 }}%</span> refund:
                <span class="money">${{ (refundInfo?.refundAmount || 0).toFixed(2) }}</span>
              </p>
            </div>

            <ng-template #noRefund>
              <p class="no-refund">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                No refund available
              </p>
              <p>Based on the {{ refundInfo?.policyName !== 'Unknown' ? (refundInfo?.policyName || 'standard') + '
                policy' : 'booking terms' }} and your cancellation timing, you are not eligible for a refund.</p>
            </ng-template>
          </div>
        </div>

        <div class="confirmation-question">
          <p>Are you sure you want to cancel this booking?</p>

          <div class="action-buttons">
            <button class="cancel-action-btn secondary" (click)="closeCancellationModal()">
              Keep reservation
            </button>
            <button class="cancel-action-btn primary" (click)="confirmCancellation()">
              Yes, cancel booking
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>