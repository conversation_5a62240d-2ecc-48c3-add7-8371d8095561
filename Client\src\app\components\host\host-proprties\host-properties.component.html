<!-- Header -->
<!-- Main Content -->
<div class="container py-4">
  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="alert alert-danger" role="alert">
    {{ error }}
  </div>

  <!-- No Properties State -->
  <div *ngIf="!loading && !error && filteredProperties.length === 0" class="empty-state">
    <div class="empty-state-content">
      <!-- Empty State for Active Properties -->
      <div *ngIf="status === 'active'">
        <i class="bi bi-house-door display-1"></i>
        <h2>No Active listings yet</h2>
        <p>Start creating your first property listing</p>
        <button class="btn btn-primary mt-3" routerLink="/host/add-property">
          Create your first listing
        </button>
      </div>

      <!-- Empty State for Pending Properties -->
      <div *ngIf="status === 'pending'">
        <i class="bi bi-clock-history display-1"></i>
        <h2>No pending listings</h2>
        <p>No properties pending approval</p>
      </div>
    </div>
  </div>

  <!-- Properties Grid -->
  <div *ngIf="!loading && !error && filteredProperties.length > 0" class="property-grid">
    <div *ngFor="let property of filteredProperties" class="property-card">
      <!-- Image Container -->
      <div class="image-container"
           
       >
        <img [src]="getPropertyImage(property)" 
             [alt]="property?.title || 'Property Image'"
             class="property-image"
             (error)="handleImageError(property)">
        
        <!-- Navigation Arrows -->
        <button class="arrow left-arrow" 
             (click)="prevImage(property.id, $event)"
             *ngIf="(property?.images?.length ?? 0) > 1">
          <span class="material-icons">chevron_left</span>
        </button>
        <button class="arrow right-arrow" 
             (click)="nextImage(property.id, $event)"
             *ngIf="(property?.images?.length ?? 0) > 1">
          <span class="material-icons">chevron_right</span>
        </button>

        <!-- Status Badge -->
        <div class="status-badge" [class.status-pending]="property.status === 'Pending'">
          {{ property.status }}
        </div>

        <!-- Property Actions -->
        <div class="dropdown position-absolute" style="top: 15px; right: 15px; z-index: 2;">
          <button class="btn btn-light" type="button" data-bs-toggle="dropdown" (click)="$event.stopPropagation()">
            <span class="material-icons">more_horiz</span>
          </button>
          <ul class="dropdown-menu">
            <li>
              <button class="dropdown-item" (click)="editProperty(property.id)">
                <span class="material-icons me-2">edit</span>Edit
              </button>
            </li>
            <!-- <li>
               <button class="dropdown-item" (click)="viewBookingDetails(property.id)">
                <span class="material-icons me-2" style="margin-right: 0px">bookings</span>Bookings
              </button>
            </li> -->

             <li> 
              <button class="dropdown-item" (click)="viewBookingDetails(property.id)">
                <span class="material-icons me-2">history</span>Bookings
              </button>
            </li> 
          </ul>
        </div>

        <!-- Image Dots -->
        <div class="image-dots" *ngIf="(property?.images?.length ?? 0) > 1">
          <span *ngFor="let image of property?.images ?? []; let i = index"
                [class.active]="i === (currentImageIndices[property?.id ?? 0] ?? 0)"></span>
        </div>
      </div>
      
      <!-- Property Info -->
      <div class="property-info">
        <h3 class="property-title">{{ property?.title || 'Untitled Property' }}</h3>
      </div>
    </div>
  </div>
</div> 