/* verification.component.scss */
.verification-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
}

h1 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 32px;
}

h2 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 24px;
}

h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.verification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40px 0;
  border-bottom: 1px solid #e4e4e4;
  cursor: pointer;
  position: relative;

  &:hover {
    background-color: #f7f7f7;
  }
}

.verification-content {
  flex: 1;
}

.verification-title {
  display: flex;
  align-items: center;
  font-size: 19px;
  font-weight: 500;
  margin-bottom: 4px;
}

.verification-description {
  font-size: 17px;
  color: #717171;

  &.success {
    color: #008489;
  }
}

.status-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #008489;
  color: white;
  font-size: 12px;
  margin-left: 8px;

  &.error {
    background-color: #FF5A5F;
  }
}

.chevron {
  font-size: 20px;
  color: #717171;
}

.verification-details {
  margin-top: 16px;
}

.back-button {
  background: none;
  border: none;
  font-size: 16px;
  color: #222222;
  cursor: pointer;
  padding: 8px 0;
  margin-bottom: 16px;

  &:hover {
    text-decoration: underline;
  }
}

.form-group {
  margin-bottom: 24px;

  label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  input,
  select {
    width: 100%;
    padding: 12px;
    border: 1px solid #b0b0b0;
    border-radius: 8px;
    font-size: 16px;

    &:focus {
      outline: none;
      border-color: #008489;
    }
  }

  &.verification-code-input {
    input {
      font-size: 24px;
      text-align: center;
      letter-spacing: 10px;
    }
  }
}

.primary-button {
  width: 100%;
  padding: 14px;
  background-color: #FF5A5F;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #FF3B41;
  }

  &:disabled {
    background-color: #DDDDDD;
    cursor: not-allowed;
  }
}

.text-button {
  background: none;
  border: none;
  color: #008489;
  font-weight: 500;
  cursor: pointer;
  padding: 0;

  &:hover {
    text-decoration: underline;
  }
}

.verification-options {
  margin-top: 24px;
  text-align: center;

  p {
    margin: 8px 0;
    font-size: 14px;
  }
}

.small-text {
  font-size: 12px;
  color: #717171;
  margin-bottom: 16px;
}

.id-verification-section {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
}

.id-upload {
  flex: 1;
}

.upload-area {
  border: 2px dashed #b0b0b0;
  border-radius: 8px;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  &:hover {
    border-color: #008489;
  }

  &.uploaded {
    border-color: #008489;
    border-style: solid;
  }

  label {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #717171;

  .upload-icon {
    font-size: 24px;
  }
}

.file-uploaded {
  color: #008489;
  padding: 8px;
  text-align: center;
  word-break: break-all;
}

/* New styles for image preview */
.image-preview {
  margin-top: 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: contain;
  }
}

.privacy-notice {
  background-color: #F7F7F7;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;

  p {
    color: #717171;
    font-size: 14px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  a {
    color: #222222;
    text-decoration: underline;
    font-weight: 500;
  }
}

.error-message {
  background-color: #ffeeee;
  border: 1px solid #ffcccc;
  color: #FF5A5F;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 14px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #FF5A5F;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.email-sent {
  text-align: center;
  padding: 32px 0;

  .email-sent-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  p {
    margin-bottom: 24px;
    color: #484848;
  }
}

.verification-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: not-allowed;
  z-index: 10;
  border-radius: 4px;
}

.verification-overlay-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.verification-overlay-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #008489;
  color: white;
  font-size: 17px;
}

.verification-overlay-text {
  font-weight: 600;
  color: #008489;
}

.verification-overlay-pending-text {
  font-weight: 600;

  color: #c2853b;
}