using System.ComponentModel.DataAnnotations;

namespace API.Models
{
    public enum ChannelType
    {
        OTA, // Online Travel Agency
        GDS, // Global Distribution System
        Direct,
        Metasearch,
        Corporate,
        Wholesale
    }

    public enum ChannelStatus
    {
        Active,
        Inactive,
        Suspended,
        Testing
    }

    public enum SyncStatus
    {
        Synced,
        Pending,
        Failed,
        Partial
    }

    public class Channel
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        public ChannelType ChannelType { get; set; }
        
        public ChannelStatus Status { get; set; } = ChannelStatus.Active;
        
        [StringLength(500)]
        public string ApiEndpoint { get; set; }
        
        [StringLength(200)]
        public string ApiKey { get; set; }
        
        [StringLength(200)]
        public string Username { get; set; }
        
        [StringLength(200)]
        public string Password { get; set; }
        
        public decimal CommissionRate { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public bool AutoSync { get; set; } = true;
        
        public int SyncIntervalMinutes { get; set; } = 60;
        
        public DateTime LastSyncAt { get; set; }
        
        [StringLength(1000)]
        public string Configuration { get; set; } // JSON configuration
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual ICollection<PropertyChannel> PropertyChannels { get; set; } = new List<PropertyChannel>();
        public virtual ICollection<ChannelBooking> ChannelBookings { get; set; } = new List<ChannelBooking>();
        public virtual ICollection<ChannelRate> ChannelRates { get; set; } = new List<ChannelRate>();
        public virtual ICollection<ChannelInventory> ChannelInventories { get; set; } = new List<ChannelInventory>();
        public virtual ICollection<ChannelSyncLog> SyncLogs { get; set; } = new List<ChannelSyncLog>();
    }

    public class PropertyChannel
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        public int ChannelId { get; set; }
        
        [StringLength(100)]
        public string ChannelPropertyId { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public decimal CommissionOverride { get; set; }
        
        public bool SyncRates { get; set; } = true;
        
        public bool SyncInventory { get; set; } = true;
        
        public bool SyncRestrictions { get; set; } = true;
        
        public DateTime ConnectedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime LastSyncAt { get; set; }
        
        public SyncStatus LastSyncStatus { get; set; } = SyncStatus.Pending;

        // Navigation Properties
        public virtual Property Property { get; set; }
        public virtual Channel Channel { get; set; }
        public virtual ICollection<RoomChannelMapping> RoomChannelMappings { get; set; } = new List<RoomChannelMapping>();
    }

    public class RoomChannelMapping
    {
        public int Id { get; set; }
        public int PropertyChannelId { get; set; }
        public int RoomId { get; set; }
        
        [StringLength(100)]
        public string ChannelRoomId { get; set; }
        
        [StringLength(200)]
        public string ChannelRoomName { get; set; }
        
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual PropertyChannel PropertyChannel { get; set; }
        public virtual Room Room { get; set; }
    }

    public class ChannelRate
    {
        public int Id { get; set; }
        public int ChannelId { get; set; }
        public int PropertyId { get; set; }
        public int? RoomId { get; set; }
        
        public DateTime Date { get; set; }
        
        public decimal Rate { get; set; }
        
        public decimal ChannelRate_Value { get; set; } // Rate sent to channel (may include markup/discount)
        
        public bool IsActive { get; set; } = true;
        
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        
        public SyncStatus SyncStatus { get; set; } = SyncStatus.Pending;
        
        public DateTime? SyncedAt { get; set; }

        // Navigation Properties
        public virtual Channel Channel { get; set; }
        public virtual Property Property { get; set; }
        public virtual Room Room { get; set; }
    }

    public class ChannelInventory
    {
        public int Id { get; set; }
        public int ChannelId { get; set; }
        public int PropertyId { get; set; }
        public int? RoomId { get; set; }
        
        public DateTime Date { get; set; }
        
        public int AvailableRooms { get; set; }
        
        public int ChannelAllotment { get; set; }
        
        public int MinStay { get; set; } = 1;
        
        public int MaxStay { get; set; } = 30;
        
        public bool ClosedToArrival { get; set; }
        
        public bool ClosedToDeparture { get; set; }
        
        public bool IsClosed { get; set; }
        
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        
        public SyncStatus SyncStatus { get; set; } = SyncStatus.Pending;
        
        public DateTime? SyncedAt { get; set; }

        // Navigation Properties
        public virtual Channel Channel { get; set; }
        public virtual Property Property { get; set; }
        public virtual Room Room { get; set; }
    }

    public class ChannelBooking
    {
        public int Id { get; set; }
        public int ChannelId { get; set; }
        public int BookingId { get; set; }
        
        [StringLength(100)]
        public string ChannelBookingId { get; set; }
        
        [StringLength(100)]
        public string ChannelConfirmationNumber { get; set; }
        
        public decimal ChannelCommission { get; set; }
        
        public decimal NetRate { get; set; }
        
        public DateTime BookingDate { get; set; }
        
        [StringLength(1000)]
        public string ChannelBookingData { get; set; } // JSON data from channel
        
        public SyncStatus SyncStatus { get; set; } = SyncStatus.Synced;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Channel Channel { get; set; }
        public virtual Booking Booking { get; set; }
    }

    public class ChannelSyncLog
    {
        public int Id { get; set; }
        public int ChannelId { get; set; }
        public int? PropertyId { get; set; }
        
        [StringLength(100)]
        public string SyncType { get; set; } // Rates, Inventory, Bookings, etc.
        
        public SyncStatus Status { get; set; }
        
        public DateTime StartTime { get; set; }
        
        public DateTime? EndTime { get; set; }
        
        public int RecordsProcessed { get; set; }
        
        public int RecordsSuccessful { get; set; }
        
        public int RecordsFailed { get; set; }
        
        [StringLength(2000)]
        public string ErrorMessage { get; set; }
        
        [StringLength(4000)]
        public string Details { get; set; } // JSON details

        // Navigation Properties
        public virtual Channel Channel { get; set; }
        public virtual Property Property { get; set; }
    }

    public class RateParity
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        public int? RoomId { get; set; }
        
        public DateTime Date { get; set; }
        
        public decimal BaseRate { get; set; }
        
        [StringLength(1000)]
        public string ChannelRates { get; set; } // JSON with rates per channel
        
        public bool HasParity { get; set; }
        
        public decimal MaxDeviation { get; set; }
        
        public decimal MinDeviation { get; set; }
        
        [StringLength(500)]
        public string ParityIssues { get; set; }
        
        public DateTime CheckedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Property Property { get; set; }
        public virtual Room Room { get; set; }
    }

    public class ChannelPerformance
    {
        public int Id { get; set; }
        public int ChannelId { get; set; }
        public int PropertyId { get; set; }
        
        public DateTime Date { get; set; }
        
        public int BookingsCount { get; set; }
        
        public decimal Revenue { get; set; }
        
        public decimal Commission { get; set; }
        
        public decimal NetRevenue { get; set; }
        
        public int RoomNights { get; set; }
        
        public decimal ADR { get; set; } // Average Daily Rate
        
        public decimal ConversionRate { get; set; }
        
        public int CancellationsCount { get; set; }
        
        public decimal CancellationRate { get; set; }

        // Navigation Properties
        public virtual Channel Channel { get; set; }
        public virtual Property Property { get; set; }
    }
}
