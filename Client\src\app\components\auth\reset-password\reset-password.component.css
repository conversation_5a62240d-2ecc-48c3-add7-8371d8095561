.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 2rem;
}

.login-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 2.5rem;
    width: 100%;
    max-width: 450px;
}

h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #222;
    margin-bottom: 0.5rem;
}

h2 {
    font-size: 1.1rem;
    font-weight: 400;
    color: #717171;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.25rem;
}

label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #222;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #ff5a5f;
}

.btn-primary {
    display: block;
    width: 100%;
    padding: 0.75rem;
    background-color: #ff5a5f;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-top: 1rem;
}

.btn-primary:hover {
    background-color: #ff4146;
}

.btn-primary:disabled {
    background-color: #ffacaf;
    cursor: not-allowed;
}

.alert {
    padding: 0.75rem;
    border-radius: 8px;
    margin: 1rem 0;
    font-size: 0.9rem;
}

.alert-danger {
    background-color: #ffd2d3;
    color: #d32f2f;
    border: 1px solid #ffbdbd;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.error-message {
    color: #d32f2f;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.password-input-container {
    position: relative;
}

.password-toggle-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #717171;
    cursor: pointer;
}

.loading,
.invalid-token {
    text-align: center;
    padding: 2rem 0;
}

.error-text {
    color: #d32f2f;
    margin-bottom: 1.5rem;
}

.btn-link {
    background: none;
    border: none;
    color: #ff5a5f;
    text-decoration: underline;
    cursor: pointer;
    padding: 0;
    font-size: 0.9rem;
}