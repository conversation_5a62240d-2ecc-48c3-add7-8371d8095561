.booking-details-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;

  .loading-spinner {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
  }

  .error-message {
    color: #f44336;
    text-align: center;
    margin: 2rem 0;
    padding: 1rem;
    background-color: #ffebee;
    border-radius: 4px;
  }

  .booking-content {
    h2 {
      margin-bottom: 2rem;
      color: #333;
      text-align: center;
    }

    .booking-card {
      margin-bottom: 2rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      mat-card-header {
        margin-bottom: 1rem;
        
        mat-card-title {
          color: #1976d2;
          font-size: 1.2rem;
        }
      }

      mat-card-content {
        p {
          margin: 0.5rem 0;
          line-height: 1.5;
        }
      }
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      margin-top: 2rem;
      padding: 1.5rem;
      border-radius: 4px;
      background-color: #f5f5f5;

      .status-message {
        text-align: center;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 500;
      }

      .pending-actions {
        text-align: center;
        
        .status-message {
          color: #ff9800;
        }
        
        button {
          min-width: 200px;
          padding: 0.5rem 1.5rem;
          font-size: 1.1rem;
        }
      }
      
      .confirmed-actions {
        text-align: center;
        
        .status-message {
          color: #4caf50;
        }
      }
      
      .cancelled-actions {
        text-align: center;
        
        .status-message {
          color: #f44336;
        }
      }
    }
  }
}

.status-pending {
  color: #ff9800;
  font-weight: 500;
}

.status-confirmed {
  color: #4caf50;
  font-weight: 500;
}

.status-cancelled {
  color: #f44336;
  font-weight: 500;
} 