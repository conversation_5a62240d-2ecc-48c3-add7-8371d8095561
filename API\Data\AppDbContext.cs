﻿using API.Data.Configurations;
using API.Models;
using Microsoft.EntityFrameworkCore;

namespace API.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options) { }
        public AppDbContext() { }

        public DbSet<User> Users { get; set; }
        public DbSet<Property> Properties { get; set; }
        public DbSet<Booking> Bookings { get; set; }
        public DbSet<Review> Reviews { get; set; }
        public DbSet<Amenity> Amenities { get; set; }
        public DbSet<PropertyImage> PropertyImages { get; set; }
        public DbSet<CancellationPolicy> CancellationPolicies { get; set; }
        public DbSet<Message> Messages { get; set; }
        public DbSet<Favourite> Favourites { get; set; }
        public DbSet<Conversation> Conversations { get; set; }
        public DbSet<BookingPayment> BookingPayments { get; set; }
        public DbSet<HostVerification> HostVerifications { get; set; }
        public DbSet<Promotion> Promotions { get; set; }
        public DbSet<UserUsedPromotion> UserUsedPromotions { get; set; }
        public DbSet<PropertyCategory> PropertyCategories { get; set; }
        public DbSet<PropertyAvailability> PropertyAvailabilities { get; set; }
        //public DbSet<Models.Host> Hosts { get; set; }
        public DbSet<HostPayout> HostPayouts { get; set; }
        public DbSet<Violation> Violations { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<BookingPayout> BookingPayouts { get; set; }



        public DbSet<Models.Host> HostProfules { get; set; }
        public DbSet<VwPropertyDetails> VwPropertyDetails { get; set; }
        public DbSet<VwHostPerformance> VwHostPerformance { get; set; }
        public DbSet<VwActivePromotions> VwActivePromotions { get; set; }

        // Room Management
        public DbSet<Room> Rooms { get; set; }
        public DbSet<RoomAmenity> RoomAmenities { get; set; }
        public DbSet<RoomImage> RoomImages { get; set; }
        public DbSet<RoomAvailability> RoomAvailabilities { get; set; }
        public DbSet<RoomBooking> RoomBookings { get; set; }

        // Corporate Booking
        public DbSet<Company> Companies { get; set; }
        public DbSet<CorporateUser> CorporateUsers { get; set; }
        public DbSet<CorporateRate> CorporateRates { get; set; }
        public DbSet<CorporateBooking> CorporateBookings { get; set; }
        public DbSet<BookingApproval> BookingApprovals { get; set; }
        public DbSet<ExpenseReport> ExpenseReports { get; set; }
        public DbSet<ExpenseReportItem> ExpenseReportItems { get; set; }

        // Dynamic Pricing
        public DbSet<PricingRule> PricingRules { get; set; }
        public DbSet<PricingRuleCondition> PricingRuleConditions { get; set; }
        public DbSet<SeasonalPricing> SeasonalPricings { get; set; }
        public DbSet<EventPricing> EventPricings { get; set; }
        public DbSet<CompetitorPricing> CompetitorPricings { get; set; }
        public DbSet<DemandForecast> DemandForecasts { get; set; }
        public DbSet<PricingStrategy_Model> PricingStrategies { get; set; }
        public DbSet<PriceHistory> PriceHistories { get; set; }

        // Housekeeping & Maintenance
        public DbSet<Staff> Staff { get; set; }
        public DbSet<HousekeepingTask> HousekeepingTasks { get; set; }
        public DbSet<TaskChecklistItem> TaskChecklistItems { get; set; }
        public DbSet<TaskImage> TaskImages { get; set; }
        public DbSet<RoomMaintenance> RoomMaintenances { get; set; }
        public DbSet<MaintenanceImage> MaintenanceImages { get; set; }
        public DbSet<StaffSchedule> StaffSchedules { get; set; }
        public DbSet<InventoryItem> InventoryItems { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }

        // Channel Management
        public DbSet<Channel> Channels { get; set; }
        public DbSet<PropertyChannel> PropertyChannels { get; set; }
        public DbSet<RoomChannelMapping> RoomChannelMappings { get; set; }
        public DbSet<ChannelRate> ChannelRates { get; set; }
        public DbSet<ChannelInventory> ChannelInventories { get; set; }
        public DbSet<ChannelBooking> ChannelBookings { get; set; }
        public DbSet<ChannelSyncLog> ChannelSyncLogs { get; set; }
        public DbSet<RateParity> RateParities { get; set; }
        public DbSet<ChannelPerformance> ChannelPerformances { get; set; }

        // Experience Marketplace
        public DbSet<ExperienceProvider> ExperienceProviders { get; set; }
        public DbSet<Experience> Experiences { get; set; }
        public DbSet<ExperienceImage> ExperienceImages { get; set; }
        public DbSet<ExperienceProviderImage> ExperienceProviderImages { get; set; }
        public DbSet<ExperienceAvailability> ExperienceAvailabilities { get; set; }
        public DbSet<ExperienceBooking> ExperienceBookings { get; set; }
        public DbSet<ExperienceBookingParticipant> ExperienceBookingParticipants { get; set; }
        public DbSet<ExperienceReview> ExperienceReviews { get; set; }
        public DbSet<ExperienceTag> ExperienceTags { get; set; }
        public DbSet<Models.ServiceProvider> ServiceProviders { get; set; }
        public DbSet<ServiceBooking> ServiceBookings { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(AppDbContext).Assembly);

            modelBuilder.Entity<VwPropertyDetails>().HasNoKey().ToView("vw_property_details");
            modelBuilder.Entity<VwHostPerformance>().HasNoKey().ToView("vw_host_performance");
            modelBuilder.Entity<VwActivePromotions>().HasNoKey().ToView("vw_active_promotions");

            // Configure Host-HostPayout relationship
            modelBuilder.Entity<Models.Host>()
                .HasMany(h => h.Payouts)
                .WithOne(p => p.Host)
                .HasForeignKey(p => p.HostId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure Room relationships
            modelBuilder.Entity<RoomAmenity>()
                .HasKey(ra => new { ra.RoomId, ra.AmenityId });

            // Configure Corporate Booking relationships
            modelBuilder.Entity<CorporateBooking>()
                .HasOne(cb => cb.ApprovedByUser)
                .WithMany()
                .HasForeignKey(cb => cb.ApprovedBy)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Channel Management composite keys
            modelBuilder.Entity<ChannelRate>()
                .HasIndex(cr => new { cr.ChannelId, cr.PropertyId, cr.Date, cr.RoomId })
                .IsUnique();

            modelBuilder.Entity<ChannelInventory>()
                .HasIndex(ci => new { ci.ChannelId, ci.PropertyId, ci.Date, ci.RoomId })
                .IsUnique();

            // Configure Experience Tag composite key
            modelBuilder.Entity<ExperienceTag>()
                .HasIndex(et => new { et.ExperienceId, et.Tag })
                .IsUnique();

            // Configure enum conversions
            modelBuilder.Entity<Room>()
                .Property(r => r.RoomType)
                .HasConversion<string>();

            modelBuilder.Entity<Room>()
                .Property(r => r.Status)
                .HasConversion<string>();

            modelBuilder.Entity<Company>()
                .Property(c => c.Status)
                .HasConversion<string>();

            modelBuilder.Entity<Channel>()
                .Property(c => c.ChannelType)
                .HasConversion<string>();

            modelBuilder.Entity<Channel>()
                .Property(c => c.Status)
                .HasConversion<string>();

            modelBuilder.Entity<Experience>()
                .Property(e => e.Category)
                .HasConversion<string>();

            // Configure decimal precision
            modelBuilder.Entity<Room>()
                .Property(r => r.BasePrice)
                .HasPrecision(18, 2);

            modelBuilder.Entity<CorporateRate>()
                .Property(cr => cr.DiscountPercentage)
                .HasPrecision(5, 2);

            modelBuilder.Entity<PricingRule>()
                .Property(pr => pr.MinPriceMultiplier)
                .HasPrecision(5, 2);

            modelBuilder.Entity<PricingRule>()
                .Property(pr => pr.MaxPriceMultiplier)
                .HasPrecision(5, 2);

            modelBuilder.Entity<Experience>()
                .Property(e => e.Price)
                .HasPrecision(18, 2);

            // Configure indexes for performance
            modelBuilder.Entity<RoomAvailability>()
                .HasIndex(ra => new { ra.RoomId, ra.Date });

            modelBuilder.Entity<ExperienceAvailability>()
                .HasIndex(ea => new { ea.ExperienceId, ea.Date });

            modelBuilder.Entity<PriceHistory>()
                .HasIndex(ph => new { ph.PropertyId, ph.Date });

            modelBuilder.Entity<ChannelSyncLog>()
                .HasIndex(csl => new { csl.ChannelId, csl.StartTime });
        }
    }
}
