{"name": "airbnb", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "start:proxy": "ng serve --proxy-config proxy.conf.json"}, "private": true, "dependencies": {"@abacritt/angularx-social-login": "^2.3.0", "@angular/animations": "^19.2.7", "@angular/cdk": "^19.2.8", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/fire": "^19.1.0", "@angular/forms": "^19.2.0", "@angular/google-maps": "^19.2.9", "@angular/material": "^19.2.8", "@angular/platform-browser": "^19.2.7", "@angular/platform-browser-dynamic": "^19.2.7", "@angular/router": "^19.2.7", "@auth0/angular-jwt": "^5.2.0", "@fortawesome/fontawesome-free": "^6.7.2", "@microsoft/signalr": "^8.0.7", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "@stripe/stripe-js": "^4.0.0", "@types/google-one-tap": "^1.2.6", "bootstrap": "^5.3.5", "chart.js": "^4.4.9", "firebase": "^11.6.0", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.0", "stripe": "^18.0.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.8", "@angular/cli": "^19.2.8", "@angular/compiler-cli": "^19.2.7", "@types/chart.js": "^2.9.41", "@types/jasmine": "~5.1.0", "@types/leaflet": "^1.9.17", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}