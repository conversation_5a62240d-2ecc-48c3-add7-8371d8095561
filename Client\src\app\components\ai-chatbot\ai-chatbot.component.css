:root {
  --airbnb-primary: #FF385C;
  --airbnb-secondary: #E31C5F;
  --airbnb-light: #F7F7F7;
  --airbnb-dark: #222222;
  --airbnb-gray: #717171;
  --airbnb-border: #DDDDDD;
}

.chatbot-container {
  position: fixed;
  bottom: 80px;
  right: 20px;
  z-index: 1000;
}

.chatbot-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #ff385c;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s;
}

.chatbot-icon:hover {
  transform: scale(1.1);
}

.chatbot-icon .material-icons {
  color: white;
  font-size: 28px;
}

.chat-window {
  position: fixed;
  bottom: 90px;
  right: 20px;
  width: 350px;
  height: 500px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-window.hidden {
  display: none;
}

.chat-header {
  background-color: #ff385c;
  color: white;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
}

.close-btn:hover {
  opacity: 0.8;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message {
  max-width: 80%;
  margin-bottom: 8px;
}

.message.user-message {
  align-self: flex-end;
}

.message.ai-message {
  align-self: flex-start;
}

.message-bubble {
  padding: 10px;
  border-radius: 12px;
  position: relative;
}

.user-message .message-bubble {
  background-color: #ff385c;
  color: white;
}

.ai-message .message-bubble {
  background-color: #f7f7f7;
  color: #333;
}

.message-sender {
  font-size: 11px;
  margin-bottom: 3px;
  opacity: 0.8;
}

.message-content {
  font-size: 13px;
  line-height: 1.4;
  white-space: pre-wrap;
}

.message-content h1 {
  font-size: 15px;
  font-weight: 600;
  margin: 8px 0 4px 0;
  color: inherit;
}

.message-content h2 {
  font-size: 14px;
  font-weight: 600;
  margin: 8px 0 4px 0;
  color: inherit;
}

.message-content h3 {
  font-size: 13px;
  font-weight: 600;
  margin: 8px 0 4px 0;
  color: inherit;
}

.message-content p {
  margin: 4px 0;
}

.message-content ul {
  margin: 4px 0;
  padding-left: 16px;
}

.message-content li {
  margin: 2px 0;
  padding-left: 4px;
}

.message-timestamp {
  font-size: 9px;
  opacity: 0.7;
  margin-top: 3px;
  text-align: right;
}

.chat-input {
  padding: 16px;
  border-top: 1px solid #eee;
  background-color: white;
}

textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  resize: none;
  font-size: 13px;
  margin-bottom: 8px;
}

textarea:focus {
  outline: none;
  border-color: #ff385c;
}

.audio-controls {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.play-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.play-button .material-icons {
  font-size: 18px;
}

.play-button:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.play-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

.play-button.playing {
  background-color: #e74c3c;
  animation: pulse 1.5s infinite;
}

.play-button.playing:hover {
  background-color: #c0392b;
}

.play-button:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7;
}

.play-text {
  font-weight: 500;
  letter-spacing: 0.5px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

.audio-controls button .material-icons {
  font-size: 16px;
}

.audio-controls button:hover {
  background-color: #45a049;
}

.audio-controls button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.button-group {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.send-btn, .clear-btn, .record-button {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.send-btn {
  background-color: #ff385c;
  color: white;
}

.clear-btn {
  background-color: #f7f7f7;
  color: #666;
}

.record-button {
  background-color: #f44336;
  color: white;
}

.record-button:hover {
  background-color: #d32f2f;
}

.record-button:disabled {
  background-color: #ffcdd2;
  cursor: not-allowed;
}

.send-btn:hover, .clear-btn:hover {
  opacity: 0.9;
}

.send-btn:disabled {
  background-color: #ffa5b5;
  cursor: not-allowed;
}

.input-controls {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  padding: 16px;
}

.dot-flashing {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #ff385c;
  animation: dot-flashing 1s infinite linear alternate;
  animation-delay: 0.5s;
}

.dot-flashing::before, .dot-flashing::after {
  content: '';
  display: inline-block;
  position: absolute;
  top: 0;
}

.dot-flashing::before {
  left: -15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #ff385c;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 0s;
}

.dot-flashing::after {
  left: 15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #ff385c;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 1s;
}

@keyframes dot-flashing {
  0% {
    background-color: #ff385c;
  }
  50%, 100% {
    background-color: rgba(255, 56, 92, 0.2);
  }
} 