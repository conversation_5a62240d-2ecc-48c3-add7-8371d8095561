using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using API.Data;
using API.Models;

namespace API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class DynamicPricingController : ControllerBase
    {
        private readonly AppDbContext _context;

        public DynamicPricingController(AppDbContext context)
        {
            _context = context;
        }

        // GET: api/DynamicPricing/rules/{propertyId}
        [HttpGet("rules/{propertyId}")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<IEnumerable<PricingRule>>> GetPricingRules(int propertyId)
        {
            var rules = await _context.PricingRules
                .Where(pr => pr.PropertyId == propertyId)
                .Include(pr => pr.Conditions_Navigation)
                .OrderBy(pr => pr.Priority)
                .ToListAsync();

            return Ok(rules);
        }

        // POST: api/DynamicPricing/rules
        [HttpPost("rules")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<PricingRule>> CreatePricingRule(PricingRule pricingRule)
        {
            // Verify property ownership
            var property = await _context.Properties.FindAsync(pricingRule.PropertyId);
            if (property == null)
            {
                return NotFound("Property not found");
            }

            pricingRule.CreatedAt = DateTime.UtcNow;
            pricingRule.UpdatedAt = DateTime.UtcNow;

            _context.PricingRules.Add(pricingRule);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetPricingRules), new { propertyId = pricingRule.PropertyId }, pricingRule);
        }

        // PUT: api/DynamicPricing/rules/{id}
        [HttpPut("rules/{id}")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<IActionResult> UpdatePricingRule(int id, PricingRule pricingRule)
        {
            if (id != pricingRule.Id)
            {
                return BadRequest();
            }

            var existingRule = await _context.PricingRules.FindAsync(id);
            if (existingRule == null)
            {
                return NotFound();
            }

            existingRule.Name = pricingRule.Name;
            existingRule.RuleType = pricingRule.RuleType;
            existingRule.IsActive = pricingRule.IsActive;
            existingRule.Priority = pricingRule.Priority;
            existingRule.MinPriceMultiplier = pricingRule.MinPriceMultiplier;
            existingRule.MaxPriceMultiplier = pricingRule.MaxPriceMultiplier;
            existingRule.ValidFrom = pricingRule.ValidFrom;
            existingRule.ValidTo = pricingRule.ValidTo;
            existingRule.Conditions = pricingRule.Conditions;
            existingRule.AdjustmentPercentage = pricingRule.AdjustmentPercentage;
            existingRule.FixedAdjustment = pricingRule.FixedAdjustment;
            existingRule.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return NoContent();
        }

        // DELETE: api/DynamicPricing/rules/{id}
        [HttpDelete("rules/{id}")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<IActionResult> DeletePricingRule(int id)
        {
            var rule = await _context.PricingRules.FindAsync(id);
            if (rule == null)
            {
                return NotFound();
            }

            _context.PricingRules.Remove(rule);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        // GET: api/DynamicPricing/seasonal/{propertyId}
        [HttpGet("seasonal/{propertyId}")]
        public async Task<ActionResult<IEnumerable<SeasonalPricing>>> GetSeasonalPricing(int propertyId)
        {
            var seasonalPricing = await _context.SeasonalPricings
                .Where(sp => sp.PropertyId == propertyId && sp.IsActive)
                .OrderBy(sp => sp.StartDate)
                .ToListAsync();

            return Ok(seasonalPricing);
        }

        // POST: api/DynamicPricing/seasonal
        [HttpPost("seasonal")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<SeasonalPricing>> CreateSeasonalPricing(SeasonalPricing seasonalPricing)
        {
            seasonalPricing.CreatedAt = DateTime.UtcNow;

            _context.SeasonalPricings.Add(seasonalPricing);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetSeasonalPricing), new { propertyId = seasonalPricing.PropertyId }, seasonalPricing);
        }

        // GET: api/DynamicPricing/events/{propertyId}
        [HttpGet("events/{propertyId}")]
        public async Task<ActionResult<IEnumerable<EventPricing>>> GetEventPricing(int propertyId)
        {
            var eventPricing = await _context.EventPricings
                .Where(ep => ep.PropertyId == propertyId && ep.IsActive)
                .Where(ep => ep.EventEndDate >= DateTime.Today)
                .OrderBy(ep => ep.EventStartDate)
                .ToListAsync();

            return Ok(eventPricing);
        }

        // POST: api/DynamicPricing/events
        [HttpPost("events")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<EventPricing>> CreateEventPricing(EventPricing eventPricing)
        {
            eventPricing.CreatedAt = DateTime.UtcNow;

            _context.EventPricings.Add(eventPricing);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetEventPricing), new { propertyId = eventPricing.PropertyId }, eventPricing);
        }

        // GET: api/DynamicPricing/competitors/{propertyId}
        [HttpGet("competitors/{propertyId}")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<IEnumerable<CompetitorPricing>>> GetCompetitorPricing(int propertyId)
        {
            var competitorPricing = await _context.CompetitorPricings
                .Where(cp => cp.PropertyId == propertyId)
                .OrderByDescending(cp => cp.PriceDate)
                .Take(50)
                .ToListAsync();

            return Ok(competitorPricing);
        }

        // POST: api/DynamicPricing/competitors
        [HttpPost("competitors")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<CompetitorPricing>> AddCompetitorPricing(CompetitorPricing competitorPricing)
        {
            competitorPricing.CreatedAt = DateTime.UtcNow;
            competitorPricing.PriceDifference = competitorPricing.OurPrice - competitorPricing.CompetitorPrice;

            _context.CompetitorPricings.Add(competitorPricing);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetCompetitorPricing), new { propertyId = competitorPricing.PropertyId }, competitorPricing);
        }

        // GET: api/DynamicPricing/forecast/{propertyId}
        [HttpGet("forecast/{propertyId}")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<IEnumerable<DemandForecast>>> GetDemandForecast(int propertyId, int days = 30)
        {
            var startDate = DateTime.Today;
            var endDate = startDate.AddDays(days);

            var forecast = await _context.DemandForecasts
                .Where(df => df.PropertyId == propertyId)
                .Where(df => df.ForecastDate >= startDate && df.ForecastDate <= endDate)
                .OrderBy(df => df.ForecastDate)
                .ToListAsync();

            return Ok(forecast);
        }

        // POST: api/DynamicPricing/calculate-price
        [HttpPost("calculate-price")]
        public async Task<ActionResult<decimal>> CalculateOptimalPrice(int propertyId, int? roomId, DateTime date)
        {
            var property = await _context.Properties.FindAsync(propertyId);
            if (property == null)
            {
                return NotFound("Property not found");
            }

            decimal basePrice = property.PricePerNight;
            
            // Get room-specific price if roomId is provided
            if (roomId.HasValue)
            {
                var room = await _context.Rooms.FindAsync(roomId.Value);
                if (room != null)
                {
                    basePrice = room.BasePrice;
                }
            }

            // Apply pricing rules
            var applicableRules = await _context.PricingRules
                .Where(pr => pr.PropertyId == propertyId && pr.IsActive)
                .Where(pr => pr.ValidFrom <= date && pr.ValidTo >= date)
                .OrderBy(pr => pr.Priority)
                .ToListAsync();

            decimal adjustedPrice = basePrice;

            foreach (var rule in applicableRules)
            {
                // Apply rule logic based on type
                switch (rule.RuleType)
                {
                    case PricingRuleType.Seasonal:
                        var seasonalPricing = await _context.SeasonalPricings
                            .FirstOrDefaultAsync(sp => sp.PropertyId == propertyId && 
                                                      sp.StartDate <= date && sp.EndDate >= date && sp.IsActive);
                        if (seasonalPricing != null)
                        {
                            adjustedPrice *= seasonalPricing.PriceMultiplier;
                        }
                        break;

                    case PricingRuleType.EventBased:
                        var eventPricing = await _context.EventPricings
                            .FirstOrDefaultAsync(ep => ep.PropertyId == propertyId && 
                                                      ep.EventStartDate <= date && ep.EventEndDate >= date && ep.IsActive);
                        if (eventPricing != null)
                        {
                            adjustedPrice *= eventPricing.PriceMultiplier;
                        }
                        break;

                    case PricingRuleType.DemandBased:
                        // Calculate occupancy rate for the date
                        var totalRooms = await _context.Rooms.CountAsync(r => r.PropertyId == propertyId);
                        var bookedRooms = await _context.RoomBookings
                            .Where(rb => rb.CheckInDate <= date && rb.CheckOutDate > date)
                            .CountAsync();
                        
                        if (totalRooms > 0)
                        {
                            var occupancyRate = (decimal)bookedRooms / totalRooms;
                            if (occupancyRate > 0.8m) // High demand
                            {
                                adjustedPrice *= 1.2m;
                            }
                            else if (occupancyRate < 0.3m) // Low demand
                            {
                                adjustedPrice *= 0.9m;
                            }
                        }
                        break;

                    case PricingRuleType.LastMinute:
                        var daysInAdvance = (date - DateTime.Today).Days;
                        if (daysInAdvance <= 3)
                        {
                            adjustedPrice *= 0.85m; // 15% discount for last-minute bookings
                        }
                        break;

                    case PricingRuleType.EarlyBird:
                        var daysInAdvanceEarly = (date - DateTime.Today).Days;
                        if (daysInAdvanceEarly >= 30)
                        {
                            adjustedPrice *= 0.9m; // 10% discount for early bookings
                        }
                        break;

                    case PricingRuleType.WeekendPremium:
                        if (date.DayOfWeek == DayOfWeek.Friday || date.DayOfWeek == DayOfWeek.Saturday)
                        {
                            adjustedPrice *= 1.15m; // 15% weekend premium
                        }
                        break;
                }

                // Apply min/max constraints
                if (adjustedPrice < basePrice * rule.MinPriceMultiplier)
                {
                    adjustedPrice = basePrice * rule.MinPriceMultiplier;
                }
                else if (adjustedPrice > basePrice * rule.MaxPriceMultiplier)
                {
                    adjustedPrice = basePrice * rule.MaxPriceMultiplier;
                }
            }

            // Record price history
            var priceHistory = new PriceHistory
            {
                PropertyId = propertyId,
                RoomId = roomId,
                Date = date,
                Price = adjustedPrice,
                BasePrice = basePrice,
                AdjustmentAmount = adjustedPrice - basePrice,
                AdjustmentReason = "Dynamic pricing calculation",
                CreatedAt = DateTime.UtcNow
            };

            _context.PriceHistories.Add(priceHistory);
            await _context.SaveChangesAsync();

            return Ok(Math.Round(adjustedPrice, 2));
        }

        // GET: api/DynamicPricing/strategy/{propertyId}
        [HttpGet("strategy/{propertyId}")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<PricingStrategy_Model>> GetPricingStrategy(int propertyId)
        {
            var strategy = await _context.PricingStrategies
                .FirstOrDefaultAsync(ps => ps.PropertyId == propertyId);

            if (strategy == null)
            {
                // Create default strategy
                strategy = new PricingStrategy_Model
                {
                    PropertyId = propertyId,
                    Strategy = PricingStrategy.Moderate,
                    AutoAdjustPricing = false,
                    LastUpdated = DateTime.UtcNow
                };

                _context.PricingStrategies.Add(strategy);
                await _context.SaveChangesAsync();
            }

            return Ok(strategy);
        }

        // PUT: api/DynamicPricing/strategy/{propertyId}
        [HttpPut("strategy/{propertyId}")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<IActionResult> UpdatePricingStrategy(int propertyId, PricingStrategy_Model strategy)
        {
            var existingStrategy = await _context.PricingStrategies
                .FirstOrDefaultAsync(ps => ps.PropertyId == propertyId);

            if (existingStrategy == null)
            {
                strategy.PropertyId = propertyId;
                strategy.LastUpdated = DateTime.UtcNow;
                _context.PricingStrategies.Add(strategy);
            }
            else
            {
                existingStrategy.Strategy = strategy.Strategy;
                existingStrategy.AutoAdjustPricing = strategy.AutoAdjustPricing;
                existingStrategy.MinOccupancyThreshold = strategy.MinOccupancyThreshold;
                existingStrategy.MaxOccupancyThreshold = strategy.MaxOccupancyThreshold;
                existingStrategy.PriceIncreaseStep = strategy.PriceIncreaseStep;
                existingStrategy.PriceDecreaseStep = strategy.PriceDecreaseStep;
                existingStrategy.DaysInAdvanceThreshold = strategy.DaysInAdvanceThreshold;
                existingStrategy.ConsiderCompetitors = strategy.ConsiderCompetitors;
                existingStrategy.ConsiderEvents = strategy.ConsiderEvents;
                existingStrategy.ConsiderSeasons = strategy.ConsiderSeasons;
                existingStrategy.LastUpdated = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return NoContent();
        }

        // GET: api/DynamicPricing/history/{propertyId}
        [HttpGet("history/{propertyId}")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<IEnumerable<PriceHistory>>> GetPriceHistory(int propertyId, DateTime? startDate = null, DateTime? endDate = null)
        {
            startDate ??= DateTime.Today.AddDays(-30);
            endDate ??= DateTime.Today;

            var history = await _context.PriceHistories
                .Where(ph => ph.PropertyId == propertyId)
                .Where(ph => ph.Date >= startDate && ph.Date <= endDate)
                .OrderBy(ph => ph.Date)
                .ToListAsync();

            return Ok(history);
        }
    }
}
