<app-main-navbar></app-main-navbar>
<div class="login-container">
    <div class="login-card">
        <h1>Reset Password</h1>
        <h2>Create a new password</h2>

        <div *ngIf="isLoading" class="loading">
            <p>Validating your reset link...</p>
        </div>

        <div *ngIf="!isLoading && !isTokenValid" class="invalid-token">
            <p class="error-text">{{ errorMessage }}</p>
            <button class="btn-primary" (click)="goToLogin()">Back to Login</button>
        </div>

        <div *ngIf="!isLoading && isTokenValid">
            <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()">
                <div class="form-group">
                    <label for="newPassword">New Password</label>
                    <div class="password-input-container">
                        <input [type]="showPassword ? 'text' : 'password'" id="newPassword"
                            formControlName="newPassword" class="form-control" placeholder="Enter your new password">
                        <button type="button" class="password-toggle-btn" (click)="togglePasswordVisibility('password')"
                            [attr.aria-label]="showPassword ? 'Hide password' : 'Show password'">
                            <i class="fa" [ngClass]="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
                        </button>
                    </div>
                    <div *ngIf="resetPasswordForm.get('newPassword')?.invalid && resetPasswordForm.get('newPassword')?.touched"
                        class="error-message">
                        <span *ngIf="resetPasswordForm.get('newPassword')?.errors?.['required']">Password is
                            required</span>
                        <span *ngIf="resetPasswordForm.get('newPassword')?.errors?.['minlength']">
                            Password must be at least 6 characters long
                        </span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">Confirm Password</label>
                    <div class="password-input-container">
                        <input [type]="showConfirmPassword ? 'text' : 'password'" id="confirmPassword"
                            formControlName="confirmPassword" class="form-control"
                            placeholder="Confirm your new password">
                        <button type="button" class="password-toggle-btn"
                            (click)="togglePasswordVisibility('confirmPassword')"
                            [attr.aria-label]="showConfirmPassword ? 'Hide password' : 'Show password'">
                            <i class="fa" [ngClass]="showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
                        </button>
                    </div>
                    <div *ngIf="resetPasswordForm.get('confirmPassword')?.invalid && resetPasswordForm.get('confirmPassword')?.touched"
                        class="error-message">
                        <span *ngIf="resetPasswordForm.get('confirmPassword')?.errors?.['required']">
                            Confirm password is required
                        </span>
                        <span *ngIf="resetPasswordForm.get('confirmPassword')?.errors?.['mismatch']">
                            Passwords do not match
                        </span>
                    </div>
                </div>

                <button type="submit" class="btn-primary" [disabled]="resetPasswordForm.invalid || isSubmitting">
                    {{ isSubmitting ? 'Resetting...' : 'Reset Password' }}
                </button>
            </form>

            <div *ngIf="errorMessage && !isSubmitting" class="alert alert-danger">
                {{ errorMessage }}
            </div>

            <div *ngIf="successMessage" class="alert alert-success">
                {{ successMessage }}
            </div>
        </div>
    </div>
</div>