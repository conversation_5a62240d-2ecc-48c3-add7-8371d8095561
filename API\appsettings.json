{"ConnectionStrings": {"DefaultConnection": "Server=HOSSEINPC;Database=HotelMarketplace;Trusted_Connection=True;TrustServerCertificate=True;Integrated Security=True;", "RedisConnection": "localhost:6379", "ElasticsearchConnection": "http://localhost:9200"}, "Jwt": {"Key": "your-secret-key-here-make-it-long-and-secure-for-hotel-marketplace", "Issuer": "hotel-marketplace-api", "Audience": "hotel-marketplace-client", "ExpiryMinutes": 60, "RefreshTokenExpiryDays": 7}, "Stripe": {"SecretKey": "sk_test_your_stripe_secret_key", "PublicKey": "pk_test_51RGtmjRmZNI84H1fLpWKyawf8I15oU6VHUwAaOYQ1oK8WXGmUrIkYB4dSc4rp04ostgODlFu3cCb1w5gdDAdx1cq00xijE1SNL", "WebhookSecret": "whsec_your_webhook_secret", "ConnectClientId": "ca_your_connect_client_id"}, "PaymentGateways": {"PayPal": {"ClientId": "your_paypal_client_id", "ClientSecret": "your_paypal_client_secret", "Environment": "sandbox"}, "Square": {"ApplicationId": "your_square_app_id", "AccessToken": "your_square_access_token"}}, "CloudServices": {"AWS": {"AccessKey": "your_aws_access_key", "SecretKey": "your_aws_secret_key", "S3Bucket": "hotel-marketplace-assets", "Region": "us-east-1"}, "Azure": {"StorageConnectionString": "your_azure_storage_connection", "BlobContainer": "property-images"}}, "ExternalAPIs": {"GoogleMaps": {"ApiKey": "your_google_maps_api_key"}, "Weather": {"ApiKey": "your_weather_api_key"}, "CurrencyExchange": {"ApiKey": "your_currency_api_key"}, "ChannelManager": {"BookingCom": {"Username": "your_booking_username", "Password": "your_booking_password"}, "Expedia": {"ApiKey": "your_expedia_api_key"}}}, "AI": {"OpenAI": {"ApiKey": "your-openai-api-key", "Model": "gpt-4"}, "Azure": {"CognitiveServicesKey": "your_cognitive_services_key", "TextAnalyticsEndpoint": "your_text_analytics_endpoint"}}, "Messaging": {"Twilio": {"AccountSid": "your_twilio_account_sid", "AuthToken": "your_twilio_auth_token", "PhoneNumber": "your_twilio_phone_number"}, "SendGrid": {"ApiKey": "your_sendgrid_api_key", "FromEmail": "<EMAIL>"}}, "Authentication": {"Google": {"ClientId": "*************-qt40so8h1a59955ra6huff4f835hn315.apps.googleusercontent.com", "ClientSecret": "dummy-secret-for-development"}, "Microsoft": {"ClientId": "your_microsoft_client_id", "ClientSecret": "your_microsoft_client_secret"}, "Facebook": {"AppId": "your_facebook_app_id", "AppSecret": "your_facebook_app_secret"}}, "EmailSettings": {"SmtpHost": "smtp.gmail.com", "SmtpPort": "587", "SmtpUsername": "<EMAIL>", "SmtpPassword": "password", "FromEmail": "<EMAIL>", "FromName": "Hotel Marketplace"}, "Caching": {"Redis": {"DefaultExpiry": "01:00:00", "SlidingExpiry": "00:30:00"}}, "RateLimiting": {"GeneralLimit": "100", "AuthLimit": "10", "SearchLimit": "50", "BookingLimit": "20"}, "Monitoring": {"ApplicationInsights": {"InstrumentationKey": "your_app_insights_key"}, "Serilog": {"MinimumLevel": "Information", "WriteTo": ["<PERSON><PERSON><PERSON>", "File", "ApplicationInsights"]}}, "Security": {"EncryptionKey": "your_encryption_key", "AllowedOrigins": ["https://localhost:4200", "https://yourdomain.com"], "RequireHttps": true, "EnableCors": true}, "Features": {"EnableChannelManager": true, "EnableDynamicPricing": true, "EnableAIRecommendations": true, "EnableMultiCurrency": true, "EnableCorporateBooking": true, "EnableMultiProperty": true, "EnableRoomManagement": true}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*"}