{"ConnectionStrings": {"DefaultConnection": "Server=HOSSEINPC;Database=Airbnb_clone;Trusted_Connection=True;TrustServerCertificate=True;Integrated Security=True;"}, "Jwt": {"Key": "your-secret-key-here-make-it-long-and-secure", "Issuer": "airbnb-clone-api", "Audience": "airbnb-clone-client"}, "Stripe": {"SecretKey": "sk_test_your_stripe_secret_key", "PublicKey": "pk_test_51RGtmjRmZNI84H1fLpWKyawf8I15oU6VHUwAaOYQ1oK8WXGmUrIkYB4dSc4rp04ostgODlFu3cCb1w5gdDAdx1cq00xijE1SNL"}, "OpenAI": {"ApiKey": "your-openai-api-key"}, "Authentication": {"Google": {"ClientId": "1031756027306-qt40so8h1a59955ra6huff4f835hn315.apps.googleusercontent.com", "ClientSecret": "dummy-secret-for-development"}}, "EmailSettings": {"SmtpHost": "smtp.gmail.com", "SmtpPort": "587", "SmtpUsername": "<EMAIL>", "SmtpPassword": "password", "FromEmail": "<EMAIL>", "FromName": "Airbnb Clone"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}