<div class="container py-4">
  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-5">
    <mat-spinner diameter="50"></mat-spinner>
    <p class="mt-3">Loading booking details...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="alert alert-danger" role="alert">
    {{ error }}
  </div>

  <!-- Content -->
  <div *ngIf="!loading && !error" class="booking-details-container">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1 class="mb-0">Property Booking Details</h1>
      <button mat-raised-button color="primary" (click)="goBack()">
        <mat-icon>arrow_back</mat-icon> Back to Properties
      </button>
    </div>

    <!-- Analytics Summary Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <mat-card class="analytics-card">
          <mat-card-content>
            <div class="analytics-icon">
              <mat-icon>calendar_today</mat-icon>
            </div>
            <div class="analytics-value">{{ totalBookings }}</div>
            <div class="analytics-label">Total Bookings</div>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="col-md-3">
        <mat-card class="analytics-card">
          <mat-card-content>
            <div class="analytics-icon">
              <mat-icon>attach_money</mat-icon>
            </div>
            <div class="analytics-value">{{ formatCurrency(totalRevenue) }}</div>
            <div class="analytics-label">Total Revenue</div>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="col-md-3">
        <mat-card class="analytics-card">
          <mat-card-content>
            <div class="analytics-icon">
              <mat-icon>date_range</mat-icon>
            </div>
            <div class="analytics-value">{{ averageBookingDuration.toFixed(1) }}</div>
            <div class="analytics-label">Avg. Booking Duration (days)</div>
          </mat-card-content>
        </mat-card>
      </div>
      <div class="col-md-3">
        <mat-card class="analytics-card">
          <mat-card-content>
            <div class="analytics-icon">
              <mat-icon>payments</mat-icon>
            </div>
            <div class="analytics-value">{{ formatCurrency(averageRevenuePerBooking) }}</div>
            <div class="analytics-label">Avg. Revenue per Booking</div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <!-- Booking Status Distribution -->
    <mat-card class="mb-4">
      <mat-card-header>
        <mat-card-title>Booking Status Distribution</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="status-chips">
          <mat-chip-listbox>
            <mat-chip *ngFor="let status of bookingStatusCounts | keyvalue" 
                      [color]="getStatusColor(status.key)" 
                      selected>
              {{ status.key }}: {{ status.value }}
            </mat-chip>
          </mat-chip-listbox>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Monthly Bookings and Revenue -->
    <mat-card class="mb-4">
      <mat-card-header>
        <mat-card-title>Monthly Bookings and Revenue</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="table-responsive">
          <table mat-table [dataSource]="monthlyBookings | keyvalue" class="w-100">
            <ng-container matColumnDef="month">
              <th mat-header-cell *matHeaderCellDef>Month</th>
              <td mat-cell *matCellDef="let item">{{ item.key }}</td>
            </ng-container>

            <ng-container matColumnDef="bookings">
              <th mat-header-cell *matHeaderCellDef>Bookings</th>
              <td mat-cell *matCellDef="let item">{{ item.value }}</td>
            </ng-container>

            <ng-container matColumnDef="revenue">
              <th mat-header-cell *matHeaderCellDef>Revenue</th>
              <td mat-cell *matCellDef="let item">{{ formatCurrency(monthlyRevenue[item.key]) }}</td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="['month', 'bookings', 'revenue']"></tr>
            <tr mat-row *matRowDef="let row; columns: ['month', 'bookings', 'revenue'];"></tr>
          </table>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Bookings Table -->
    <mat-card>
      <mat-card-header>
        <mat-card-title>All Bookings</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="table-responsive">
          <table mat-table [dataSource]="bookings" class="w-100">
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef>ID</th>
              <td mat-cell *matCellDef="let booking">{{ booking.id }}</td>
            </ng-container>

            <ng-container matColumnDef="guestName">
              <th mat-header-cell *matHeaderCellDef>Guest</th>
              <td mat-cell *matCellDef="let booking">{{ booking.guestName }}</td>
            </ng-container>

            <ng-container matColumnDef="startDate">
              <th mat-header-cell *matHeaderCellDef>Start Date</th>
              <td mat-cell *matCellDef="let booking">{{ formatDate(booking.startDate) }}</td>
            </ng-container>

            <ng-container matColumnDef="endDate">
              <th mat-header-cell *matHeaderCellDef>End Date</th>
              <td mat-cell *matCellDef="let booking">{{ formatDate(booking.endDate) }}</td>
            </ng-container>

            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let booking">
                <mat-chip [color]="getStatusColor(booking.status)" selected>
                  {{ booking.status }}
                </mat-chip>
              </td>
            </ng-container>

            <ng-container matColumnDef="totalAmount">
              <th mat-header-cell *matHeaderCellDef>Amount</th>
              <td mat-cell *matCellDef="let booking">{{ formatCurrency(booking.totalAmount) }}</td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="['id', 'guestName', 'startDate', 'endDate', 'status', 'totalAmount']"></tr>
            <tr mat-row *matRowDef="let row; columns: ['id', 'guestName', 'startDate', 'endDate', 'status', 'totalAmount'];"></tr>
          </table>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div> 