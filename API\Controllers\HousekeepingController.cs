using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using API.Data;
using API.Models;

namespace API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class HousekeepingController : ControllerBase
    {
        private readonly AppDbContext _context;

        public HousekeepingController(AppDbContext context)
        {
            _context = context;
        }

        // GET: api/Housekeeping/tasks
        [HttpGet("tasks")]
        [Authorize(Roles = "Host,Admin,Staff")]
        public async Task<ActionResult<IEnumerable<HousekeepingTask>>> GetTasks(
            int? propertyId = null,
            int? roomId = null,
            Models.TaskStatus? status = null,
            TaskType? taskType = null,
            int? assignedStaffId = null,
            DateTime? scheduledDate = null)
        {
            var query = _context.HousekeepingTasks.AsQueryable();

            if (propertyId.HasValue)
                query = query.Where(t => t.PropertyId == propertyId.Value);

            if (roomId.HasValue)
                query = query.Where(t => t.RoomId == roomId.Value);

            if (status.HasValue)
                query = query.Where(t => t.Status == status.Value);

            if (taskType.HasValue)
                query = query.Where(t => t.TaskType == taskType.Value);

            if (assignedStaffId.HasValue)
                query = query.Where(t => t.AssignedStaffId == assignedStaffId.Value);

            if (scheduledDate.HasValue)
                query = query.Where(t => t.ScheduledDate.Date == scheduledDate.Value.Date);

            var tasks = await query
                .Include(t => t.Property)
                .Include(t => t.Room)
                .Include(t => t.AssignedStaff)
                .Include(t => t.ChecklistItems)
                .OrderBy(t => t.ScheduledDate)
                .ThenBy(t => t.Priority)
                .ToListAsync();

            return Ok(tasks);
        }

        // GET: api/Housekeeping/tasks/{id}
        [HttpGet("tasks/{id}")]
        [Authorize(Roles = "Host,Admin,Staff")]
        public async Task<ActionResult<HousekeepingTask>> GetTask(int id)
        {
            var task = await _context.HousekeepingTasks
                .Include(t => t.Property)
                .Include(t => t.Room)
                .Include(t => t.AssignedStaff)
                .Include(t => t.ChecklistItems)
                .Include(t => t.TaskImages)
                .FirstOrDefaultAsync(t => t.Id == id);

            if (task == null)
            {
                return NotFound();
            }

            return Ok(task);
        }

        // POST: api/Housekeeping/tasks
        [HttpPost("tasks")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<HousekeepingTask>> CreateTask(HousekeepingTask task)
        {
            task.CreatedAt = DateTime.UtcNow;
            task.UpdatedAt = DateTime.UtcNow;

            _context.HousekeepingTasks.Add(task);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetTask), new { id = task.Id }, task);
        }

        // PUT: api/Housekeeping/tasks/{id}
        [HttpPut("tasks/{id}")]
        [Authorize(Roles = "Host,Admin,Staff")]
        public async Task<IActionResult> UpdateTask(int id, HousekeepingTask task)
        {
            if (id != task.Id)
            {
                return BadRequest();
            }

            var existingTask = await _context.HousekeepingTasks.FindAsync(id);
            if (existingTask == null)
            {
                return NotFound();
            }

            existingTask.Title = task.Title;
            existingTask.Description = task.Description;
            existingTask.TaskType = task.TaskType;
            existingTask.Status = task.Status;
            existingTask.Priority = task.Priority;
            existingTask.ScheduledDate = task.ScheduledDate;
            existingTask.EstimatedDuration = task.EstimatedDuration;
            existingTask.AssignedStaffId = task.AssignedStaffId;
            existingTask.Notes = task.Notes;
            existingTask.CompletionNotes = task.CompletionNotes;
            existingTask.ActualDuration = task.ActualDuration;
            existingTask.UpdatedAt = DateTime.UtcNow;

            if (task.Status == Models.TaskStatus.InProgress && existingTask.StartedAt == null)
            {
                existingTask.StartedAt = DateTime.UtcNow;
            }
            else if (task.Status == Models.TaskStatus.Completed && existingTask.CompletedAt == null)
            {
                existingTask.CompletedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return NoContent();
        }

        // PUT: api/Housekeeping/tasks/{id}/start
        [HttpPut("tasks/{id}/start")]
        [Authorize(Roles = "Staff")]
        public async Task<IActionResult> StartTask(int id)
        {
            var task = await _context.HousekeepingTasks.FindAsync(id);
            if (task == null)
            {
                return NotFound();
            }

            task.Status = Models.TaskStatus.InProgress;
            task.StartedAt = DateTime.UtcNow;
            task.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return NoContent();
        }

        // PUT: api/Housekeeping/tasks/{id}/complete
        [HttpPut("tasks/{id}/complete")]
        [Authorize(Roles = "Staff")]
        public async Task<IActionResult> CompleteTask(int id, [FromBody] string completionNotes = "")
        {
            var task = await _context.HousekeepingTasks.FindAsync(id);
            if (task == null)
            {
                return NotFound();
            }

            task.Status = Models.TaskStatus.Completed;
            task.CompletedAt = DateTime.UtcNow;
            task.CompletionNotes = completionNotes;
            task.UpdatedAt = DateTime.UtcNow;

            if (task.StartedAt.HasValue)
            {
                var duration = (DateTime.UtcNow - task.StartedAt.Value).TotalHours;
                task.ActualDuration = (decimal)duration;
            }

            await _context.SaveChangesAsync();
            return NoContent();
        }

        // GET: api/Housekeeping/staff
        [HttpGet("staff")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<IEnumerable<Staff>>> GetStaff(int? propertyId = null)
        {
            var query = _context.Staff.AsQueryable();

            if (propertyId.HasValue)
            {
                // Filter staff by property (you might need to add PropertyId to Staff model)
                // For now, return all active staff
                query = query.Where(s => s.IsActive);
            }

            var staff = await query
                .Include(s => s.AssignedTasks.Where(t => t.ScheduledDate >= DateTime.Today))
                .OrderBy(s => s.FirstName)
                .ThenBy(s => s.LastName)
                .ToListAsync();

            return Ok(staff);
        }

        // POST: api/Housekeeping/staff
        [HttpPost("staff")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<Staff>> CreateStaff(Staff staff)
        {
            staff.CreatedAt = DateTime.UtcNow;

            _context.Staff.Add(staff);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetStaff), new { id = staff.Id }, staff);
        }

        // GET: api/Housekeeping/staff/{id}/schedule
        [HttpGet("staff/{id}/schedule")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<IEnumerable<StaffSchedule>>> GetStaffSchedule(int id, DateTime? startDate = null, DateTime? endDate = null)
        {
            startDate ??= DateTime.Today;
            endDate ??= DateTime.Today.AddDays(7);

            var schedule = await _context.StaffSchedules
                .Where(ss => ss.StaffId == id && ss.Date >= startDate && ss.Date <= endDate)
                .OrderBy(ss => ss.Date)
                .ThenBy(ss => ss.StartTime)
                .ToListAsync();

            return Ok(schedule);
        }

        // POST: api/Housekeeping/staff/{id}/schedule
        [HttpPost("staff/{id}/schedule")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<StaffSchedule>> AddStaffSchedule(int id, StaffSchedule schedule)
        {
            var staff = await _context.Staff.FindAsync(id);
            if (staff == null)
            {
                return NotFound("Staff not found");
            }

            schedule.StaffId = id;
            schedule.CreatedAt = DateTime.UtcNow;

            _context.StaffSchedules.Add(schedule);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetStaffSchedule), new { id = schedule.Id }, schedule);
        }

        // GET: api/Housekeeping/maintenance
        [HttpGet("maintenance")]
        [Authorize(Roles = "Host,Admin,Staff")]
        public async Task<ActionResult<IEnumerable<RoomMaintenance>>> GetMaintenanceRequests(
            int? roomId = null,
            Models.TaskStatus? status = null,
            TaskPriority? priority = null)
        {
            var query = _context.RoomMaintenances.AsQueryable();

            if (roomId.HasValue)
                query = query.Where(rm => rm.RoomId == roomId.Value);

            if (status.HasValue)
                query = query.Where(rm => rm.Status == status.Value);

            if (priority.HasValue)
                query = query.Where(rm => rm.Priority == priority.Value);

            var maintenance = await query
                .Include(rm => rm.Room)
                    .ThenInclude(r => r.Property)
                .Include(rm => rm.AssignedStaff)
                .Include(rm => rm.MaintenanceImages)
                .OrderByDescending(rm => rm.Priority)
                .ThenBy(rm => rm.ReportedDate)
                .ToListAsync();

            return Ok(maintenance);
        }

        // POST: api/Housekeeping/maintenance
        [HttpPost("maintenance")]
        [Authorize(Roles = "Host,Admin,Staff")]
        public async Task<ActionResult<RoomMaintenance>> CreateMaintenanceRequest(RoomMaintenance maintenance)
        {
            maintenance.ReportedDate = DateTime.UtcNow;

            _context.RoomMaintenances.Add(maintenance);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetMaintenanceRequests), new { id = maintenance.Id }, maintenance);
        }

        // PUT: api/Housekeeping/maintenance/{id}
        [HttpPut("maintenance/{id}")]
        [Authorize(Roles = "Host,Admin,Staff")]
        public async Task<IActionResult> UpdateMaintenanceRequest(int id, RoomMaintenance maintenance)
        {
            if (id != maintenance.Id)
            {
                return BadRequest();
            }

            var existingMaintenance = await _context.RoomMaintenances.FindAsync(id);
            if (existingMaintenance == null)
            {
                return NotFound();
            }

            existingMaintenance.IssueTitle = maintenance.IssueTitle;
            existingMaintenance.IssueDescription = maintenance.IssueDescription;
            existingMaintenance.Priority = maintenance.Priority;
            existingMaintenance.Status = maintenance.Status;
            existingMaintenance.ScheduledDate = maintenance.ScheduledDate;
            existingMaintenance.AssignedStaffId = maintenance.AssignedStaffId;
            existingMaintenance.EstimatedCost = maintenance.EstimatedCost;
            existingMaintenance.ActualCost = maintenance.ActualCost;
            existingMaintenance.ResolutionNotes = maintenance.ResolutionNotes;
            existingMaintenance.RequiresExternalVendor = maintenance.RequiresExternalVendor;
            existingMaintenance.VendorName = maintenance.VendorName;
            existingMaintenance.AffectsAvailability = maintenance.AffectsAvailability;

            if (maintenance.Status == Models.TaskStatus.Completed && existingMaintenance.CompletedDate == null)
            {
                existingMaintenance.CompletedDate = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return NoContent();
        }

        // GET: api/Housekeeping/inventory
        [HttpGet("inventory")]
        [Authorize(Roles = "Host,Admin,Staff")]
        public async Task<ActionResult<IEnumerable<InventoryItem>>> GetInventory(int? propertyId = null, string category = null)
        {
            var query = _context.InventoryItems.AsQueryable();

            if (propertyId.HasValue)
                query = query.Where(ii => ii.PropertyId == propertyId.Value);

            if (!string.IsNullOrEmpty(category))
                query = query.Where(ii => ii.Category == category);

            var inventory = await query
                .Include(ii => ii.Transactions.OrderByDescending(t => t.TransactionDate).Take(5))
                .OrderBy(ii => ii.Category)
                .ThenBy(ii => ii.Name)
                .ToListAsync();

            return Ok(inventory);
        }

        // POST: api/Housekeeping/inventory
        [HttpPost("inventory")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<InventoryItem>> CreateInventoryItem(InventoryItem item)
        {
            item.CreatedAt = DateTime.UtcNow;
            item.LastRestocked = DateTime.UtcNow;

            _context.InventoryItems.Add(item);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetInventory), new { id = item.Id }, item);
        }

        // POST: api/Housekeeping/inventory/{id}/transaction
        [HttpPost("inventory/{id}/transaction")]
        [Authorize(Roles = "Host,Admin,Staff")]
        public async Task<ActionResult<InventoryTransaction>> AddInventoryTransaction(int id, InventoryTransaction transaction)
        {
            var item = await _context.InventoryItems.FindAsync(id);
            if (item == null)
            {
                return NotFound("Inventory item not found");
            }

            transaction.InventoryItemId = id;
            transaction.TransactionDate = DateTime.UtcNow;

            // Update current stock
            if (transaction.TransactionType == "In")
            {
                item.CurrentStock += transaction.Quantity;
                if (transaction.Quantity > 0)
                {
                    item.LastRestocked = DateTime.UtcNow;
                }
            }
            else if (transaction.TransactionType == "Out")
            {
                item.CurrentStock -= transaction.Quantity;
            }
            else if (transaction.TransactionType == "Adjustment")
            {
                item.CurrentStock = transaction.Quantity;
            }

            _context.InventoryTransactions.Add(transaction);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetInventory), new { id = transaction.Id }, transaction);
        }

        // GET: api/Housekeeping/inventory/low-stock
        [HttpGet("inventory/low-stock")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<IEnumerable<InventoryItem>>> GetLowStockItems(int? propertyId = null)
        {
            var query = _context.InventoryItems.AsQueryable();

            if (propertyId.HasValue)
                query = query.Where(ii => ii.PropertyId == propertyId.Value);

            var lowStockItems = await query
                .Where(ii => ii.CurrentStock <= ii.MinimumStock)
                .OrderBy(ii => ii.CurrentStock)
                .ToListAsync();

            return Ok(lowStockItems);
        }

        // GET: api/Housekeeping/dashboard
        [HttpGet("dashboard")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult> GetHousekeepingDashboard(int? propertyId = null)
        {
            var query = _context.HousekeepingTasks.AsQueryable();
            if (propertyId.HasValue)
                query = query.Where(t => t.PropertyId == propertyId.Value);

            var today = DateTime.Today;

            var pendingTasks = await query.CountAsync(t => t.Status == Models.TaskStatus.Pending && t.ScheduledDate.Date == today);
            var inProgressTasks = await query.CountAsync(t => t.Status == Models.TaskStatus.InProgress);
            var completedTasks = await query.CountAsync(t => t.Status == Models.TaskStatus.Completed && t.CompletedAt.HasValue && t.CompletedAt.Value.Date == today);
            var overdueTasks = await query.CountAsync(t => t.Status != Models.TaskStatus.Completed && t.ScheduledDate.Date < today);

            var maintenanceQuery = _context.RoomMaintenances.AsQueryable();
            if (propertyId.HasValue)
            {
                maintenanceQuery = maintenanceQuery.Where(rm => rm.Room.PropertyId == propertyId.Value);
            }

            var urgentMaintenance = await maintenanceQuery.CountAsync(rm => rm.Priority == TaskPriority.Urgent && rm.Status != Models.TaskStatus.Completed);
            var pendingMaintenance = await maintenanceQuery.CountAsync(rm => rm.Status == Models.TaskStatus.Pending);

            var inventoryQuery = _context.InventoryItems.AsQueryable();
            if (propertyId.HasValue)
                inventoryQuery = inventoryQuery.Where(ii => ii.PropertyId == propertyId.Value);

            var lowStockCount = await inventoryQuery.CountAsync(ii => ii.CurrentStock <= ii.MinimumStock);

            var dashboard = new
            {
                Tasks = new
                {
                    Pending = pendingTasks,
                    InProgress = inProgressTasks,
                    Completed = completedTasks,
                    Overdue = overdueTasks
                },
                Maintenance = new
                {
                    Urgent = urgentMaintenance,
                    Pending = pendingMaintenance
                },
                Inventory = new
                {
                    LowStock = lowStockCount
                }
            };

            return Ok(dashboard);
        }
    }
}
