{"version": 3, "sources": ["../../../../../../node_modules/@angular/google-maps/fesm2022/google-maps.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ElementRef, NgZone, EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, Directive, InjectionToken, ContentChildren, NgModule, Injectable } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { BehaviorSubject, Observable, Subject, combineLatest, Subscription } from 'rxjs';\nimport { switchMap, take, map, takeUntil } from 'rxjs/operators';\n\n/** Manages event on a Google Maps object, ensuring that events are added only when necessary. */\nconst _c0 = [\"*\"];\nclass MapEventManager {\n  _ngZone;\n  /** Pending listeners that were added before the target was set. */\n  _pending = [];\n  _listeners = [];\n  _targetStream = new BehaviorSubject(undefined);\n  /** Clears all currently-registered event listeners. */\n  _clearListeners() {\n    for (const listener of this._listeners) {\n      listener.remove();\n    }\n    this._listeners = [];\n  }\n  constructor(_ngZone) {\n    this._ngZone = _ngZone;\n  }\n  /** Gets an observable that adds an event listener to the map when a consumer subscribes to it. */\n  getLazyEmitter(name) {\n    return this._targetStream.pipe(switchMap(target => {\n      const observable = new Observable(observer => {\n        // If the target hasn't been initialized yet, cache the observer so it can be added later.\n        if (!target) {\n          this._pending.push({\n            observable,\n            observer\n          });\n          return undefined;\n        }\n        const listener = target.addListener(name, event => {\n          this._ngZone.run(() => observer.next(event));\n        });\n        // If there's an error when initializing the Maps API (e.g. a wrong API key), it will\n        // return a dummy object that returns `undefined` from `addListener` (see #26514).\n        if (!listener) {\n          observer.complete();\n          return undefined;\n        }\n        this._listeners.push(listener);\n        return () => listener.remove();\n      });\n      return observable;\n    }));\n  }\n  /** Sets the current target that the manager should bind events to. */\n  setTarget(target) {\n    const currentTarget = this._targetStream.value;\n    if (target === currentTarget) {\n      return;\n    }\n    // Clear the listeners from the pre-existing target.\n    if (currentTarget) {\n      this._clearListeners();\n      this._pending = [];\n    }\n    this._targetStream.next(target);\n    // Add the listeners that were bound before the map was initialized.\n    this._pending.forEach(subscriber => subscriber.observable.subscribe(subscriber.observer));\n    this._pending = [];\n  }\n  /** Destroys the manager and clears the event listeners. */\n  destroy() {\n    this._clearListeners();\n    this._pending = [];\n    this._targetStream.complete();\n  }\n}\n\n/** default options set to the Googleplex */\nconst DEFAULT_OPTIONS = {\n  center: {\n    lat: 37.421995,\n    lng: -122.084092\n  },\n  zoom: 17,\n  // Note: the type conversion here isn't necessary for our CI, but it resolves a g3 failure.\n  mapTypeId: 'roadmap'\n};\n/** Arbitrary default height for the map element */\nconst DEFAULT_HEIGHT = '500px';\n/** Arbitrary default width for the map element */\nconst DEFAULT_WIDTH = '500px';\n/**\n * Angular component that renders a Google Map via the Google Maps JavaScript\n * API.\n * @see https://developers.google.com/maps/documentation/javascript/reference/\n */\nclass GoogleMap {\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _eventManager = new MapEventManager(inject(NgZone));\n  _mapEl;\n  _existingAuthFailureCallback;\n  /**\n   * The underlying google.maps.Map object\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/map#Map\n   */\n  googleMap;\n  /** Whether we're currently rendering inside a browser. */\n  _isBrowser;\n  /** Height of the map. Set this to `null` if you'd like to control the height through CSS. */\n  height = DEFAULT_HEIGHT;\n  /** Width of the map. Set this to `null` if you'd like to control the width through CSS. */\n  width = DEFAULT_WIDTH;\n  /**\n   * The Map ID of the map. This parameter cannot be set or changed after a map is instantiated.\n   * See: https://developers.google.com/maps/documentation/javascript/reference/map#MapOptions.mapId\n   */\n  mapId;\n  /**\n   * Type of map that should be rendered. E.g. hybrid map, terrain map etc.\n   * See: https://developers.google.com/maps/documentation/javascript/reference/map#MapTypeId\n   */\n  mapTypeId;\n  set center(center) {\n    this._center = center;\n  }\n  _center;\n  set zoom(zoom) {\n    this._zoom = zoom;\n  }\n  _zoom;\n  set options(options) {\n    this._options = options || DEFAULT_OPTIONS;\n  }\n  _options = DEFAULT_OPTIONS;\n  /** Event emitted when the map is initialized. */\n  mapInitialized = new EventEmitter();\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/events#auth-errors\n   */\n  authFailure = new EventEmitter();\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.bounds_changed\n   */\n  boundsChanged = this._eventManager.getLazyEmitter('bounds_changed');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.center_changed\n   */\n  centerChanged = this._eventManager.getLazyEmitter('center_changed');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.click\n   */\n  mapClick = this._eventManager.getLazyEmitter('click');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.dblclick\n   */\n  mapDblclick = this._eventManager.getLazyEmitter('dblclick');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.drag\n   */\n  mapDrag = this._eventManager.getLazyEmitter('drag');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.dragend\n   */\n  mapDragend = this._eventManager.getLazyEmitter('dragend');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.dragstart\n   */\n  mapDragstart = this._eventManager.getLazyEmitter('dragstart');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.heading_changed\n   */\n  headingChanged = this._eventManager.getLazyEmitter('heading_changed');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.idle\n   */\n  idle = this._eventManager.getLazyEmitter('idle');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.maptypeid_changed\n   */\n  maptypeidChanged = this._eventManager.getLazyEmitter('maptypeid_changed');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.mousemove\n   */\n  mapMousemove = this._eventManager.getLazyEmitter('mousemove');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.mouseout\n   */\n  mapMouseout = this._eventManager.getLazyEmitter('mouseout');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.mouseover\n   */\n  mapMouseover = this._eventManager.getLazyEmitter('mouseover');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/map#Map.projection_changed\n   */\n  projectionChanged = this._eventManager.getLazyEmitter('projection_changed');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.rightclick\n   */\n  mapRightclick = this._eventManager.getLazyEmitter('rightclick');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.tilesloaded\n   */\n  tilesloaded = this._eventManager.getLazyEmitter('tilesloaded');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.tilt_changed\n   */\n  tiltChanged = this._eventManager.getLazyEmitter('tilt_changed');\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.zoom_changed\n   */\n  zoomChanged = this._eventManager.getLazyEmitter('zoom_changed');\n  constructor() {\n    const platformId = inject(PLATFORM_ID);\n    this._isBrowser = isPlatformBrowser(platformId);\n    if (this._isBrowser) {\n      const googleMapsWindow = window;\n      if (!googleMapsWindow.google && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Namespace google not found, cannot construct embedded google ' + 'map. Please install the Google Maps JavaScript API: ' + 'https://developers.google.com/maps/documentation/javascript/' + 'tutorial#Loading_the_Maps_API');\n      }\n      this._existingAuthFailureCallback = googleMapsWindow.gm_authFailure;\n      googleMapsWindow.gm_authFailure = () => {\n        if (this._existingAuthFailureCallback) {\n          this._existingAuthFailureCallback();\n        }\n        this.authFailure.emit();\n      };\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['height'] || changes['width']) {\n      this._setSize();\n    }\n    const googleMap = this.googleMap;\n    if (googleMap) {\n      if (changes['options']) {\n        googleMap.setOptions(this._combineOptions());\n      }\n      if (changes['center'] && this._center) {\n        googleMap.setCenter(this._center);\n      }\n      // Note that the zoom can be zero.\n      if (changes['zoom'] && this._zoom != null) {\n        googleMap.setZoom(this._zoom);\n      }\n      if (changes['mapTypeId'] && this.mapTypeId) {\n        googleMap.setMapTypeId(this.mapTypeId);\n      }\n    }\n  }\n  ngOnInit() {\n    // It should be a noop during server-side rendering.\n    if (this._isBrowser) {\n      this._mapEl = this._elementRef.nativeElement.querySelector('.map-container');\n      this._setSize();\n      // Create the object outside the zone so its events don't trigger change detection.\n      // We'll bring it back in inside the `MapEventManager` only for the events that the\n      // user has subscribed to.\n      if (google.maps.Map) {\n        this._initialize(google.maps.Map);\n      } else {\n        this._ngZone.runOutsideAngular(() => {\n          google.maps.importLibrary('maps').then(lib => this._initialize(lib.Map));\n        });\n      }\n    }\n  }\n  _initialize(mapConstructor) {\n    this._ngZone.runOutsideAngular(() => {\n      this.googleMap = new mapConstructor(this._mapEl, this._combineOptions());\n      this._eventManager.setTarget(this.googleMap);\n      this.mapInitialized.emit(this.googleMap);\n    });\n  }\n  ngOnDestroy() {\n    this.mapInitialized.complete();\n    this._eventManager.destroy();\n    if (this._isBrowser) {\n      const googleMapsWindow = window;\n      googleMapsWindow.gm_authFailure = this._existingAuthFailureCallback;\n    }\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.fitBounds\n   */\n  fitBounds(bounds, padding) {\n    this._assertInitialized();\n    this.googleMap.fitBounds(bounds, padding);\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.panBy\n   */\n  panBy(x, y) {\n    this._assertInitialized();\n    this.googleMap.panBy(x, y);\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.panTo\n   */\n  panTo(latLng) {\n    this._assertInitialized();\n    this.googleMap.panTo(latLng);\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.panToBounds\n   */\n  panToBounds(latLngBounds, padding) {\n    this._assertInitialized();\n    this.googleMap.panToBounds(latLngBounds, padding);\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.getBounds\n   */\n  getBounds() {\n    this._assertInitialized();\n    return this.googleMap.getBounds() || null;\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.getCenter\n   */\n  getCenter() {\n    this._assertInitialized();\n    return this.googleMap.getCenter();\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.getClickableIcons\n   */\n  getClickableIcons() {\n    this._assertInitialized();\n    return this.googleMap.getClickableIcons();\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.getHeading\n   */\n  getHeading() {\n    this._assertInitialized();\n    return this.googleMap.getHeading();\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.getMapTypeId\n   */\n  getMapTypeId() {\n    this._assertInitialized();\n    return this.googleMap.getMapTypeId();\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.getProjection\n   */\n  getProjection() {\n    this._assertInitialized();\n    return this.googleMap.getProjection() || null;\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.getStreetView\n   */\n  getStreetView() {\n    this._assertInitialized();\n    return this.googleMap.getStreetView();\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.getTilt\n   */\n  getTilt() {\n    this._assertInitialized();\n    return this.googleMap.getTilt();\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.getZoom\n   */\n  getZoom() {\n    this._assertInitialized();\n    return this.googleMap.getZoom();\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.controls\n   */\n  get controls() {\n    this._assertInitialized();\n    return this.googleMap.controls;\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.data\n   */\n  get data() {\n    this._assertInitialized();\n    return this.googleMap.data;\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.mapTypes\n   */\n  get mapTypes() {\n    this._assertInitialized();\n    return this.googleMap.mapTypes;\n  }\n  /**\n   * See\n   * https://developers.google.com/maps/documentation/javascript/reference/map#Map.overlayMapTypes\n   */\n  get overlayMapTypes() {\n    this._assertInitialized();\n    return this.googleMap.overlayMapTypes;\n  }\n  /** Returns a promise that resolves when the map has been initialized. */\n  _resolveMap() {\n    return this.googleMap ? Promise.resolve(this.googleMap) : this.mapInitialized.pipe(take(1)).toPromise();\n  }\n  _setSize() {\n    if (this._mapEl) {\n      const styles = this._mapEl.style;\n      styles.height = this.height === null ? '' : coerceCssPixelValue(this.height) || DEFAULT_HEIGHT;\n      styles.width = this.width === null ? '' : coerceCssPixelValue(this.width) || DEFAULT_WIDTH;\n    }\n  }\n  /** Combines the center and zoom and the other map options into a single object */\n  _combineOptions() {\n    const options = this._options || {};\n    return {\n      ...options,\n      // It's important that we set **some** kind of `center` and `zoom`, otherwise\n      // Google Maps will render a blank rectangle which looks broken.\n      center: this._center || options.center || DEFAULT_OPTIONS.center,\n      zoom: this._zoom ?? options.zoom ?? DEFAULT_OPTIONS.zoom,\n      // Passing in an undefined `mapTypeId` seems to break tile loading\n      // so make sure that we have some kind of default (see #22082).\n      mapTypeId: this.mapTypeId || options.mapTypeId || DEFAULT_OPTIONS.mapTypeId,\n      mapId: this.mapId || options.mapId\n    };\n  }\n  /** Asserts that the map has been initialized. */\n  _assertInitialized() {\n    if (!this.googleMap && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Cannot access Google Map information before the API has been initialized. ' + 'Please wait for the API to load before trying to interact with it.');\n    }\n  }\n  static ɵfac = function GoogleMap_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || GoogleMap)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: GoogleMap,\n    selectors: [[\"google-map\"]],\n    inputs: {\n      height: \"height\",\n      width: \"width\",\n      mapId: \"mapId\",\n      mapTypeId: \"mapTypeId\",\n      center: \"center\",\n      zoom: \"zoom\",\n      options: \"options\"\n    },\n    outputs: {\n      mapInitialized: \"mapInitialized\",\n      authFailure: \"authFailure\",\n      boundsChanged: \"boundsChanged\",\n      centerChanged: \"centerChanged\",\n      mapClick: \"mapClick\",\n      mapDblclick: \"mapDblclick\",\n      mapDrag: \"mapDrag\",\n      mapDragend: \"mapDragend\",\n      mapDragstart: \"mapDragstart\",\n      headingChanged: \"headingChanged\",\n      idle: \"idle\",\n      maptypeidChanged: \"maptypeidChanged\",\n      mapMousemove: \"mapMousemove\",\n      mapMouseout: \"mapMouseout\",\n      mapMouseover: \"mapMouseover\",\n      projectionChanged: \"projectionChanged\",\n      mapRightclick: \"mapRightclick\",\n      tilesloaded: \"tilesloaded\",\n      tiltChanged: \"tiltChanged\",\n      zoomChanged: \"zoomChanged\"\n    },\n    exportAs: [\"googleMap\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 0,\n    consts: [[1, \"map-container\"]],\n    template: function GoogleMap_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelement(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GoogleMap, [{\n    type: Component,\n    args: [{\n      selector: 'google-map',\n      exportAs: 'googleMap',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: '<div class=\"map-container\"></div><ng-content />',\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], () => [], {\n    height: [{\n      type: Input\n    }],\n    width: [{\n      type: Input\n    }],\n    mapId: [{\n      type: Input\n    }],\n    mapTypeId: [{\n      type: Input\n    }],\n    center: [{\n      type: Input\n    }],\n    zoom: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    mapInitialized: [{\n      type: Output\n    }],\n    authFailure: [{\n      type: Output\n    }],\n    boundsChanged: [{\n      type: Output\n    }],\n    centerChanged: [{\n      type: Output\n    }],\n    mapClick: [{\n      type: Output\n    }],\n    mapDblclick: [{\n      type: Output\n    }],\n    mapDrag: [{\n      type: Output\n    }],\n    mapDragend: [{\n      type: Output\n    }],\n    mapDragstart: [{\n      type: Output\n    }],\n    headingChanged: [{\n      type: Output\n    }],\n    idle: [{\n      type: Output\n    }],\n    maptypeidChanged: [{\n      type: Output\n    }],\n    mapMousemove: [{\n      type: Output\n    }],\n    mapMouseout: [{\n      type: Output\n    }],\n    mapMouseover: [{\n      type: Output\n    }],\n    projectionChanged: [{\n      type: Output\n    }],\n    mapRightclick: [{\n      type: Output\n    }],\n    tilesloaded: [{\n      type: Output\n    }],\n    tiltChanged: [{\n      type: Output\n    }],\n    zoomChanged: [{\n      type: Output\n    }]\n  });\n})();\nconst cssUnitsPattern = /([A-Za-z%]+)$/;\n/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n  if (value == null) {\n    return '';\n  }\n  return cssUnitsPattern.test(value) ? value : `${value}px`;\n}\nclass MapBaseLayer {\n  _map = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  constructor() {}\n  ngOnInit() {\n    if (this._map._isBrowser) {\n      this._ngZone.runOutsideAngular(() => {\n        this._initializeObject();\n      });\n      this._assertInitialized();\n      this._setMap();\n    }\n  }\n  ngOnDestroy() {\n    this._unsetMap();\n  }\n  _assertInitialized() {\n    if (!this._map.googleMap) {\n      throw Error('Cannot access Google Map information before the API has been initialized. ' + 'Please wait for the API to load before trying to interact with it.');\n    }\n  }\n  _initializeObject() {}\n  _setMap() {}\n  _unsetMap() {}\n  static ɵfac = function MapBaseLayer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapBaseLayer)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapBaseLayer,\n    selectors: [[\"map-base-layer\"]],\n    exportAs: [\"mapBaseLayer\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapBaseLayer, [{\n    type: Directive,\n    args: [{\n      selector: 'map-base-layer',\n      exportAs: 'mapBaseLayer'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Angular component that renders a Google Maps Bicycling Layer via the Google Maps JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/map#BicyclingLayer\n */\nclass MapBicyclingLayer {\n  _map = inject(GoogleMap);\n  _zone = inject(NgZone);\n  /**\n   * The underlying google.maps.BicyclingLayer object.\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/map#BicyclingLayer\n   */\n  bicyclingLayer;\n  /** Event emitted when the bicycling layer is initialized. */\n  bicyclingLayerInitialized = new EventEmitter();\n  ngOnInit() {\n    if (this._map._isBrowser) {\n      if (google.maps.BicyclingLayer && this._map.googleMap) {\n        this._initialize(this._map.googleMap, google.maps.BicyclingLayer);\n      } else {\n        this._zone.runOutsideAngular(() => {\n          Promise.all([this._map._resolveMap(), google.maps.importLibrary('maps')]).then(([map, lib]) => {\n            this._initialize(map, lib.BicyclingLayer);\n          });\n        });\n      }\n    }\n  }\n  _initialize(map, layerConstructor) {\n    this._zone.runOutsideAngular(() => {\n      this.bicyclingLayer = new layerConstructor();\n      this.bicyclingLayerInitialized.emit(this.bicyclingLayer);\n      this._assertLayerInitialized();\n      this.bicyclingLayer.setMap(map);\n    });\n  }\n  ngOnDestroy() {\n    this.bicyclingLayer?.setMap(null);\n  }\n  _assertLayerInitialized() {\n    if (!this.bicyclingLayer) {\n      throw Error('Cannot interact with a Google Map Bicycling Layer before it has been initialized. ' + 'Please wait for the Transit Layer to load before trying to interact with it.');\n    }\n  }\n  static ɵfac = function MapBicyclingLayer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapBicyclingLayer)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapBicyclingLayer,\n    selectors: [[\"map-bicycling-layer\"]],\n    outputs: {\n      bicyclingLayerInitialized: \"bicyclingLayerInitialized\"\n    },\n    exportAs: [\"mapBicyclingLayer\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapBicyclingLayer, [{\n    type: Directive,\n    args: [{\n      selector: 'map-bicycling-layer',\n      exportAs: 'mapBicyclingLayer'\n    }]\n  }], null, {\n    bicyclingLayerInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular component that renders a Google Maps Circle via the Google Maps JavaScript API.\n * @see developers.google.com/maps/documentation/javascript/reference/polygon#Circle\n */\nclass MapCircle {\n  _map = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _eventManager = new MapEventManager(inject(NgZone));\n  _options = new BehaviorSubject({});\n  _center = new BehaviorSubject(undefined);\n  _radius = new BehaviorSubject(undefined);\n  _destroyed = new Subject();\n  /**\n   * Underlying google.maps.Circle object.\n   *\n   * @see developers.google.com/maps/documentation/javascript/reference/polygon#Circle\n   */\n  circle; // initialized in ngOnInit\n  set options(options) {\n    this._options.next(options || {});\n  }\n  set center(center) {\n    this._center.next(center);\n  }\n  set radius(radius) {\n    this._radius.next(radius);\n  }\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.center_changed\n   */\n  centerChanged = this._eventManager.getLazyEmitter('center_changed');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.click\n   */\n  circleClick = this._eventManager.getLazyEmitter('click');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.dblclick\n   */\n  circleDblclick = this._eventManager.getLazyEmitter('dblclick');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.drag\n   */\n  circleDrag = this._eventManager.getLazyEmitter('drag');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.dragend\n   */\n  circleDragend = this._eventManager.getLazyEmitter('dragend');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.dragstart\n   */\n  circleDragstart = this._eventManager.getLazyEmitter('dragstart');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.mousedown\n   */\n  circleMousedown = this._eventManager.getLazyEmitter('mousedown');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.mousemove\n   */\n  circleMousemove = this._eventManager.getLazyEmitter('mousemove');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.mouseout\n   */\n  circleMouseout = this._eventManager.getLazyEmitter('mouseout');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.mouseover\n   */\n  circleMouseover = this._eventManager.getLazyEmitter('mouseover');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.mouseup\n   */\n  circleMouseup = this._eventManager.getLazyEmitter('mouseup');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.radius_changed\n   */\n  radiusChanged = this._eventManager.getLazyEmitter('radius_changed');\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.rightclick\n   */\n  circleRightclick = this._eventManager.getLazyEmitter('rightclick');\n  /** Event emitted when the circle is initialized. */\n  circleInitialized = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    if (!this._map._isBrowser) {\n      return;\n    }\n    this._combineOptions().pipe(take(1)).subscribe(options => {\n      if (google.maps.Circle && this._map.googleMap) {\n        this._initialize(this._map.googleMap, google.maps.Circle, options);\n      } else {\n        this._ngZone.runOutsideAngular(() => {\n          Promise.all([this._map._resolveMap(), google.maps.importLibrary('maps')]).then(([map, lib]) => {\n            this._initialize(map, lib.Circle, options);\n          });\n        });\n      }\n    });\n  }\n  _initialize(map, circleConstructor, options) {\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.circle = new circleConstructor(options);\n      this._assertInitialized();\n      this.circle.setMap(map);\n      this._eventManager.setTarget(this.circle);\n      this.circleInitialized.emit(this.circle);\n      this._watchForOptionsChanges();\n      this._watchForCenterChanges();\n      this._watchForRadiusChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._eventManager.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.circle?.setMap(null);\n  }\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.getBounds\n   */\n  getBounds() {\n    this._assertInitialized();\n    return this.circle.getBounds();\n  }\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.getCenter\n   */\n  getCenter() {\n    this._assertInitialized();\n    return this.circle.getCenter();\n  }\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.getDraggable\n   */\n  getDraggable() {\n    this._assertInitialized();\n    return this.circle.getDraggable();\n  }\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.getEditable\n   */\n  getEditable() {\n    this._assertInitialized();\n    return this.circle.getEditable();\n  }\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.getRadius\n   */\n  getRadius() {\n    this._assertInitialized();\n    return this.circle.getRadius();\n  }\n  /**\n   * @see\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Circle.getVisible\n   */\n  getVisible() {\n    this._assertInitialized();\n    return this.circle.getVisible();\n  }\n  _combineOptions() {\n    return combineLatest([this._options, this._center, this._radius]).pipe(map(([options, center, radius]) => {\n      const combinedOptions = {\n        ...options,\n        center: center || options.center,\n        radius: radius !== undefined ? radius : options.radius\n      };\n      return combinedOptions;\n    }));\n  }\n  _watchForOptionsChanges() {\n    this._options.pipe(takeUntil(this._destroyed)).subscribe(options => {\n      this._assertInitialized();\n      this.circle.setOptions(options);\n    });\n  }\n  _watchForCenterChanges() {\n    this._center.pipe(takeUntil(this._destroyed)).subscribe(center => {\n      if (center) {\n        this._assertInitialized();\n        this.circle.setCenter(center);\n      }\n    });\n  }\n  _watchForRadiusChanges() {\n    this._radius.pipe(takeUntil(this._destroyed)).subscribe(radius => {\n      if (radius !== undefined) {\n        this._assertInitialized();\n        this.circle.setRadius(radius);\n      }\n    });\n  }\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.circle) {\n        throw Error('Cannot interact with a Google Map Circle before it has been ' + 'initialized. Please wait for the Circle to load before trying to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function MapCircle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapCircle)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapCircle,\n    selectors: [[\"map-circle\"]],\n    inputs: {\n      options: \"options\",\n      center: \"center\",\n      radius: \"radius\"\n    },\n    outputs: {\n      centerChanged: \"centerChanged\",\n      circleClick: \"circleClick\",\n      circleDblclick: \"circleDblclick\",\n      circleDrag: \"circleDrag\",\n      circleDragend: \"circleDragend\",\n      circleDragstart: \"circleDragstart\",\n      circleMousedown: \"circleMousedown\",\n      circleMousemove: \"circleMousemove\",\n      circleMouseout: \"circleMouseout\",\n      circleMouseover: \"circleMouseover\",\n      circleMouseup: \"circleMouseup\",\n      radiusChanged: \"radiusChanged\",\n      circleRightclick: \"circleRightclick\",\n      circleInitialized: \"circleInitialized\"\n    },\n    exportAs: [\"mapCircle\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapCircle, [{\n    type: Directive,\n    args: [{\n      selector: 'map-circle',\n      exportAs: 'mapCircle'\n    }]\n  }], () => [], {\n    options: [{\n      type: Input\n    }],\n    center: [{\n      type: Input\n    }],\n    radius: [{\n      type: Input\n    }],\n    centerChanged: [{\n      type: Output\n    }],\n    circleClick: [{\n      type: Output\n    }],\n    circleDblclick: [{\n      type: Output\n    }],\n    circleDrag: [{\n      type: Output\n    }],\n    circleDragend: [{\n      type: Output\n    }],\n    circleDragstart: [{\n      type: Output\n    }],\n    circleMousedown: [{\n      type: Output\n    }],\n    circleMousemove: [{\n      type: Output\n    }],\n    circleMouseout: [{\n      type: Output\n    }],\n    circleMouseover: [{\n      type: Output\n    }],\n    circleMouseup: [{\n      type: Output\n    }],\n    radiusChanged: [{\n      type: Output\n    }],\n    circleRightclick: [{\n      type: Output\n    }],\n    circleInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular component that renders a Google Maps Directions Renderer via the Google Maps\n * JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/directions#DirectionsRenderer\n */\nclass MapDirectionsRenderer {\n  _googleMap = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _eventManager = new MapEventManager(inject(NgZone));\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/directions\n   * #DirectionsRendererOptions.directions\n   */\n  set directions(directions) {\n    this._directions = directions;\n  }\n  _directions;\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/directions\n   * #DirectionsRendererOptions\n   */\n  set options(options) {\n    this._options = options;\n  }\n  _options;\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/directions\n   * #DirectionsRenderer.directions_changed\n   */\n  directionsChanged = this._eventManager.getLazyEmitter('directions_changed');\n  /** Event emitted when the directions renderer is initialized. */\n  directionsRendererInitialized = new EventEmitter();\n  /** The underlying google.maps.DirectionsRenderer object. */\n  directionsRenderer;\n  constructor() {}\n  ngOnInit() {\n    if (this._googleMap._isBrowser) {\n      if (google.maps.DirectionsRenderer && this._googleMap.googleMap) {\n        this._initialize(this._googleMap.googleMap, google.maps.DirectionsRenderer);\n      } else {\n        this._ngZone.runOutsideAngular(() => {\n          Promise.all([this._googleMap._resolveMap(), google.maps.importLibrary('routes')]).then(([map, lib]) => {\n            this._initialize(map, lib.DirectionsRenderer);\n          });\n        });\n      }\n    }\n  }\n  _initialize(map, rendererConstructor) {\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.directionsRenderer = new rendererConstructor(this._combineOptions());\n      this._assertInitialized();\n      this.directionsRenderer.setMap(map);\n      this._eventManager.setTarget(this.directionsRenderer);\n      this.directionsRendererInitialized.emit(this.directionsRenderer);\n    });\n  }\n  ngOnChanges(changes) {\n    if (this.directionsRenderer) {\n      if (changes['options']) {\n        this.directionsRenderer.setOptions(this._combineOptions());\n      }\n      if (changes['directions'] && this._directions !== undefined) {\n        this.directionsRenderer.setDirections(this._directions);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._eventManager.destroy();\n    this.directionsRenderer?.setMap(null);\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/directions\n   * #DirectionsRenderer.getDirections\n   */\n  getDirections() {\n    this._assertInitialized();\n    return this.directionsRenderer.getDirections();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/directions\n   * #DirectionsRenderer.getPanel\n   */\n  getPanel() {\n    this._assertInitialized();\n    return this.directionsRenderer.getPanel();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/directions\n   * #DirectionsRenderer.getRouteIndex\n   */\n  getRouteIndex() {\n    this._assertInitialized();\n    return this.directionsRenderer.getRouteIndex();\n  }\n  _combineOptions() {\n    const options = this._options || {};\n    return {\n      ...options,\n      directions: this._directions || options.directions,\n      map: this._googleMap.googleMap\n    };\n  }\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.directionsRenderer) {\n        throw Error('Cannot interact with a Google Map Directions Renderer before it has been ' + 'initialized. Please wait for the Directions Renderer to load before trying ' + 'to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function MapDirectionsRenderer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapDirectionsRenderer)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapDirectionsRenderer,\n    selectors: [[\"map-directions-renderer\"]],\n    inputs: {\n      directions: \"directions\",\n      options: \"options\"\n    },\n    outputs: {\n      directionsChanged: \"directionsChanged\",\n      directionsRendererInitialized: \"directionsRendererInitialized\"\n    },\n    exportAs: [\"mapDirectionsRenderer\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapDirectionsRenderer, [{\n    type: Directive,\n    args: [{\n      selector: 'map-directions-renderer',\n      exportAs: 'mapDirectionsRenderer'\n    }]\n  }], () => [], {\n    directions: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    directionsChanged: [{\n      type: Output\n    }],\n    directionsRendererInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular component that renders a Google Maps Ground Overlay via the Google Maps JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/image-overlay#GroundOverlay\n */\nclass MapGroundOverlay {\n  _map = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _eventManager = new MapEventManager(inject(NgZone));\n  _opacity = new BehaviorSubject(1);\n  _url = new BehaviorSubject('');\n  _bounds = new BehaviorSubject(undefined);\n  _destroyed = new Subject();\n  _hasWatchers;\n  /**\n   * The underlying google.maps.GroundOverlay object.\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/image-overlay#GroundOverlay\n   */\n  groundOverlay;\n  /** URL of the image that will be shown in the overlay. */\n  set url(url) {\n    this._url.next(url);\n  }\n  /** Bounds for the overlay. */\n  get bounds() {\n    return this._bounds.value;\n  }\n  set bounds(bounds) {\n    this._bounds.next(bounds);\n  }\n  /** Whether the overlay is clickable */\n  clickable = false;\n  /** Opacity of the overlay. */\n  set opacity(opacity) {\n    this._opacity.next(opacity);\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/image-overlay#GroundOverlay.click\n   */\n  mapClick = this._eventManager.getLazyEmitter('click');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/image-overlay\n   * #GroundOverlay.dblclick\n   */\n  mapDblclick = this._eventManager.getLazyEmitter('dblclick');\n  /** Event emitted when the ground overlay is initialized. */\n  groundOverlayInitialized = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    if (this._map._isBrowser) {\n      // The ground overlay setup is slightly different from the other Google Maps objects in that\n      // we have to recreate the `GroundOverlay` object whenever the bounds change, because\n      // Google Maps doesn't provide an API to update the bounds of an existing overlay.\n      this._bounds.pipe(takeUntil(this._destroyed)).subscribe(bounds => {\n        if (this.groundOverlay) {\n          this.groundOverlay.setMap(null);\n          this.groundOverlay = undefined;\n        }\n        if (!bounds) {\n          return;\n        }\n        if (google.maps.GroundOverlay && this._map.googleMap) {\n          this._initialize(this._map.googleMap, google.maps.GroundOverlay, bounds);\n        } else {\n          this._ngZone.runOutsideAngular(() => {\n            Promise.all([this._map._resolveMap(), google.maps.importLibrary('maps')]).then(([map, lib]) => {\n              this._initialize(map, lib.GroundOverlay, bounds);\n            });\n          });\n        }\n      });\n    }\n  }\n  _initialize(map, overlayConstructor, bounds) {\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.groundOverlay = new overlayConstructor(this._url.getValue(), bounds, {\n        clickable: this.clickable,\n        opacity: this._opacity.value\n      });\n      this._assertInitialized();\n      this.groundOverlay.setMap(map);\n      this._eventManager.setTarget(this.groundOverlay);\n      this.groundOverlayInitialized.emit(this.groundOverlay);\n      // We only need to set up the watchers once.\n      if (!this._hasWatchers) {\n        this._hasWatchers = true;\n        this._watchForOpacityChanges();\n        this._watchForUrlChanges();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._eventManager.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.groundOverlay?.setMap(null);\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/image-overlay\n   * #GroundOverlay.getBounds\n   */\n  getBounds() {\n    this._assertInitialized();\n    return this.groundOverlay.getBounds();\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/image-overlay\n   * #GroundOverlay.getOpacity\n   */\n  getOpacity() {\n    this._assertInitialized();\n    return this.groundOverlay.getOpacity();\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/image-overlay\n   * #GroundOverlay.getUrl\n   */\n  getUrl() {\n    this._assertInitialized();\n    return this.groundOverlay.getUrl();\n  }\n  _watchForOpacityChanges() {\n    this._opacity.pipe(takeUntil(this._destroyed)).subscribe(opacity => {\n      if (opacity != null) {\n        this.groundOverlay?.setOpacity(opacity);\n      }\n    });\n  }\n  _watchForUrlChanges() {\n    this._url.pipe(takeUntil(this._destroyed)).subscribe(url => {\n      const overlay = this.groundOverlay;\n      if (overlay) {\n        overlay.set('url', url);\n        // Google Maps only redraws the overlay if we re-set the map.\n        overlay.setMap(null);\n        overlay.setMap(this._map.googleMap);\n      }\n    });\n  }\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.groundOverlay) {\n        throw Error('Cannot interact with a Google Map GroundOverlay before it has been initialized. ' + 'Please wait for the GroundOverlay to load before trying to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function MapGroundOverlay_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapGroundOverlay)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapGroundOverlay,\n    selectors: [[\"map-ground-overlay\"]],\n    inputs: {\n      url: \"url\",\n      bounds: \"bounds\",\n      clickable: \"clickable\",\n      opacity: \"opacity\"\n    },\n    outputs: {\n      mapClick: \"mapClick\",\n      mapDblclick: \"mapDblclick\",\n      groundOverlayInitialized: \"groundOverlayInitialized\"\n    },\n    exportAs: [\"mapGroundOverlay\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapGroundOverlay, [{\n    type: Directive,\n    args: [{\n      selector: 'map-ground-overlay',\n      exportAs: 'mapGroundOverlay'\n    }]\n  }], () => [], {\n    url: [{\n      type: Input\n    }],\n    bounds: [{\n      type: Input\n    }],\n    clickable: [{\n      type: Input\n    }],\n    opacity: [{\n      type: Input\n    }],\n    mapClick: [{\n      type: Output\n    }],\n    mapDblclick: [{\n      type: Output\n    }],\n    groundOverlayInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular component that renders a Google Maps info window via the Google Maps JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/info-window\n */\nclass MapInfoWindow {\n  _googleMap = inject(GoogleMap);\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _eventManager = new MapEventManager(inject(NgZone));\n  _options = new BehaviorSubject({});\n  _position = new BehaviorSubject(undefined);\n  _destroy = new Subject();\n  /**\n   * Underlying google.maps.InfoWindow\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/info-window#InfoWindow\n   */\n  infoWindow;\n  set options(options) {\n    this._options.next(options || {});\n  }\n  set position(position) {\n    this._position.next(position);\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/info-window#InfoWindow.closeclick\n   */\n  closeclick = this._eventManager.getLazyEmitter('closeclick');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/info-window\n   * #InfoWindow.content_changed\n   */\n  contentChanged = this._eventManager.getLazyEmitter('content_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/info-window#InfoWindow.domready\n   */\n  domready = this._eventManager.getLazyEmitter('domready');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/info-window\n   * #InfoWindow.position_changed\n   */\n  positionChanged = this._eventManager.getLazyEmitter('position_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/info-window\n   * #InfoWindow.zindex_changed\n   */\n  zindexChanged = this._eventManager.getLazyEmitter('zindex_changed');\n  /** Event emitted when the info window is initialized. */\n  infoWindowInitialized = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    if (this._googleMap._isBrowser) {\n      this._combineOptions().pipe(take(1)).subscribe(options => {\n        if (google.maps.InfoWindow) {\n          this._initialize(google.maps.InfoWindow, options);\n        } else {\n          this._ngZone.runOutsideAngular(() => {\n            google.maps.importLibrary('maps').then(lib => {\n              this._initialize(lib.InfoWindow, options);\n            });\n          });\n        }\n      });\n    }\n  }\n  _initialize(infoWindowConstructor, options) {\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.infoWindow = new infoWindowConstructor(options);\n      this._eventManager.setTarget(this.infoWindow);\n      this.infoWindowInitialized.emit(this.infoWindow);\n      this._watchForOptionsChanges();\n      this._watchForPositionChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._eventManager.destroy();\n    this._destroy.next();\n    this._destroy.complete();\n    // If no info window has been created on the server, we do not try closing it.\n    // On the server, an info window cannot be created and this would cause errors.\n    if (this.infoWindow) {\n      this.close();\n    }\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/info-window#InfoWindow.close\n   */\n  close() {\n    this._assertInitialized();\n    this.infoWindow.close();\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/info-window#InfoWindow.getContent\n   */\n  getContent() {\n    this._assertInitialized();\n    return this.infoWindow.getContent() || null;\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/info-window\n   * #InfoWindow.getPosition\n   */\n  getPosition() {\n    this._assertInitialized();\n    return this.infoWindow.getPosition() || null;\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/info-window#InfoWindow.getZIndex\n   */\n  getZIndex() {\n    this._assertInitialized();\n    return this.infoWindow.getZIndex();\n  }\n  /**\n   * Opens the MapInfoWindow using the provided AdvancedMarkerElement.\n   * @deprecated Use the `open` method instead.\n   * @breaking-change 20.0.0\n   */\n  openAdvancedMarkerElement(advancedMarkerElement, content) {\n    this.open({\n      getAnchor: () => advancedMarkerElement\n    }, undefined, content);\n  }\n  /**\n   * Opens the MapInfoWindow using the provided anchor. If the anchor is not set,\n   * then the position property of the options input is used instead.\n   */\n  open(anchor, shouldFocus, content) {\n    this._assertInitialized();\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && anchor && !anchor.getAnchor) {\n      throw new Error('Specified anchor does not implement the `getAnchor` method. ' + 'It cannot be used to open an info window.');\n    }\n    const anchorObject = anchor ? anchor.getAnchor() : undefined;\n    // Prevent the info window from initializing when trying to reopen on the same anchor.\n    // Note that when the window is opened for the first time, the anchor will always be\n    // undefined. If that's the case, we have to allow it to open in order to handle the\n    // case where the window doesn't have an anchor, but is placed at a particular position.\n    if (this.infoWindow.get('anchor') !== anchorObject || !anchorObject) {\n      // If no explicit content is provided, it is taken from the DOM node.\n      // If it is, we need to hide it so it doesn't take up space on the page.\n      this._elementRef.nativeElement.style.display = content ? 'none' : '';\n      if (content) {\n        this.infoWindow.setContent(content);\n      }\n      this.infoWindow.open({\n        map: this._googleMap.googleMap,\n        anchor: anchorObject,\n        shouldFocus\n      });\n    }\n  }\n  _combineOptions() {\n    return combineLatest([this._options, this._position]).pipe(map(([options, position]) => {\n      const combinedOptions = {\n        ...options,\n        position: position || options.position,\n        content: this._elementRef.nativeElement\n      };\n      return combinedOptions;\n    }));\n  }\n  _watchForOptionsChanges() {\n    this._options.pipe(takeUntil(this._destroy)).subscribe(options => {\n      this._assertInitialized();\n      this.infoWindow.setOptions(options);\n    });\n  }\n  _watchForPositionChanges() {\n    this._position.pipe(takeUntil(this._destroy)).subscribe(position => {\n      if (position) {\n        this._assertInitialized();\n        this.infoWindow.setPosition(position);\n      }\n    });\n  }\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.infoWindow) {\n        throw Error('Cannot interact with a Google Map Info Window before it has been ' + 'initialized. Please wait for the Info Window to load before trying to interact with ' + 'it.');\n      }\n    }\n  }\n  static ɵfac = function MapInfoWindow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapInfoWindow)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapInfoWindow,\n    selectors: [[\"map-info-window\"]],\n    hostAttrs: [2, \"display\", \"none\"],\n    inputs: {\n      options: \"options\",\n      position: \"position\"\n    },\n    outputs: {\n      closeclick: \"closeclick\",\n      contentChanged: \"contentChanged\",\n      domready: \"domready\",\n      positionChanged: \"positionChanged\",\n      zindexChanged: \"zindexChanged\",\n      infoWindowInitialized: \"infoWindowInitialized\"\n    },\n    exportAs: [\"mapInfoWindow\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapInfoWindow, [{\n    type: Directive,\n    args: [{\n      selector: 'map-info-window',\n      exportAs: 'mapInfoWindow',\n      host: {\n        'style': 'display: none'\n      }\n    }]\n  }], () => [], {\n    options: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    closeclick: [{\n      type: Output\n    }],\n    contentChanged: [{\n      type: Output\n    }],\n    domready: [{\n      type: Output\n    }],\n    positionChanged: [{\n      type: Output\n    }],\n    zindexChanged: [{\n      type: Output\n    }],\n    infoWindowInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular component that renders a Google Maps KML Layer via the Google Maps JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/kml#KmlLayer\n */\nclass MapKmlLayer {\n  _map = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _eventManager = new MapEventManager(inject(NgZone));\n  _options = new BehaviorSubject({});\n  _url = new BehaviorSubject('');\n  _destroyed = new Subject();\n  /**\n   * The underlying google.maps.KmlLayer object.\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/kml#KmlLayer\n   */\n  kmlLayer;\n  set options(options) {\n    this._options.next(options || {});\n  }\n  set url(url) {\n    this._url.next(url);\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/kml#KmlLayer.click\n   */\n  kmlClick = this._eventManager.getLazyEmitter('click');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/kml\n   * #KmlLayer.defaultviewport_changed\n   */\n  defaultviewportChanged = this._eventManager.getLazyEmitter('defaultviewport_changed');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/kml#KmlLayer.status_changed\n   */\n  statusChanged = this._eventManager.getLazyEmitter('status_changed');\n  /** Event emitted when the KML layer is initialized. */\n  kmlLayerInitialized = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    if (this._map._isBrowser) {\n      this._combineOptions().pipe(take(1)).subscribe(options => {\n        if (google.maps.KmlLayer && this._map.googleMap) {\n          this._initialize(this._map.googleMap, google.maps.KmlLayer, options);\n        } else {\n          this._ngZone.runOutsideAngular(() => {\n            Promise.all([this._map._resolveMap(), google.maps.importLibrary('maps')]).then(([map, lib]) => {\n              this._initialize(map, lib.KmlLayer, options);\n            });\n          });\n        }\n      });\n    }\n  }\n  _initialize(map, layerConstructor, options) {\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.kmlLayer = new layerConstructor(options);\n      this._assertInitialized();\n      this.kmlLayer.setMap(map);\n      this._eventManager.setTarget(this.kmlLayer);\n      this.kmlLayerInitialized.emit(this.kmlLayer);\n      this._watchForOptionsChanges();\n      this._watchForUrlChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._eventManager.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.kmlLayer?.setMap(null);\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/kml#KmlLayer.getDefaultViewport\n   */\n  getDefaultViewport() {\n    this._assertInitialized();\n    return this.kmlLayer.getDefaultViewport();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/kml#KmlLayer.getMetadata\n   */\n  getMetadata() {\n    this._assertInitialized();\n    return this.kmlLayer.getMetadata();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/kml#KmlLayer.getStatus\n   */\n  getStatus() {\n    this._assertInitialized();\n    return this.kmlLayer.getStatus();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/kml#KmlLayer.getUrl\n   */\n  getUrl() {\n    this._assertInitialized();\n    return this.kmlLayer.getUrl();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/kml#KmlLayer.getZIndex\n   */\n  getZIndex() {\n    this._assertInitialized();\n    return this.kmlLayer.getZIndex();\n  }\n  _combineOptions() {\n    return combineLatest([this._options, this._url]).pipe(map(([options, url]) => {\n      const combinedOptions = {\n        ...options,\n        url: url || options.url\n      };\n      return combinedOptions;\n    }));\n  }\n  _watchForOptionsChanges() {\n    this._options.pipe(takeUntil(this._destroyed)).subscribe(options => {\n      if (this.kmlLayer) {\n        this._assertInitialized();\n        this.kmlLayer.setOptions(options);\n      }\n    });\n  }\n  _watchForUrlChanges() {\n    this._url.pipe(takeUntil(this._destroyed)).subscribe(url => {\n      if (url && this.kmlLayer) {\n        this._assertInitialized();\n        this.kmlLayer.setUrl(url);\n      }\n    });\n  }\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.kmlLayer) {\n        throw Error('Cannot interact with a Google Map KmlLayer before it has been ' + 'initialized. Please wait for the KmlLayer to load before trying to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function MapKmlLayer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapKmlLayer)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapKmlLayer,\n    selectors: [[\"map-kml-layer\"]],\n    inputs: {\n      options: \"options\",\n      url: \"url\"\n    },\n    outputs: {\n      kmlClick: \"kmlClick\",\n      defaultviewportChanged: \"defaultviewportChanged\",\n      statusChanged: \"statusChanged\",\n      kmlLayerInitialized: \"kmlLayerInitialized\"\n    },\n    exportAs: [\"mapKmlLayer\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapKmlLayer, [{\n    type: Directive,\n    args: [{\n      selector: 'map-kml-layer',\n      exportAs: 'mapKmlLayer'\n    }]\n  }], () => [], {\n    options: [{\n      type: Input\n    }],\n    url: [{\n      type: Input\n    }],\n    kmlClick: [{\n      type: Output\n    }],\n    defaultviewportChanged: [{\n      type: Output\n    }],\n    statusChanged: [{\n      type: Output\n    }],\n    kmlLayerInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Token that marker directives can use to expose themselves to the clusterer. */\nconst MAP_MARKER = new InjectionToken('MAP_MARKER');\n\n/**\n * Default options for the Google Maps marker component. Displays a marker\n * at the Googleplex.\n */\nconst DEFAULT_MARKER_OPTIONS$1 = {\n  position: {\n    lat: 37.421995,\n    lng: -122.084092\n  }\n};\n/**\n * Angular component that renders a Google Maps marker via the Google Maps JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/marker\n */\nclass MapMarker {\n  _googleMap = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _eventManager = new MapEventManager(inject(NgZone));\n  /**\n   * Title of the marker.\n   * See: developers.google.com/maps/documentation/javascript/reference/marker#MarkerOptions.title\n   */\n  set title(title) {\n    this._title = title;\n  }\n  _title;\n  /**\n   * Position of the marker. See:\n   * developers.google.com/maps/documentation/javascript/reference/marker#MarkerOptions.position\n   */\n  set position(position) {\n    this._position = position;\n  }\n  _position;\n  /**\n   * Label for the marker.\n   * See: developers.google.com/maps/documentation/javascript/reference/marker#MarkerOptions.label\n   */\n  set label(label) {\n    this._label = label;\n  }\n  _label;\n  /**\n   * Whether the marker is clickable. See:\n   * developers.google.com/maps/documentation/javascript/reference/marker#MarkerOptions.clickable\n   */\n  set clickable(clickable) {\n    this._clickable = clickable;\n  }\n  _clickable;\n  /**\n   * Options used to configure the marker.\n   * See: developers.google.com/maps/documentation/javascript/reference/marker#MarkerOptions\n   */\n  set options(options) {\n    this._options = options;\n  }\n  _options;\n  /**\n   * Icon to be used for the marker.\n   * See: https://developers.google.com/maps/documentation/javascript/reference/marker#Icon\n   */\n  set icon(icon) {\n    this._icon = icon;\n  }\n  _icon;\n  /**\n   * Whether the marker is visible.\n   * See: developers.google.com/maps/documentation/javascript/reference/marker#MarkerOptions.visible\n   */\n  set visible(value) {\n    this._visible = value;\n  }\n  _visible;\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.animation_changed\n   */\n  animationChanged = this._eventManager.getLazyEmitter('animation_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.click\n   */\n  mapClick = this._eventManager.getLazyEmitter('click');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.clickable_changed\n   */\n  clickableChanged = this._eventManager.getLazyEmitter('clickable_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.cursor_changed\n   */\n  cursorChanged = this._eventManager.getLazyEmitter('cursor_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.dblclick\n   */\n  mapDblclick = this._eventManager.getLazyEmitter('dblclick');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.drag\n   */\n  mapDrag = this._eventManager.getLazyEmitter('drag');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.dragend\n   */\n  mapDragend = this._eventManager.getLazyEmitter('dragend');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.draggable_changed\n   */\n  draggableChanged = this._eventManager.getLazyEmitter('draggable_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.dragstart\n   */\n  mapDragstart = this._eventManager.getLazyEmitter('dragstart');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.flat_changed\n   */\n  flatChanged = this._eventManager.getLazyEmitter('flat_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.icon_changed\n   */\n  iconChanged = this._eventManager.getLazyEmitter('icon_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.mousedown\n   */\n  mapMousedown = this._eventManager.getLazyEmitter('mousedown');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.mouseout\n   */\n  mapMouseout = this._eventManager.getLazyEmitter('mouseout');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.mouseover\n   */\n  mapMouseover = this._eventManager.getLazyEmitter('mouseover');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.mouseup\n   */\n  mapMouseup = this._eventManager.getLazyEmitter('mouseup');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.position_changed\n   */\n  positionChanged = this._eventManager.getLazyEmitter('position_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.rightclick\n   */\n  mapRightclick = this._eventManager.getLazyEmitter('rightclick');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.shape_changed\n   */\n  shapeChanged = this._eventManager.getLazyEmitter('shape_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.title_changed\n   */\n  titleChanged = this._eventManager.getLazyEmitter('title_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.visible_changed\n   */\n  visibleChanged = this._eventManager.getLazyEmitter('visible_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.zindex_changed\n   */\n  zindexChanged = this._eventManager.getLazyEmitter('zindex_changed');\n  /** Event emitted when the marker is initialized. */\n  markerInitialized = new EventEmitter();\n  /**\n   * The underlying google.maps.Marker object.\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/marker#Marker\n   */\n  marker;\n  constructor() {}\n  ngOnInit() {\n    if (!this._googleMap._isBrowser) {\n      return;\n    }\n    if (google.maps.Marker && this._googleMap.googleMap) {\n      this._initialize(this._googleMap.googleMap, google.maps.Marker);\n    } else {\n      this._ngZone.runOutsideAngular(() => {\n        Promise.all([this._googleMap._resolveMap(), google.maps.importLibrary('marker')]).then(([map, lib]) => {\n          this._initialize(map, lib.Marker);\n        });\n      });\n    }\n  }\n  _initialize(map, markerConstructor) {\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.marker = new markerConstructor(this._combineOptions());\n      this._assertInitialized();\n      this.marker.setMap(map);\n      this._eventManager.setTarget(this.marker);\n      this.markerInitialized.next(this.marker);\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      marker,\n      _title,\n      _position,\n      _label,\n      _clickable,\n      _icon,\n      _visible\n    } = this;\n    if (marker) {\n      if (changes['options']) {\n        marker.setOptions(this._combineOptions());\n      }\n      if (changes['title'] && _title !== undefined) {\n        marker.setTitle(_title);\n      }\n      if (changes['position'] && _position) {\n        marker.setPosition(_position);\n      }\n      if (changes['label'] && _label !== undefined) {\n        marker.setLabel(_label);\n      }\n      if (changes['clickable'] && _clickable !== undefined) {\n        marker.setClickable(_clickable);\n      }\n      if (changes['icon'] && _icon) {\n        marker.setIcon(_icon);\n      }\n      if (changes['visible'] && _visible !== undefined) {\n        marker.setVisible(_visible);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.markerInitialized.complete();\n    this._eventManager.destroy();\n    this.marker?.setMap(null);\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getAnimation\n   */\n  getAnimation() {\n    this._assertInitialized();\n    return this.marker.getAnimation() || null;\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getClickable\n   */\n  getClickable() {\n    this._assertInitialized();\n    return this.marker.getClickable();\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getCursor\n   */\n  getCursor() {\n    this._assertInitialized();\n    return this.marker.getCursor() || null;\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getDraggable\n   */\n  getDraggable() {\n    this._assertInitialized();\n    return !!this.marker.getDraggable();\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getIcon\n   */\n  getIcon() {\n    this._assertInitialized();\n    return this.marker.getIcon() || null;\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getLabel\n   */\n  getLabel() {\n    this._assertInitialized();\n    return this.marker.getLabel() || null;\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getOpacity\n   */\n  getOpacity() {\n    this._assertInitialized();\n    return this.marker.getOpacity() || null;\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getPosition\n   */\n  getPosition() {\n    this._assertInitialized();\n    return this.marker.getPosition() || null;\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getShape\n   */\n  getShape() {\n    this._assertInitialized();\n    return this.marker.getShape() || null;\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getTitle\n   */\n  getTitle() {\n    this._assertInitialized();\n    return this.marker.getTitle() || null;\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getVisible\n   */\n  getVisible() {\n    this._assertInitialized();\n    return this.marker.getVisible();\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/marker#Marker.getZIndex\n   */\n  getZIndex() {\n    this._assertInitialized();\n    return this.marker.getZIndex() || null;\n  }\n  /** Gets the anchor point that can be used to attach other Google Maps objects. */\n  getAnchor() {\n    this._assertInitialized();\n    return this.marker;\n  }\n  /** Returns a promise that resolves when the marker has been initialized. */\n  _resolveMarker() {\n    return this.marker ? Promise.resolve(this.marker) : this.markerInitialized.pipe(take(1)).toPromise();\n  }\n  /** Creates a combined options object using the passed-in options and the individual inputs. */\n  _combineOptions() {\n    const options = this._options || DEFAULT_MARKER_OPTIONS$1;\n    return {\n      ...options,\n      title: this._title || options.title,\n      position: this._position || options.position,\n      label: this._label || options.label,\n      clickable: this._clickable ?? options.clickable,\n      map: this._googleMap.googleMap,\n      icon: this._icon || options.icon,\n      visible: this._visible ?? options.visible\n    };\n  }\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.marker) {\n        throw Error('Cannot interact with a Google Map Marker before it has been ' + 'initialized. Please wait for the Marker to load before trying to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function MapMarker_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapMarker)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapMarker,\n    selectors: [[\"map-marker\"]],\n    inputs: {\n      title: \"title\",\n      position: \"position\",\n      label: \"label\",\n      clickable: \"clickable\",\n      options: \"options\",\n      icon: \"icon\",\n      visible: \"visible\"\n    },\n    outputs: {\n      animationChanged: \"animationChanged\",\n      mapClick: \"mapClick\",\n      clickableChanged: \"clickableChanged\",\n      cursorChanged: \"cursorChanged\",\n      mapDblclick: \"mapDblclick\",\n      mapDrag: \"mapDrag\",\n      mapDragend: \"mapDragend\",\n      draggableChanged: \"draggableChanged\",\n      mapDragstart: \"mapDragstart\",\n      flatChanged: \"flatChanged\",\n      iconChanged: \"iconChanged\",\n      mapMousedown: \"mapMousedown\",\n      mapMouseout: \"mapMouseout\",\n      mapMouseover: \"mapMouseover\",\n      mapMouseup: \"mapMouseup\",\n      positionChanged: \"positionChanged\",\n      mapRightclick: \"mapRightclick\",\n      shapeChanged: \"shapeChanged\",\n      titleChanged: \"titleChanged\",\n      visibleChanged: \"visibleChanged\",\n      zindexChanged: \"zindexChanged\",\n      markerInitialized: \"markerInitialized\"\n    },\n    exportAs: [\"mapMarker\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAP_MARKER,\n      useExisting: MapMarker\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapMarker, [{\n    type: Directive,\n    args: [{\n      selector: 'map-marker',\n      exportAs: 'mapMarker',\n      providers: [{\n        provide: MAP_MARKER,\n        useExisting: MapMarker\n      }]\n    }]\n  }], () => [], {\n    title: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    clickable: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }],\n    animationChanged: [{\n      type: Output\n    }],\n    mapClick: [{\n      type: Output\n    }],\n    clickableChanged: [{\n      type: Output\n    }],\n    cursorChanged: [{\n      type: Output\n    }],\n    mapDblclick: [{\n      type: Output\n    }],\n    mapDrag: [{\n      type: Output\n    }],\n    mapDragend: [{\n      type: Output\n    }],\n    draggableChanged: [{\n      type: Output\n    }],\n    mapDragstart: [{\n      type: Output\n    }],\n    flatChanged: [{\n      type: Output\n    }],\n    iconChanged: [{\n      type: Output\n    }],\n    mapMousedown: [{\n      type: Output\n    }],\n    mapMouseout: [{\n      type: Output\n    }],\n    mapMouseover: [{\n      type: Output\n    }],\n    mapMouseup: [{\n      type: Output\n    }],\n    positionChanged: [{\n      type: Output\n    }],\n    mapRightclick: [{\n      type: Output\n    }],\n    shapeChanged: [{\n      type: Output\n    }],\n    titleChanged: [{\n      type: Output\n    }],\n    visibleChanged: [{\n      type: Output\n    }],\n    zindexChanged: [{\n      type: Output\n    }],\n    markerInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Default options for a clusterer. */\nconst DEFAULT_CLUSTERER_OPTIONS = {};\n/**\n * Angular component for implementing a Google Maps Marker Clusterer.\n * See https://developers.google.com/maps/documentation/javascript/marker-clustering\n *\n * @deprecated This component is using a deprecated clustering implementation. Use the\n *   `map-marker-clusterer` component instead.\n * @breaking-change 21.0.0\n *\n */\nclass DeprecatedMapMarkerClusterer {\n  _googleMap = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _currentMarkers = new Set();\n  _eventManager = new MapEventManager(inject(NgZone));\n  _destroy = new Subject();\n  /** Whether the clusterer is allowed to be initialized. */\n  _canInitialize = this._googleMap._isBrowser;\n  ariaLabelFn = () => '';\n  set averageCenter(averageCenter) {\n    this._averageCenter = averageCenter;\n  }\n  _averageCenter;\n  batchSize;\n  set batchSizeIE(batchSizeIE) {\n    this._batchSizeIE = batchSizeIE;\n  }\n  _batchSizeIE;\n  set calculator(calculator) {\n    this._calculator = calculator;\n  }\n  _calculator;\n  set clusterClass(clusterClass) {\n    this._clusterClass = clusterClass;\n  }\n  _clusterClass;\n  set enableRetinaIcons(enableRetinaIcons) {\n    this._enableRetinaIcons = enableRetinaIcons;\n  }\n  _enableRetinaIcons;\n  set gridSize(gridSize) {\n    this._gridSize = gridSize;\n  }\n  _gridSize;\n  set ignoreHidden(ignoreHidden) {\n    this._ignoreHidden = ignoreHidden;\n  }\n  _ignoreHidden;\n  set imageExtension(imageExtension) {\n    this._imageExtension = imageExtension;\n  }\n  _imageExtension;\n  set imagePath(imagePath) {\n    this._imagePath = imagePath;\n  }\n  _imagePath;\n  set imageSizes(imageSizes) {\n    this._imageSizes = imageSizes;\n  }\n  _imageSizes;\n  set maxZoom(maxZoom) {\n    this._maxZoom = maxZoom;\n  }\n  _maxZoom;\n  set minimumClusterSize(minimumClusterSize) {\n    this._minimumClusterSize = minimumClusterSize;\n  }\n  _minimumClusterSize;\n  set styles(styles) {\n    this._styles = styles;\n  }\n  _styles;\n  set title(title) {\n    this._title = title;\n  }\n  _title;\n  set zIndex(zIndex) {\n    this._zIndex = zIndex;\n  }\n  _zIndex;\n  set zoomOnClick(zoomOnClick) {\n    this._zoomOnClick = zoomOnClick;\n  }\n  _zoomOnClick;\n  set options(options) {\n    this._options = options;\n  }\n  _options;\n  /**\n   * See\n   * googlemaps.github.io/v3-utility-library/modules/\n   * _google_markerclustererplus.html#clusteringbegin\n   */\n  clusteringbegin = this._eventManager.getLazyEmitter('clusteringbegin');\n  /**\n   * See\n   * googlemaps.github.io/v3-utility-library/modules/_google_markerclustererplus.html#clusteringend\n   */\n  clusteringend = this._eventManager.getLazyEmitter('clusteringend');\n  /** Emits when a cluster has been clicked. */\n  clusterClick = this._eventManager.getLazyEmitter('click');\n  _markers;\n  /**\n   * The underlying MarkerClusterer object.\n   *\n   * See\n   * googlemaps.github.io/v3-utility-library/classes/\n   * _google_markerclustererplus.markerclusterer.html\n   */\n  markerClusterer;\n  /** Event emitted when the clusterer is initialized. */\n  markerClustererInitialized = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    if (this._canInitialize) {\n      this._ngZone.runOutsideAngular(() => {\n        this._googleMap._resolveMap().then(map => {\n          if (typeof MarkerClusterer !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('MarkerClusterer class not found, cannot construct a marker cluster. ' + 'Please install the MarkerClustererPlus library: ' + 'https://github.com/googlemaps/js-markerclustererplus');\n          }\n          // Create the object outside the zone so its events don't trigger change detection.\n          // We'll bring it back in inside the `MapEventManager` only for the events that the\n          // user has subscribed to.\n          this.markerClusterer = this._ngZone.runOutsideAngular(() => {\n            return new MarkerClusterer(map, [], this._combineOptions());\n          });\n          this._assertInitialized();\n          this._eventManager.setTarget(this.markerClusterer);\n          this.markerClustererInitialized.emit(this.markerClusterer);\n        });\n      });\n    }\n  }\n  ngAfterContentInit() {\n    if (this._canInitialize) {\n      if (this.markerClusterer) {\n        this._watchForMarkerChanges();\n      } else {\n        this.markerClustererInitialized.pipe(take(1), takeUntil(this._destroy)).subscribe(() => this._watchForMarkerChanges());\n      }\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      markerClusterer: clusterer,\n      ariaLabelFn,\n      _averageCenter,\n      _batchSizeIE,\n      _calculator,\n      _styles,\n      _clusterClass,\n      _enableRetinaIcons,\n      _gridSize,\n      _ignoreHidden,\n      _imageExtension,\n      _imagePath,\n      _imageSizes,\n      _maxZoom,\n      _minimumClusterSize,\n      _title,\n      _zIndex,\n      _zoomOnClick\n    } = this;\n    if (clusterer) {\n      if (changes['options']) {\n        clusterer.setOptions(this._combineOptions());\n      }\n      if (changes['ariaLabelFn']) {\n        clusterer.ariaLabelFn = ariaLabelFn;\n      }\n      if (changes['averageCenter'] && _averageCenter !== undefined) {\n        clusterer.setAverageCenter(_averageCenter);\n      }\n      if (changes['batchSizeIE'] && _batchSizeIE !== undefined) {\n        clusterer.setBatchSizeIE(_batchSizeIE);\n      }\n      if (changes['calculator'] && !!_calculator) {\n        clusterer.setCalculator(_calculator);\n      }\n      if (changes['clusterClass'] && _clusterClass !== undefined) {\n        clusterer.setClusterClass(_clusterClass);\n      }\n      if (changes['enableRetinaIcons'] && _enableRetinaIcons !== undefined) {\n        clusterer.setEnableRetinaIcons(_enableRetinaIcons);\n      }\n      if (changes['gridSize'] && _gridSize !== undefined) {\n        clusterer.setGridSize(_gridSize);\n      }\n      if (changes['ignoreHidden'] && _ignoreHidden !== undefined) {\n        clusterer.setIgnoreHidden(_ignoreHidden);\n      }\n      if (changes['imageExtension'] && _imageExtension !== undefined) {\n        clusterer.setImageExtension(_imageExtension);\n      }\n      if (changes['imagePath'] && _imagePath !== undefined) {\n        clusterer.setImagePath(_imagePath);\n      }\n      if (changes['imageSizes'] && _imageSizes) {\n        clusterer.setImageSizes(_imageSizes);\n      }\n      if (changes['maxZoom'] && _maxZoom !== undefined) {\n        clusterer.setMaxZoom(_maxZoom);\n      }\n      if (changes['minimumClusterSize'] && _minimumClusterSize !== undefined) {\n        clusterer.setMinimumClusterSize(_minimumClusterSize);\n      }\n      if (changes['styles'] && _styles) {\n        clusterer.setStyles(_styles);\n      }\n      if (changes['title'] && _title !== undefined) {\n        clusterer.setTitle(_title);\n      }\n      if (changes['zIndex'] && _zIndex !== undefined) {\n        clusterer.setZIndex(_zIndex);\n      }\n      if (changes['zoomOnClick'] && _zoomOnClick !== undefined) {\n        clusterer.setZoomOnClick(_zoomOnClick);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._destroy.next();\n    this._destroy.complete();\n    this._eventManager.destroy();\n    this.markerClusterer?.setMap(null);\n  }\n  fitMapToMarkers(padding) {\n    this._assertInitialized();\n    this.markerClusterer.fitMapToMarkers(padding);\n  }\n  getAverageCenter() {\n    this._assertInitialized();\n    return this.markerClusterer.getAverageCenter();\n  }\n  getBatchSizeIE() {\n    this._assertInitialized();\n    return this.markerClusterer.getBatchSizeIE();\n  }\n  getCalculator() {\n    this._assertInitialized();\n    return this.markerClusterer.getCalculator();\n  }\n  getClusterClass() {\n    this._assertInitialized();\n    return this.markerClusterer.getClusterClass();\n  }\n  getClusters() {\n    this._assertInitialized();\n    return this.markerClusterer.getClusters();\n  }\n  getEnableRetinaIcons() {\n    this._assertInitialized();\n    return this.markerClusterer.getEnableRetinaIcons();\n  }\n  getGridSize() {\n    this._assertInitialized();\n    return this.markerClusterer.getGridSize();\n  }\n  getIgnoreHidden() {\n    this._assertInitialized();\n    return this.markerClusterer.getIgnoreHidden();\n  }\n  getImageExtension() {\n    this._assertInitialized();\n    return this.markerClusterer.getImageExtension();\n  }\n  getImagePath() {\n    this._assertInitialized();\n    return this.markerClusterer.getImagePath();\n  }\n  getImageSizes() {\n    this._assertInitialized();\n    return this.markerClusterer.getImageSizes();\n  }\n  getMaxZoom() {\n    this._assertInitialized();\n    return this.markerClusterer.getMaxZoom();\n  }\n  getMinimumClusterSize() {\n    this._assertInitialized();\n    return this.markerClusterer.getMinimumClusterSize();\n  }\n  getStyles() {\n    this._assertInitialized();\n    return this.markerClusterer.getStyles();\n  }\n  getTitle() {\n    this._assertInitialized();\n    return this.markerClusterer.getTitle();\n  }\n  getTotalClusters() {\n    this._assertInitialized();\n    return this.markerClusterer.getTotalClusters();\n  }\n  getTotalMarkers() {\n    this._assertInitialized();\n    return this.markerClusterer.getTotalMarkers();\n  }\n  getZIndex() {\n    this._assertInitialized();\n    return this.markerClusterer.getZIndex();\n  }\n  getZoomOnClick() {\n    this._assertInitialized();\n    return this.markerClusterer.getZoomOnClick();\n  }\n  _combineOptions() {\n    const options = this._options || DEFAULT_CLUSTERER_OPTIONS;\n    return {\n      ...options,\n      ariaLabelFn: this.ariaLabelFn ?? options.ariaLabelFn,\n      averageCenter: this._averageCenter ?? options.averageCenter,\n      batchSize: this.batchSize ?? options.batchSize,\n      batchSizeIE: this._batchSizeIE ?? options.batchSizeIE,\n      calculator: this._calculator ?? options.calculator,\n      clusterClass: this._clusterClass ?? options.clusterClass,\n      enableRetinaIcons: this._enableRetinaIcons ?? options.enableRetinaIcons,\n      gridSize: this._gridSize ?? options.gridSize,\n      ignoreHidden: this._ignoreHidden ?? options.ignoreHidden,\n      imageExtension: this._imageExtension ?? options.imageExtension,\n      imagePath: this._imagePath ?? options.imagePath,\n      imageSizes: this._imageSizes ?? options.imageSizes,\n      maxZoom: this._maxZoom ?? options.maxZoom,\n      minimumClusterSize: this._minimumClusterSize ?? options.minimumClusterSize,\n      styles: this._styles ?? options.styles,\n      title: this._title ?? options.title,\n      zIndex: this._zIndex ?? options.zIndex,\n      zoomOnClick: this._zoomOnClick ?? options.zoomOnClick\n    };\n  }\n  _watchForMarkerChanges() {\n    this._assertInitialized();\n    this._ngZone.runOutsideAngular(() => {\n      this._getInternalMarkers(this._markers).then(markers => {\n        const initialMarkers = [];\n        for (const marker of markers) {\n          this._currentMarkers.add(marker);\n          initialMarkers.push(marker);\n        }\n        this.markerClusterer.addMarkers(initialMarkers);\n      });\n    });\n    this._markers.changes.pipe(takeUntil(this._destroy)).subscribe(markerComponents => {\n      this._assertInitialized();\n      this._ngZone.runOutsideAngular(() => {\n        this._getInternalMarkers(markerComponents).then(markers => {\n          const newMarkers = new Set(markers);\n          const markersToAdd = [];\n          const markersToRemove = [];\n          for (const marker of Array.from(newMarkers)) {\n            if (!this._currentMarkers.has(marker)) {\n              this._currentMarkers.add(marker);\n              markersToAdd.push(marker);\n            }\n          }\n          for (const marker of Array.from(this._currentMarkers)) {\n            if (!newMarkers.has(marker)) {\n              markersToRemove.push(marker);\n            }\n          }\n          this.markerClusterer.addMarkers(markersToAdd, true);\n          this.markerClusterer.removeMarkers(markersToRemove, true);\n          this.markerClusterer.repaint();\n          for (const marker of markersToRemove) {\n            this._currentMarkers.delete(marker);\n          }\n        });\n      });\n    });\n  }\n  _getInternalMarkers(markers) {\n    return Promise.all(markers.map(markerComponent => markerComponent._resolveMarker()));\n  }\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.markerClusterer) {\n        throw Error('Cannot interact with a MarkerClusterer before it has been initialized. ' + 'Please wait for the MarkerClusterer to load before trying to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function DeprecatedMapMarkerClusterer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DeprecatedMapMarkerClusterer)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: DeprecatedMapMarkerClusterer,\n    selectors: [[\"deprecated-map-marker-clusterer\"]],\n    contentQueries: function DeprecatedMapMarkerClusterer_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MapMarker, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._markers = _t);\n      }\n    },\n    inputs: {\n      ariaLabelFn: \"ariaLabelFn\",\n      averageCenter: \"averageCenter\",\n      batchSize: \"batchSize\",\n      batchSizeIE: \"batchSizeIE\",\n      calculator: \"calculator\",\n      clusterClass: \"clusterClass\",\n      enableRetinaIcons: \"enableRetinaIcons\",\n      gridSize: \"gridSize\",\n      ignoreHidden: \"ignoreHidden\",\n      imageExtension: \"imageExtension\",\n      imagePath: \"imagePath\",\n      imageSizes: \"imageSizes\",\n      maxZoom: \"maxZoom\",\n      minimumClusterSize: \"minimumClusterSize\",\n      styles: \"styles\",\n      title: \"title\",\n      zIndex: \"zIndex\",\n      zoomOnClick: \"zoomOnClick\",\n      options: \"options\"\n    },\n    outputs: {\n      clusteringbegin: \"clusteringbegin\",\n      clusteringend: \"clusteringend\",\n      clusterClick: \"clusterClick\",\n      markerClustererInitialized: \"markerClustererInitialized\"\n    },\n    exportAs: [\"mapMarkerClusterer\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function DeprecatedMapMarkerClusterer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DeprecatedMapMarkerClusterer, [{\n    type: Component,\n    args: [{\n      selector: 'deprecated-map-marker-clusterer',\n      exportAs: 'mapMarkerClusterer',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: '<ng-content/>',\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], () => [], {\n    ariaLabelFn: [{\n      type: Input\n    }],\n    averageCenter: [{\n      type: Input\n    }],\n    batchSize: [{\n      type: Input\n    }],\n    batchSizeIE: [{\n      type: Input\n    }],\n    calculator: [{\n      type: Input\n    }],\n    clusterClass: [{\n      type: Input\n    }],\n    enableRetinaIcons: [{\n      type: Input\n    }],\n    gridSize: [{\n      type: Input\n    }],\n    ignoreHidden: [{\n      type: Input\n    }],\n    imageExtension: [{\n      type: Input\n    }],\n    imagePath: [{\n      type: Input\n    }],\n    imageSizes: [{\n      type: Input\n    }],\n    maxZoom: [{\n      type: Input\n    }],\n    minimumClusterSize: [{\n      type: Input\n    }],\n    styles: [{\n      type: Input\n    }],\n    title: [{\n      type: Input\n    }],\n    zIndex: [{\n      type: Input\n    }],\n    zoomOnClick: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    clusteringbegin: [{\n      type: Output\n    }],\n    clusteringend: [{\n      type: Output\n    }],\n    clusterClick: [{\n      type: Output\n    }],\n    _markers: [{\n      type: ContentChildren,\n      args: [MapMarker, {\n        descendants: true\n      }]\n    }],\n    markerClustererInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular component that renders a Google Maps Polygon via the Google Maps JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon\n */\nclass MapPolygon {\n  _map = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _eventManager = new MapEventManager(inject(NgZone));\n  _options = new BehaviorSubject({});\n  _paths = new BehaviorSubject(undefined);\n  _destroyed = new Subject();\n  /**\n   * The underlying google.maps.Polygon object.\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon\n   */\n  polygon;\n  set options(options) {\n    this._options.next(options || {});\n  }\n  set paths(paths) {\n    this._paths.next(paths);\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.click\n   */\n  polygonClick = this._eventManager.getLazyEmitter('click');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.dblclick\n   */\n  polygonDblclick = this._eventManager.getLazyEmitter('dblclick');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.drag\n   */\n  polygonDrag = this._eventManager.getLazyEmitter('drag');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.dragend\n   */\n  polygonDragend = this._eventManager.getLazyEmitter('dragend');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.dragstart\n   */\n  polygonDragstart = this._eventManager.getLazyEmitter('dragstart');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.mousedown\n   */\n  polygonMousedown = this._eventManager.getLazyEmitter('mousedown');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.mousemove\n   */\n  polygonMousemove = this._eventManager.getLazyEmitter('mousemove');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.mouseout\n   */\n  polygonMouseout = this._eventManager.getLazyEmitter('mouseout');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.mouseover\n   */\n  polygonMouseover = this._eventManager.getLazyEmitter('mouseover');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.mouseup\n   */\n  polygonMouseup = this._eventManager.getLazyEmitter('mouseup');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.rightclick\n   */\n  polygonRightclick = this._eventManager.getLazyEmitter('rightclick');\n  /** Event emitted when the polygon is initialized. */\n  polygonInitialized = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    if (this._map._isBrowser) {\n      this._combineOptions().pipe(take(1)).subscribe(options => {\n        if (google.maps.Polygon && this._map.googleMap) {\n          this._initialize(this._map.googleMap, google.maps.Polygon, options);\n        } else {\n          this._ngZone.runOutsideAngular(() => {\n            Promise.all([this._map._resolveMap(), google.maps.importLibrary('maps')]).then(([map, lib]) => {\n              this._initialize(map, lib.Polygon, options);\n            });\n          });\n        }\n      });\n    }\n  }\n  _initialize(map, polygonConstructor, options) {\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.polygon = new polygonConstructor(options);\n      this._assertInitialized();\n      this.polygon.setMap(map);\n      this._eventManager.setTarget(this.polygon);\n      this.polygonInitialized.emit(this.polygon);\n      this._watchForOptionsChanges();\n      this._watchForPathChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._eventManager.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.polygon?.setMap(null);\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.getDraggable\n   */\n  getDraggable() {\n    this._assertInitialized();\n    return this.polygon.getDraggable();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.getEditable\n   */\n  getEditable() {\n    this._assertInitialized();\n    return this.polygon.getEditable();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.getPath\n   */\n  getPath() {\n    this._assertInitialized();\n    return this.polygon.getPath();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.getPaths\n   */\n  getPaths() {\n    this._assertInitialized();\n    return this.polygon.getPaths();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polygon.getVisible\n   */\n  getVisible() {\n    this._assertInitialized();\n    return this.polygon.getVisible();\n  }\n  _combineOptions() {\n    return combineLatest([this._options, this._paths]).pipe(map(([options, paths]) => {\n      const combinedOptions = {\n        ...options,\n        paths: paths || options.paths\n      };\n      return combinedOptions;\n    }));\n  }\n  _watchForOptionsChanges() {\n    this._options.pipe(takeUntil(this._destroyed)).subscribe(options => {\n      this._assertInitialized();\n      this.polygon.setOptions(options);\n    });\n  }\n  _watchForPathChanges() {\n    this._paths.pipe(takeUntil(this._destroyed)).subscribe(paths => {\n      if (paths) {\n        this._assertInitialized();\n        this.polygon.setPaths(paths);\n      }\n    });\n  }\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.polygon) {\n        throw Error('Cannot interact with a Google Map Polygon before it has been ' + 'initialized. Please wait for the Polygon to load before trying to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function MapPolygon_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapPolygon)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapPolygon,\n    selectors: [[\"map-polygon\"]],\n    inputs: {\n      options: \"options\",\n      paths: \"paths\"\n    },\n    outputs: {\n      polygonClick: \"polygonClick\",\n      polygonDblclick: \"polygonDblclick\",\n      polygonDrag: \"polygonDrag\",\n      polygonDragend: \"polygonDragend\",\n      polygonDragstart: \"polygonDragstart\",\n      polygonMousedown: \"polygonMousedown\",\n      polygonMousemove: \"polygonMousemove\",\n      polygonMouseout: \"polygonMouseout\",\n      polygonMouseover: \"polygonMouseover\",\n      polygonMouseup: \"polygonMouseup\",\n      polygonRightclick: \"polygonRightclick\",\n      polygonInitialized: \"polygonInitialized\"\n    },\n    exportAs: [\"mapPolygon\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapPolygon, [{\n    type: Directive,\n    args: [{\n      selector: 'map-polygon',\n      exportAs: 'mapPolygon'\n    }]\n  }], () => [], {\n    options: [{\n      type: Input\n    }],\n    paths: [{\n      type: Input\n    }],\n    polygonClick: [{\n      type: Output\n    }],\n    polygonDblclick: [{\n      type: Output\n    }],\n    polygonDrag: [{\n      type: Output\n    }],\n    polygonDragend: [{\n      type: Output\n    }],\n    polygonDragstart: [{\n      type: Output\n    }],\n    polygonMousedown: [{\n      type: Output\n    }],\n    polygonMousemove: [{\n      type: Output\n    }],\n    polygonMouseout: [{\n      type: Output\n    }],\n    polygonMouseover: [{\n      type: Output\n    }],\n    polygonMouseup: [{\n      type: Output\n    }],\n    polygonRightclick: [{\n      type: Output\n    }],\n    polygonInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular component that renders a Google Maps Polyline via the Google Maps JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline\n */\nclass MapPolyline {\n  _map = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _eventManager = new MapEventManager(inject(NgZone));\n  _options = new BehaviorSubject({});\n  _path = new BehaviorSubject(undefined);\n  _destroyed = new Subject();\n  /**\n   * The underlying google.maps.Polyline object.\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline\n   */\n  polyline;\n  set options(options) {\n    this._options.next(options || {});\n  }\n  set path(path) {\n    this._path.next(path);\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.click\n   */\n  polylineClick = this._eventManager.getLazyEmitter('click');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.dblclick\n   */\n  polylineDblclick = this._eventManager.getLazyEmitter('dblclick');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.drag\n   */\n  polylineDrag = this._eventManager.getLazyEmitter('drag');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.dragend\n   */\n  polylineDragend = this._eventManager.getLazyEmitter('dragend');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.dragstart\n   */\n  polylineDragstart = this._eventManager.getLazyEmitter('dragstart');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.mousedown\n   */\n  polylineMousedown = this._eventManager.getLazyEmitter('mousedown');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.mousemove\n   */\n  polylineMousemove = this._eventManager.getLazyEmitter('mousemove');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.mouseout\n   */\n  polylineMouseout = this._eventManager.getLazyEmitter('mouseout');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.mouseover\n   */\n  polylineMouseover = this._eventManager.getLazyEmitter('mouseover');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.mouseup\n   */\n  polylineMouseup = this._eventManager.getLazyEmitter('mouseup');\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.rightclick\n   */\n  polylineRightclick = this._eventManager.getLazyEmitter('rightclick');\n  /** Event emitted when the polyline is initialized. */\n  polylineInitialized = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    if (this._map._isBrowser) {\n      this._combineOptions().pipe(take(1)).subscribe(options => {\n        if (google.maps.Polyline && this._map.googleMap) {\n          this._initialize(this._map.googleMap, google.maps.Polyline, options);\n        } else {\n          this._ngZone.runOutsideAngular(() => {\n            Promise.all([this._map._resolveMap(), google.maps.importLibrary('maps')]).then(([map, lib]) => {\n              this._initialize(map, lib.Polyline, options);\n            });\n          });\n        }\n      });\n    }\n  }\n  _initialize(map, polylineConstructor, options) {\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.polyline = new polylineConstructor(options);\n      this._assertInitialized();\n      this.polyline.setMap(map);\n      this._eventManager.setTarget(this.polyline);\n      this.polylineInitialized.emit(this.polyline);\n      this._watchForOptionsChanges();\n      this._watchForPathChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._eventManager.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.polyline?.setMap(null);\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.getDraggable\n   */\n  getDraggable() {\n    this._assertInitialized();\n    return this.polyline.getDraggable();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.getEditable\n   */\n  getEditable() {\n    this._assertInitialized();\n    return this.polyline.getEditable();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.getPath\n   */\n  getPath() {\n    this._assertInitialized();\n    return this.polyline.getPath();\n  }\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Polyline.getVisible\n   */\n  getVisible() {\n    this._assertInitialized();\n    return this.polyline.getVisible();\n  }\n  _combineOptions() {\n    return combineLatest([this._options, this._path]).pipe(map(([options, path]) => {\n      const combinedOptions = {\n        ...options,\n        path: path || options.path\n      };\n      return combinedOptions;\n    }));\n  }\n  _watchForOptionsChanges() {\n    this._options.pipe(takeUntil(this._destroyed)).subscribe(options => {\n      this._assertInitialized();\n      this.polyline.setOptions(options);\n    });\n  }\n  _watchForPathChanges() {\n    this._path.pipe(takeUntil(this._destroyed)).subscribe(path => {\n      if (path) {\n        this._assertInitialized();\n        this.polyline.setPath(path);\n      }\n    });\n  }\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.polyline) {\n        throw Error('Cannot interact with a Google Map Polyline before it has been ' + 'initialized. Please wait for the Polyline to load before trying to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function MapPolyline_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapPolyline)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapPolyline,\n    selectors: [[\"map-polyline\"]],\n    inputs: {\n      options: \"options\",\n      path: \"path\"\n    },\n    outputs: {\n      polylineClick: \"polylineClick\",\n      polylineDblclick: \"polylineDblclick\",\n      polylineDrag: \"polylineDrag\",\n      polylineDragend: \"polylineDragend\",\n      polylineDragstart: \"polylineDragstart\",\n      polylineMousedown: \"polylineMousedown\",\n      polylineMousemove: \"polylineMousemove\",\n      polylineMouseout: \"polylineMouseout\",\n      polylineMouseover: \"polylineMouseover\",\n      polylineMouseup: \"polylineMouseup\",\n      polylineRightclick: \"polylineRightclick\",\n      polylineInitialized: \"polylineInitialized\"\n    },\n    exportAs: [\"mapPolyline\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapPolyline, [{\n    type: Directive,\n    args: [{\n      selector: 'map-polyline',\n      exportAs: 'mapPolyline'\n    }]\n  }], () => [], {\n    options: [{\n      type: Input\n    }],\n    path: [{\n      type: Input\n    }],\n    polylineClick: [{\n      type: Output\n    }],\n    polylineDblclick: [{\n      type: Output\n    }],\n    polylineDrag: [{\n      type: Output\n    }],\n    polylineDragend: [{\n      type: Output\n    }],\n    polylineDragstart: [{\n      type: Output\n    }],\n    polylineMousedown: [{\n      type: Output\n    }],\n    polylineMousemove: [{\n      type: Output\n    }],\n    polylineMouseout: [{\n      type: Output\n    }],\n    polylineMouseover: [{\n      type: Output\n    }],\n    polylineMouseup: [{\n      type: Output\n    }],\n    polylineRightclick: [{\n      type: Output\n    }],\n    polylineInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular component that renders a Google Maps Rectangle via the Google Maps JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle\n */\nclass MapRectangle {\n  _map = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _eventManager = new MapEventManager(inject(NgZone));\n  _options = new BehaviorSubject({});\n  _bounds = new BehaviorSubject(undefined);\n  _destroyed = new Subject();\n  /**\n   * The underlying google.maps.Rectangle object.\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle\n   */\n  rectangle;\n  set options(options) {\n    this._options.next(options || {});\n  }\n  set bounds(bounds) {\n    this._bounds.next(bounds);\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.boundsChanged\n   */\n  boundsChanged = this._eventManager.getLazyEmitter('bounds_changed');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.click\n   */\n  rectangleClick = this._eventManager.getLazyEmitter('click');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.dblclick\n   */\n  rectangleDblclick = this._eventManager.getLazyEmitter('dblclick');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.drag\n   */\n  rectangleDrag = this._eventManager.getLazyEmitter('drag');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.dragend\n   */\n  rectangleDragend = this._eventManager.getLazyEmitter('dragend');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.dragstart\n   */\n  rectangleDragstart = this._eventManager.getLazyEmitter('dragstart');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.mousedown\n   */\n  rectangleMousedown = this._eventManager.getLazyEmitter('mousedown');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.mousemove\n   */\n  rectangleMousemove = this._eventManager.getLazyEmitter('mousemove');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.mouseout\n   */\n  rectangleMouseout = this._eventManager.getLazyEmitter('mouseout');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.mouseover\n   */\n  rectangleMouseover = this._eventManager.getLazyEmitter('mouseover');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.mouseup\n   */\n  rectangleMouseup = this._eventManager.getLazyEmitter('mouseup');\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.rightclick\n   */\n  rectangleRightclick = this._eventManager.getLazyEmitter('rightclick');\n  /** Event emitted when the rectangle is initialized. */\n  rectangleInitialized = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    if (this._map._isBrowser) {\n      this._combineOptions().pipe(take(1)).subscribe(options => {\n        if (google.maps.Rectangle && this._map.googleMap) {\n          this._initialize(this._map.googleMap, google.maps.Rectangle, options);\n        } else {\n          this._ngZone.runOutsideAngular(() => {\n            Promise.all([this._map._resolveMap(), google.maps.importLibrary('maps')]).then(([map, lib]) => {\n              this._initialize(map, lib.Rectangle, options);\n            });\n          });\n        }\n      });\n    }\n  }\n  _initialize(map, rectangleConstructor, options) {\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.rectangle = new rectangleConstructor(options);\n      this._assertInitialized();\n      this.rectangle.setMap(map);\n      this._eventManager.setTarget(this.rectangle);\n      this.rectangleInitialized.emit(this.rectangle);\n      this._watchForOptionsChanges();\n      this._watchForBoundsChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._eventManager.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.rectangle?.setMap(null);\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.getBounds\n   */\n  getBounds() {\n    this._assertInitialized();\n    return this.rectangle.getBounds();\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.getDraggable\n   */\n  getDraggable() {\n    this._assertInitialized();\n    return this.rectangle.getDraggable();\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.getEditable\n   */\n  getEditable() {\n    this._assertInitialized();\n    return this.rectangle.getEditable();\n  }\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/polygon#Rectangle.getVisible\n   */\n  getVisible() {\n    this._assertInitialized();\n    return this.rectangle.getVisible();\n  }\n  _combineOptions() {\n    return combineLatest([this._options, this._bounds]).pipe(map(([options, bounds]) => {\n      const combinedOptions = {\n        ...options,\n        bounds: bounds || options.bounds\n      };\n      return combinedOptions;\n    }));\n  }\n  _watchForOptionsChanges() {\n    this._options.pipe(takeUntil(this._destroyed)).subscribe(options => {\n      this._assertInitialized();\n      this.rectangle.setOptions(options);\n    });\n  }\n  _watchForBoundsChanges() {\n    this._bounds.pipe(takeUntil(this._destroyed)).subscribe(bounds => {\n      if (bounds) {\n        this._assertInitialized();\n        this.rectangle.setBounds(bounds);\n      }\n    });\n  }\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.rectangle) {\n        throw Error('Cannot interact with a Google Map Rectangle before it has been initialized. ' + 'Please wait for the Rectangle to load before trying to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function MapRectangle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapRectangle)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapRectangle,\n    selectors: [[\"map-rectangle\"]],\n    inputs: {\n      options: \"options\",\n      bounds: \"bounds\"\n    },\n    outputs: {\n      boundsChanged: \"boundsChanged\",\n      rectangleClick: \"rectangleClick\",\n      rectangleDblclick: \"rectangleDblclick\",\n      rectangleDrag: \"rectangleDrag\",\n      rectangleDragend: \"rectangleDragend\",\n      rectangleDragstart: \"rectangleDragstart\",\n      rectangleMousedown: \"rectangleMousedown\",\n      rectangleMousemove: \"rectangleMousemove\",\n      rectangleMouseout: \"rectangleMouseout\",\n      rectangleMouseover: \"rectangleMouseover\",\n      rectangleMouseup: \"rectangleMouseup\",\n      rectangleRightclick: \"rectangleRightclick\",\n      rectangleInitialized: \"rectangleInitialized\"\n    },\n    exportAs: [\"mapRectangle\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapRectangle, [{\n    type: Directive,\n    args: [{\n      selector: 'map-rectangle',\n      exportAs: 'mapRectangle'\n    }]\n  }], () => [], {\n    options: [{\n      type: Input\n    }],\n    bounds: [{\n      type: Input\n    }],\n    boundsChanged: [{\n      type: Output\n    }],\n    rectangleClick: [{\n      type: Output\n    }],\n    rectangleDblclick: [{\n      type: Output\n    }],\n    rectangleDrag: [{\n      type: Output\n    }],\n    rectangleDragend: [{\n      type: Output\n    }],\n    rectangleDragstart: [{\n      type: Output\n    }],\n    rectangleMousedown: [{\n      type: Output\n    }],\n    rectangleMousemove: [{\n      type: Output\n    }],\n    rectangleMouseout: [{\n      type: Output\n    }],\n    rectangleMouseover: [{\n      type: Output\n    }],\n    rectangleMouseup: [{\n      type: Output\n    }],\n    rectangleRightclick: [{\n      type: Output\n    }],\n    rectangleInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular component that renders a Google Maps Traffic Layer via the Google Maps JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/map#TrafficLayer\n */\nclass MapTrafficLayer {\n  _map = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _autoRefresh = new BehaviorSubject(true);\n  _destroyed = new Subject();\n  /**\n   * The underlying google.maps.TrafficLayer object.\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/map#TrafficLayer\n   */\n  trafficLayer;\n  /**\n   * Whether the traffic layer refreshes with updated information automatically.\n   */\n  set autoRefresh(autoRefresh) {\n    this._autoRefresh.next(autoRefresh);\n  }\n  /** Event emitted when the traffic layer is initialized. */\n  trafficLayerInitialized = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    if (this._map._isBrowser) {\n      this._combineOptions().pipe(take(1)).subscribe(options => {\n        if (google.maps.TrafficLayer && this._map.googleMap) {\n          this._initialize(this._map.googleMap, google.maps.TrafficLayer, options);\n        } else {\n          this._ngZone.runOutsideAngular(() => {\n            Promise.all([this._map._resolveMap(), google.maps.importLibrary('maps')]).then(([map, lib]) => {\n              this._initialize(map, lib.TrafficLayer, options);\n            });\n          });\n        }\n      });\n    }\n  }\n  _initialize(map, layerConstructor, options) {\n    this._ngZone.runOutsideAngular(() => {\n      this.trafficLayer = new layerConstructor(options);\n      this._assertInitialized();\n      this.trafficLayer.setMap(map);\n      this.trafficLayerInitialized.emit(this.trafficLayer);\n      this._watchForAutoRefreshChanges();\n    });\n  }\n  ngOnDestroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.trafficLayer?.setMap(null);\n  }\n  _combineOptions() {\n    return this._autoRefresh.pipe(map(autoRefresh => {\n      const combinedOptions = {\n        autoRefresh\n      };\n      return combinedOptions;\n    }));\n  }\n  _watchForAutoRefreshChanges() {\n    this._combineOptions().pipe(takeUntil(this._destroyed)).subscribe(options => {\n      this._assertInitialized();\n      this.trafficLayer.setOptions(options);\n    });\n  }\n  _assertInitialized() {\n    if (!this.trafficLayer) {\n      throw Error('Cannot interact with a Google Map Traffic Layer before it has been initialized. ' + 'Please wait for the Traffic Layer to load before trying to interact with it.');\n    }\n  }\n  static ɵfac = function MapTrafficLayer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapTrafficLayer)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapTrafficLayer,\n    selectors: [[\"map-traffic-layer\"]],\n    inputs: {\n      autoRefresh: \"autoRefresh\"\n    },\n    outputs: {\n      trafficLayerInitialized: \"trafficLayerInitialized\"\n    },\n    exportAs: [\"mapTrafficLayer\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapTrafficLayer, [{\n    type: Directive,\n    args: [{\n      selector: 'map-traffic-layer',\n      exportAs: 'mapTrafficLayer'\n    }]\n  }], () => [], {\n    autoRefresh: [{\n      type: Input\n    }],\n    trafficLayerInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular component that renders a Google Maps Transit Layer via the Google Maps JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/map#TransitLayer\n */\nclass MapTransitLayer {\n  _map = inject(GoogleMap);\n  _zone = inject(NgZone);\n  /**\n   * The underlying google.maps.TransitLayer object.\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/map#TransitLayer\n   */\n  transitLayer;\n  /** Event emitted when the transit layer is initialized. */\n  transitLayerInitialized = new EventEmitter();\n  ngOnInit() {\n    if (this._map._isBrowser) {\n      if (google.maps.TransitLayer && this._map.googleMap) {\n        this._initialize(this._map.googleMap, google.maps.TransitLayer);\n      } else {\n        this._zone.runOutsideAngular(() => {\n          Promise.all([this._map._resolveMap(), google.maps.importLibrary('maps')]).then(([map, lib]) => {\n            this._initialize(map, lib.TransitLayer);\n          });\n        });\n      }\n    }\n  }\n  _initialize(map, layerConstructor) {\n    this._zone.runOutsideAngular(() => {\n      this.transitLayer = new layerConstructor();\n      this.transitLayerInitialized.emit(this.transitLayer);\n      this._assertLayerInitialized();\n      this.transitLayer.setMap(map);\n    });\n  }\n  ngOnDestroy() {\n    this.transitLayer?.setMap(null);\n  }\n  _assertLayerInitialized() {\n    if (!this.transitLayer) {\n      throw Error('Cannot interact with a Google Map Transit Layer before it has been initialized. ' + 'Please wait for the Transit Layer to load before trying to interact with it.');\n    }\n  }\n  static ɵfac = function MapTransitLayer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapTransitLayer)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapTransitLayer,\n    selectors: [[\"map-transit-layer\"]],\n    outputs: {\n      transitLayerInitialized: \"transitLayerInitialized\"\n    },\n    exportAs: [\"mapTransitLayer\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapTransitLayer, [{\n    type: Directive,\n    args: [{\n      selector: 'map-transit-layer',\n      exportAs: 'mapTransitLayer'\n    }]\n  }], null, {\n    transitLayerInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular directive that renders a Google Maps heatmap via the Google Maps JavaScript API.\n *\n * See: https://developers.google.com/maps/documentation/javascript/reference/visualization\n */\nclass MapHeatmapLayer {\n  _googleMap = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  /**\n   * Data shown on the heatmap.\n   * See: https://developers.google.com/maps/documentation/javascript/reference/visualization\n   */\n  set data(data) {\n    this._data = data;\n  }\n  _data;\n  /**\n   * Options used to configure the heatmap. See:\n   * developers.google.com/maps/documentation/javascript/reference/visualization#HeatmapLayerOptions\n   */\n  set options(options) {\n    this._options = options;\n  }\n  _options;\n  /**\n   * The underlying google.maps.visualization.HeatmapLayer object.\n   *\n   * See: https://developers.google.com/maps/documentation/javascript/reference/visualization\n   */\n  heatmap;\n  /** Event emitted when the heatmap is initialized. */\n  heatmapInitialized = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    if (this._googleMap._isBrowser) {\n      if (!window.google?.maps?.visualization && !window.google?.maps.importLibrary && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Namespace `google.maps.visualization` not found, cannot construct heatmap. ' + 'Please install the Google Maps JavaScript API with the \"visualization\" library: ' + 'https://developers.google.com/maps/documentation/javascript/visualization');\n      }\n      if (google.maps.visualization?.HeatmapLayer && this._googleMap.googleMap) {\n        this._initialize(this._googleMap.googleMap, google.maps.visualization.HeatmapLayer);\n      } else {\n        this._ngZone.runOutsideAngular(() => {\n          Promise.all([this._googleMap._resolveMap(), google.maps.importLibrary('visualization')]).then(([map, lib]) => {\n            this._initialize(map, lib.HeatmapLayer);\n          });\n        });\n      }\n    }\n  }\n  _initialize(map, heatmapConstructor) {\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.heatmap = new heatmapConstructor(this._combineOptions());\n      this._assertInitialized();\n      this.heatmap.setMap(map);\n      this.heatmapInitialized.emit(this.heatmap);\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      _data,\n      heatmap\n    } = this;\n    if (heatmap) {\n      if (changes['options']) {\n        heatmap.setOptions(this._combineOptions());\n      }\n      if (changes['data'] && _data !== undefined) {\n        heatmap.setData(this._normalizeData(_data));\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.heatmap?.setMap(null);\n  }\n  /**\n   * Gets the data that is currently shown on the heatmap.\n   * See: developers.google.com/maps/documentation/javascript/reference/visualization#HeatmapLayer\n   */\n  getData() {\n    this._assertInitialized();\n    return this.heatmap.getData();\n  }\n  /** Creates a combined options object using the passed-in options and the individual inputs. */\n  _combineOptions() {\n    const options = this._options || {};\n    return {\n      ...options,\n      data: this._normalizeData(this._data || options.data || []),\n      map: this._googleMap.googleMap\n    };\n  }\n  /**\n   * Most Google Maps APIs support both `LatLng` objects and `LatLngLiteral`. The latter is more\n   * convenient to write out, because the Google Maps API doesn't have to have been loaded in order\n   * to construct them. The `HeatmapLayer` appears to be an exception that only allows a `LatLng`\n   * object, or it throws a runtime error. Since it's more convenient and we expect that Angular\n   * users will load the API asynchronously, we allow them to pass in a `LatLngLiteral` and we\n   * convert it to a `LatLng` object before passing it off to Google Maps.\n   */\n  _normalizeData(data) {\n    const result = [];\n    data.forEach(item => {\n      result.push(isLatLngLiteral(item) ? new google.maps.LatLng(item.lat, item.lng) : item);\n    });\n    return result;\n  }\n  /** Asserts that the heatmap object has been initialized. */\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.heatmap) {\n        throw Error('Cannot interact with a Google Map HeatmapLayer before it has been ' + 'initialized. Please wait for the heatmap to load before trying to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function MapHeatmapLayer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapHeatmapLayer)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapHeatmapLayer,\n    selectors: [[\"map-heatmap-layer\"]],\n    inputs: {\n      data: \"data\",\n      options: \"options\"\n    },\n    outputs: {\n      heatmapInitialized: \"heatmapInitialized\"\n    },\n    exportAs: [\"mapHeatmapLayer\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapHeatmapLayer, [{\n    type: Directive,\n    args: [{\n      selector: 'map-heatmap-layer',\n      exportAs: 'mapHeatmapLayer'\n    }]\n  }], () => [], {\n    data: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    heatmapInitialized: [{\n      type: Output\n    }]\n  });\n})();\n/** Asserts that an object is a `LatLngLiteral`. */\nfunction isLatLngLiteral(value) {\n  return value && typeof value.lat === 'number' && typeof value.lng === 'number';\n}\n\n/**\n * Default options for the Google Maps marker component. Displays a marker\n * at the Googleplex.\n */\nconst DEFAULT_MARKER_OPTIONS = {\n  position: {\n    lat: 37.221995,\n    lng: -122.184092\n  }\n};\n/**\n * Angular component that renders a Google Maps marker via the Google Maps JavaScript API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/marker\n */\nclass MapAdvancedMarker {\n  _googleMap = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _eventManager = new MapEventManager(inject(NgZone));\n  /**\n   * Rollover text. If provided, an accessibility text (e.g. for use with screen readers) will be added to the AdvancedMarkerElement with the provided value.\n   * See: https://developers.google.com/maps/documentation/javascript/reference/advanced-markers#AdvancedMarkerElementOptions.title\n   */\n  set title(title) {\n    this._title = title;\n  }\n  _title;\n  /**\n   * Sets the AdvancedMarkerElement's position. An AdvancedMarkerElement may be constructed without a position, but will not be displayed until its position is provided - for example, by a user's actions or choices. An AdvancedMarkerElement's position can be provided by setting AdvancedMarkerElement.position if not provided at the construction.\n   * Note: AdvancedMarkerElement with altitude is only supported on vector maps.\n   * https://developers.google.com/maps/documentation/javascript/reference/advanced-markers#AdvancedMarkerElementOptions.position\n   */\n  set position(position) {\n    this._position = position;\n  }\n  _position;\n  /**\n   * The DOM Element backing the visual of an AdvancedMarkerElement.\n   * Note: AdvancedMarkerElement does not clone the passed-in DOM element. Once the DOM element is passed to an AdvancedMarkerElement, passing the same DOM element to another AdvancedMarkerElement will move the DOM element and cause the previous AdvancedMarkerElement to look empty.\n   * See: https://developers.google.com/maps/documentation/javascript/reference/advanced-markers#AdvancedMarkerElementOptions.content\n   */\n  set content(content) {\n    this._content = content;\n  }\n  _content;\n  /**\n   * If true, the AdvancedMarkerElement can be dragged.\n   * Note: AdvancedMarkerElement with altitude is not draggable.\n   * https://developers.google.com/maps/documentation/javascript/reference/advanced-markers#AdvancedMarkerElementOptions.gmpDraggable\n   */\n  set gmpDraggable(draggable) {\n    this._draggable = draggable;\n  }\n  _draggable;\n  /**\n   * Options for constructing an AdvancedMarkerElement.\n   * https://developers.google.com/maps/documentation/javascript/reference/advanced-markers#AdvancedMarkerElementOptions\n   */\n  set options(options) {\n    this._options = options;\n  }\n  _options;\n  /**\n   * AdvancedMarkerElements on the map are prioritized by zIndex, with higher values indicating higher display.\n   * https://developers.google.com/maps/documentation/javascript/reference/advanced-markers#AdvancedMarkerElementOptions.zIndex\n   */\n  set zIndex(zIndex) {\n    this._zIndex = zIndex;\n  }\n  _zIndex;\n  /**\n   * This event is fired when the AdvancedMarkerElement element is clicked.\n   * https://developers.google.com/maps/documentation/javascript/reference/advanced-markers#AdvancedMarkerElement.click\n   */\n  mapClick = this._eventManager.getLazyEmitter('click');\n  /**\n   * This event is fired when the AdvancedMarkerElement is double-clicked.\n   */\n  mapDblclick = this._eventManager.getLazyEmitter('dblclick');\n  /**\n   * This event is fired when the mouse moves out of the AdvancedMarkerElement.\n   */\n  mapMouseout = this._eventManager.getLazyEmitter('mouseout');\n  /**\n   * This event is fired when the mouse moves over the AdvancedMarkerElement.\n   */\n  mapMouseover = this._eventManager.getLazyEmitter('mouseover');\n  /**\n   * This event is fired when the mouse button is released over the AdvancedMarkerElement.\n   */\n  mapMouseup = this._eventManager.getLazyEmitter('mouseup');\n  /**\n   * This event is fired when the AdvancedMarkerElement is right-clicked.\n   */\n  mapRightclick = this._eventManager.getLazyEmitter('rightclick');\n  /**\n   * This event is repeatedly fired while the user drags the AdvancedMarkerElement.\n   * https://developers.google.com/maps/documentation/javascript/reference/advanced-markers#AdvancedMarkerElement.drag\n   */\n  mapDrag = this._eventManager.getLazyEmitter('drag');\n  /**\n   * This event is fired when the user stops dragging the AdvancedMarkerElement.\n   * https://developers.google.com/maps/documentation/javascript/reference/advanced-markers#AdvancedMarkerElement.dragend\n   */\n  mapDragend = this._eventManager.getLazyEmitter('dragend');\n  /**\n   * This event is fired when the user starts dragging the AdvancedMarkerElement.\n   * https://developers.google.com/maps/documentation/javascript/reference/advanced-markers#AdvancedMarkerElement.dragstart\n   */\n  mapDragstart = this._eventManager.getLazyEmitter('dragstart');\n  /** Event emitted when the marker is initialized. */\n  markerInitialized = new EventEmitter();\n  /**\n   * The underlying google.maps.marker.AdvancedMarkerElement object.\n   *\n   * See developers.google.com/maps/documentation/javascript/reference/advanced-markers#AdvancedMarkerElement\n   */\n  advancedMarker;\n  constructor() {}\n  ngOnInit() {\n    if (!this._googleMap._isBrowser) {\n      return;\n    }\n    if (google.maps.marker?.AdvancedMarkerElement && this._googleMap.googleMap) {\n      this._initialize(this._googleMap.googleMap, google.maps.marker.AdvancedMarkerElement);\n    } else {\n      this._ngZone.runOutsideAngular(() => {\n        Promise.all([this._googleMap._resolveMap(), google.maps.importLibrary('marker')]).then(([map, lib]) => {\n          this._initialize(map, lib.AdvancedMarkerElement);\n        });\n      });\n    }\n  }\n  _initialize(map, advancedMarkerConstructor) {\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.advancedMarker = new advancedMarkerConstructor(this._combineOptions());\n      this._assertInitialized();\n      this.advancedMarker.map = map;\n      this._eventManager.setTarget(this.advancedMarker);\n      this.markerInitialized.next(this.advancedMarker);\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      advancedMarker,\n      _content,\n      _position,\n      _title,\n      _draggable,\n      _zIndex\n    } = this;\n    if (advancedMarker) {\n      if (changes['title']) {\n        advancedMarker.title = _title;\n      }\n      if (changes['gmpDraggable']) {\n        advancedMarker.gmpDraggable = _draggable;\n      }\n      if (changes['content']) {\n        advancedMarker.content = _content;\n      }\n      if (changes['position']) {\n        advancedMarker.position = _position;\n      }\n      if (changes['zIndex']) {\n        advancedMarker.zIndex = _zIndex;\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.markerInitialized.complete();\n    this._eventManager.destroy();\n    if (this.advancedMarker) {\n      this.advancedMarker.map = null;\n    }\n  }\n  getAnchor() {\n    this._assertInitialized();\n    return this.advancedMarker;\n  }\n  /** Returns a promise that resolves when the marker has been initialized. */\n  _resolveMarker() {\n    return this.advancedMarker ? Promise.resolve(this.advancedMarker) : this.markerInitialized.pipe(take(1)).toPromise();\n  }\n  /** Creates a combined options object using the passed-in options and the individual inputs. */\n  _combineOptions() {\n    const options = this._options || DEFAULT_MARKER_OPTIONS;\n    return {\n      ...options,\n      title: this._title || options.title,\n      position: this._position || options.position,\n      content: this._content || options.content,\n      zIndex: this._zIndex ?? options.zIndex,\n      gmpDraggable: this._draggable ?? options.gmpDraggable,\n      map: this._googleMap.googleMap\n    };\n  }\n  /** Asserts that the map has been initialized. */\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this.advancedMarker) {\n        throw Error('Cannot interact with a Google Map Marker before it has been ' + 'initialized. Please wait for the Marker to load before trying to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function MapAdvancedMarker_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapAdvancedMarker)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MapAdvancedMarker,\n    selectors: [[\"map-advanced-marker\"]],\n    inputs: {\n      title: \"title\",\n      position: \"position\",\n      content: \"content\",\n      gmpDraggable: \"gmpDraggable\",\n      options: \"options\",\n      zIndex: \"zIndex\"\n    },\n    outputs: {\n      mapClick: \"mapClick\",\n      mapDblclick: \"mapDblclick\",\n      mapMouseout: \"mapMouseout\",\n      mapMouseover: \"mapMouseover\",\n      mapMouseup: \"mapMouseup\",\n      mapRightclick: \"mapRightclick\",\n      mapDrag: \"mapDrag\",\n      mapDragend: \"mapDragend\",\n      mapDragstart: \"mapDragstart\",\n      markerInitialized: \"markerInitialized\"\n    },\n    exportAs: [\"mapAdvancedMarker\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAP_MARKER,\n      useExisting: MapAdvancedMarker\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapAdvancedMarker, [{\n    type: Directive,\n    args: [{\n      selector: 'map-advanced-marker',\n      exportAs: 'mapAdvancedMarker',\n      providers: [{\n        provide: MAP_MARKER,\n        useExisting: MapAdvancedMarker\n      }]\n    }]\n  }], () => [], {\n    title: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    content: [{\n      type: Input\n    }],\n    gmpDraggable: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    zIndex: [{\n      type: Input\n    }],\n    mapClick: [{\n      type: Output\n    }],\n    mapDblclick: [{\n      type: Output\n    }],\n    mapMouseout: [{\n      type: Output\n    }],\n    mapMouseover: [{\n      type: Output\n    }],\n    mapMouseup: [{\n      type: Output\n    }],\n    mapRightclick: [{\n      type: Output\n    }],\n    mapDrag: [{\n      type: Output\n    }],\n    mapDragend: [{\n      type: Output\n    }],\n    mapDragstart: [{\n      type: Output\n    }],\n    markerInitialized: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Angular component for implementing a Google Maps Marker Clusterer.\n *\n * See https://developers.google.com/maps/documentation/javascript/marker-clustering\n */\nclass MapMarkerClusterer {\n  _googleMap = inject(GoogleMap);\n  _ngZone = inject(NgZone);\n  _currentMarkers = new Set();\n  _closestMapEventManager = new MapEventManager(this._ngZone);\n  _markersSubscription = Subscription.EMPTY;\n  /** Whether the clusterer is allowed to be initialized. */\n  _canInitialize = this._googleMap._isBrowser;\n  /**\n   * Used to customize how the marker cluster is rendered.\n   * See https://googlemaps.github.io/js-markerclusterer/interfaces/Renderer.html.\n   */\n  renderer;\n  /**\n   * Algorithm used to cluster the markers.\n   * See https://googlemaps.github.io/js-markerclusterer/interfaces/Algorithm.html.\n   */\n  algorithm;\n  /** Emits when clustering has started. */\n  clusteringbegin = this._closestMapEventManager.getLazyEmitter('clusteringbegin');\n  /** Emits when clustering is done. */\n  clusteringend = this._closestMapEventManager.getLazyEmitter('clusteringend');\n  /** Emits when a cluster has been clicked. */\n  clusterClick = new EventEmitter();\n  /** Event emitted when the marker clusterer is initialized. */\n  markerClustererInitialized = new EventEmitter();\n  _markers;\n  /** Underlying MarkerClusterer object used to interact with Google Maps. */\n  markerClusterer;\n  async ngOnInit() {\n    if (this._canInitialize) {\n      await this._createCluster();\n      // The `clusteringbegin` and `clusteringend` events are\n      // emitted on the map so that's why set it as the target.\n      this._closestMapEventManager.setTarget(this._googleMap.googleMap);\n    }\n  }\n  async ngOnChanges(changes) {\n    const change = changes['renderer'] || changes['algorithm'];\n    // Since the options are set in the constructor, we have to recreate the cluster if they change.\n    if (this.markerClusterer && change && !change.isFirstChange()) {\n      await this._createCluster();\n    }\n  }\n  ngOnDestroy() {\n    this._markersSubscription.unsubscribe();\n    this._closestMapEventManager.destroy();\n    this._destroyCluster();\n  }\n  async _createCluster() {\n    if (!markerClusterer?.MarkerClusterer && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('MarkerClusterer class not found, cannot construct a marker cluster. ' + 'Please install the MarkerClusterer library: ' + 'https://github.com/googlemaps/js-markerclusterer');\n    }\n    const map = await this._googleMap._resolveMap();\n    this._destroyCluster();\n    // Create the object outside the zone so its events don't trigger change detection.\n    // We'll bring it back in inside the `MapEventManager` only for the events that the\n    // user has subscribed to.\n    this._ngZone.runOutsideAngular(() => {\n      this.markerClusterer = new markerClusterer.MarkerClusterer({\n        map,\n        renderer: this.renderer,\n        algorithm: this.algorithm,\n        onClusterClick: (event, cluster, map) => {\n          if (this.clusterClick.observers.length) {\n            this._ngZone.run(() => this.clusterClick.emit(cluster));\n          } else {\n            markerClusterer.defaultOnClusterClickHandler(event, cluster, map);\n          }\n        }\n      });\n      this.markerClustererInitialized.emit(this.markerClusterer);\n    });\n    await this._watchForMarkerChanges();\n  }\n  async _watchForMarkerChanges() {\n    this._assertInitialized();\n    const initialMarkers = [];\n    const markers = await this._getInternalMarkers(this._markers.toArray());\n    for (const marker of markers) {\n      this._currentMarkers.add(marker);\n      initialMarkers.push(marker);\n    }\n    this.markerClusterer.addMarkers(initialMarkers);\n    this._markersSubscription.unsubscribe();\n    this._markersSubscription = this._markers.changes.subscribe(async markerComponents => {\n      this._assertInitialized();\n      const newMarkers = new Set(await this._getInternalMarkers(markerComponents));\n      const markersToAdd = [];\n      const markersToRemove = [];\n      for (const marker of Array.from(newMarkers)) {\n        if (!this._currentMarkers.has(marker)) {\n          this._currentMarkers.add(marker);\n          markersToAdd.push(marker);\n        }\n      }\n      for (const marker of Array.from(this._currentMarkers)) {\n        if (!newMarkers.has(marker)) {\n          markersToRemove.push(marker);\n        }\n      }\n      this.markerClusterer.addMarkers(markersToAdd, true);\n      this.markerClusterer.removeMarkers(markersToRemove, true);\n      this.markerClusterer.render();\n      for (const marker of markersToRemove) {\n        this._currentMarkers.delete(marker);\n      }\n    });\n  }\n  _destroyCluster() {\n    // TODO(crisbeto): the naming here seems odd, but the `MarkerCluster` method isn't\n    // exposed. All this method seems to do at the time of writing is to call into `reset`.\n    // See: https://github.com/googlemaps/js-markerclusterer/blob/main/src/markerclusterer.ts#L205\n    this.markerClusterer?.onRemove();\n    this.markerClusterer = undefined;\n  }\n  _getInternalMarkers(markers) {\n    return Promise.all(markers.map(marker => marker._resolveMarker()));\n  }\n  _assertInitialized() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._googleMap.googleMap) {\n        throw Error('Cannot access Google Map information before the API has been initialized. ' + 'Please wait for the API to load before trying to interact with it.');\n      }\n      if (!this.markerClusterer) {\n        throw Error('Cannot interact with a MarkerClusterer before it has been initialized. ' + 'Please wait for the MarkerClusterer to load before trying to interact with it.');\n      }\n    }\n  }\n  static ɵfac = function MapMarkerClusterer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapMarkerClusterer)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MapMarkerClusterer,\n    selectors: [[\"map-marker-clusterer\"]],\n    contentQueries: function MapMarkerClusterer_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MAP_MARKER, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._markers = _t);\n      }\n    },\n    inputs: {\n      renderer: \"renderer\",\n      algorithm: \"algorithm\"\n    },\n    outputs: {\n      clusteringbegin: \"clusteringbegin\",\n      clusteringend: \"clusteringend\",\n      clusterClick: \"clusterClick\",\n      markerClustererInitialized: \"markerClustererInitialized\"\n    },\n    exportAs: [\"mapMarkerClusterer\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MapMarkerClusterer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapMarkerClusterer, [{\n    type: Component,\n    args: [{\n      selector: 'map-marker-clusterer',\n      exportAs: 'mapMarkerClusterer',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: '<ng-content/>',\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, {\n    renderer: [{\n      type: Input\n    }],\n    algorithm: [{\n      type: Input\n    }],\n    clusteringbegin: [{\n      type: Output\n    }],\n    clusteringend: [{\n      type: Output\n    }],\n    clusterClick: [{\n      type: Output\n    }],\n    markerClustererInitialized: [{\n      type: Output\n    }],\n    _markers: [{\n      type: ContentChildren,\n      args: [MAP_MARKER, {\n        descendants: true\n      }]\n    }]\n  });\n})();\nconst COMPONENTS = [GoogleMap, MapBaseLayer, MapBicyclingLayer, MapCircle, MapDirectionsRenderer, MapGroundOverlay, MapHeatmapLayer, MapInfoWindow, MapKmlLayer, MapMarker, MapAdvancedMarker, DeprecatedMapMarkerClusterer, MapPolygon, MapPolyline, MapRectangle, MapTrafficLayer, MapTransitLayer, MapMarkerClusterer];\nclass GoogleMapsModule {\n  static ɵfac = function GoogleMapsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || GoogleMapsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: GoogleMapsModule,\n    imports: [GoogleMap, MapBaseLayer, MapBicyclingLayer, MapCircle, MapDirectionsRenderer, MapGroundOverlay, MapHeatmapLayer, MapInfoWindow, MapKmlLayer, MapMarker, MapAdvancedMarker, DeprecatedMapMarkerClusterer, MapPolygon, MapPolyline, MapRectangle, MapTrafficLayer, MapTransitLayer, MapMarkerClusterer],\n    exports: [GoogleMap, MapBaseLayer, MapBicyclingLayer, MapCircle, MapDirectionsRenderer, MapGroundOverlay, MapHeatmapLayer, MapInfoWindow, MapKmlLayer, MapMarker, MapAdvancedMarker, DeprecatedMapMarkerClusterer, MapPolygon, MapPolyline, MapRectangle, MapTrafficLayer, MapTransitLayer, MapMarkerClusterer]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GoogleMapsModule, [{\n    type: NgModule,\n    args: [{\n      imports: COMPONENTS,\n      exports: COMPONENTS\n    }]\n  }], null, null);\n})();\n\n/**\n * Angular service that wraps the Google Maps DirectionsService from the Google Maps JavaScript\n * API.\n *\n * See developers.google.com/maps/documentation/javascript/reference/directions#DirectionsService\n */\nclass MapDirectionsService {\n  _ngZone = inject(NgZone);\n  _directionsService;\n  constructor() {}\n  /**\n   * See\n   * developers.google.com/maps/documentation/javascript/reference/directions\n   * #DirectionsService.route\n   */\n  route(request) {\n    return new Observable(observer => {\n      this._getService().then(service => {\n        service.route(request, (result, status) => {\n          this._ngZone.run(() => {\n            observer.next({\n              result: result || undefined,\n              status\n            });\n            observer.complete();\n          });\n        });\n      });\n    });\n  }\n  _getService() {\n    if (!this._directionsService) {\n      if (google.maps.DirectionsService) {\n        this._directionsService = new google.maps.DirectionsService();\n      } else {\n        return google.maps.importLibrary('routes').then(lib => {\n          this._directionsService = new lib.DirectionsService();\n          return this._directionsService;\n        });\n      }\n    }\n    return Promise.resolve(this._directionsService);\n  }\n  static ɵfac = function MapDirectionsService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapDirectionsService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MapDirectionsService,\n    factory: MapDirectionsService.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapDirectionsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Angular service that wraps the Google Maps Geocoder from the Google Maps JavaScript API.\n * See developers.google.com/maps/documentation/javascript/reference/geocoder#Geocoder\n */\nclass MapGeocoder {\n  _ngZone = inject(NgZone);\n  _geocoder;\n  constructor() {}\n  /**\n   * See developers.google.com/maps/documentation/javascript/reference/geocoder#Geocoder.geocode\n   */\n  geocode(request) {\n    return new Observable(observer => {\n      this._getGeocoder().then(geocoder => {\n        geocoder.geocode(request, (results, status) => {\n          this._ngZone.run(() => {\n            observer.next({\n              results: results || [],\n              status\n            });\n            observer.complete();\n          });\n        });\n      });\n    });\n  }\n  _getGeocoder() {\n    if (!this._geocoder) {\n      if (google.maps.Geocoder) {\n        this._geocoder = new google.maps.Geocoder();\n      } else {\n        return google.maps.importLibrary('geocoding').then(lib => {\n          this._geocoder = new lib.Geocoder();\n          return this._geocoder;\n        });\n      }\n    }\n    return Promise.resolve(this._geocoder);\n  }\n  static ɵfac = function MapGeocoder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MapGeocoder)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MapGeocoder,\n    factory: MapGeocoder.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MapGeocoder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { DeprecatedMapMarkerClusterer, GoogleMap, GoogleMapsModule, MapAdvancedMarker, MapBaseLayer, MapBicyclingLayer, MapCircle, MapDirectionsRenderer, MapDirectionsService, MapEventManager, MapGeocoder, MapGroundOverlay, MapHeatmapLayer, MapInfoWindow, MapKmlLayer, MapMarker, MapMarkerClusterer, MapPolygon, MapPolyline, MapRectangle, MapTrafficLayer, MapTransitLayer };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA;AAAA,EAEA,WAAW,CAAC;AAAA,EACZ,aAAa,CAAC;AAAA,EACd,gBAAgB,IAAI,gBAAgB,MAAS;AAAA;AAAA,EAE7C,kBAAkB;AAChB,eAAW,YAAY,KAAK,YAAY;AACtC,eAAS,OAAO;AAAA,IAClB;AACA,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA,EAEA,eAAe,MAAM;AACnB,WAAO,KAAK,cAAc,KAAK,UAAU,YAAU;AACjD,YAAM,aAAa,IAAI,WAAW,cAAY;AAE5C,YAAI,CAAC,QAAQ;AACX,eAAK,SAAS,KAAK;AAAA,YACjB;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,QACT;AACA,cAAM,WAAW,OAAO,YAAY,MAAM,WAAS;AACjD,eAAK,QAAQ,IAAI,MAAM,SAAS,KAAK,KAAK,CAAC;AAAA,QAC7C,CAAC;AAGD,YAAI,CAAC,UAAU;AACb,mBAAS,SAAS;AAClB,iBAAO;AAAA,QACT;AACA,aAAK,WAAW,KAAK,QAAQ;AAC7B,eAAO,MAAM,SAAS,OAAO;AAAA,MAC/B,CAAC;AACD,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA,EAEA,UAAU,QAAQ;AAChB,UAAM,gBAAgB,KAAK,cAAc;AACzC,QAAI,WAAW,eAAe;AAC5B;AAAA,IACF;AAEA,QAAI,eAAe;AACjB,WAAK,gBAAgB;AACrB,WAAK,WAAW,CAAC;AAAA,IACnB;AACA,SAAK,cAAc,KAAK,MAAM;AAE9B,SAAK,SAAS,QAAQ,gBAAc,WAAW,WAAW,UAAU,WAAW,QAAQ,CAAC;AACxF,SAAK,WAAW,CAAC;AAAA,EACnB;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,gBAAgB;AACrB,SAAK,WAAW,CAAC;AACjB,SAAK,cAAc,SAAS;AAAA,EAC9B;AACF;AAGA,IAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAAA,EACA,MAAM;AAAA;AAAA,EAEN,WAAW;AACb;AAEA,IAAM,iBAAiB;AAEvB,IAAM,gBAAgB;AAMtB,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,cAAc,OAAO,UAAU;AAAA,EAC/B,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA,EAClD;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,SAAS;AAAA;AAAA,EAET,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA,IAAI,KAAK,MAAM;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EACA;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW,WAAW;AAAA,EAC7B;AAAA,EACA,WAAW;AAAA;AAAA,EAEX,iBAAiB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,cAAc,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,gBAAgB,KAAK,cAAc,eAAe,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,gBAAgB,KAAK,cAAc,eAAe,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,WAAW,KAAK,cAAc,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpD,cAAc,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1D,UAAU,KAAK,cAAc,eAAe,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlD,aAAa,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxD,eAAe,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5D,iBAAiB,KAAK,cAAc,eAAe,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpE,OAAO,KAAK,cAAc,eAAe,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/C,mBAAmB,KAAK,cAAc,eAAe,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxE,eAAe,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5D,cAAc,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1D,eAAe,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5D,oBAAoB,KAAK,cAAc,eAAe,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1E,gBAAgB,KAAK,cAAc,eAAe,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,cAAc,KAAK,cAAc,eAAe,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7D,cAAc,KAAK,cAAc,eAAe,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,cAAc,KAAK,cAAc,eAAe,cAAc;AAAA,EAC9D,cAAc;AACZ,UAAM,aAAa,OAAO,WAAW;AACrC,SAAK,aAAa,kBAAkB,UAAU;AAC9C,QAAI,KAAK,YAAY;AACnB,YAAM,mBAAmB;AACzB,UAAI,CAAC,iBAAiB,WAAW,OAAO,cAAc,eAAe,YAAY;AAC/E,cAAM,MAAM,4MAA2N;AAAA,MACzO;AACA,WAAK,+BAA+B,iBAAiB;AACrD,uBAAiB,iBAAiB,MAAM;AACtC,YAAI,KAAK,8BAA8B;AACrC,eAAK,6BAA6B;AAAA,QACpC;AACA,aAAK,YAAY,KAAK;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,QAAQ,KAAK,QAAQ,OAAO,GAAG;AACzC,WAAK,SAAS;AAAA,IAChB;AACA,UAAM,YAAY,KAAK;AACvB,QAAI,WAAW;AACb,UAAI,QAAQ,SAAS,GAAG;AACtB,kBAAU,WAAW,KAAK,gBAAgB,CAAC;AAAA,MAC7C;AACA,UAAI,QAAQ,QAAQ,KAAK,KAAK,SAAS;AACrC,kBAAU,UAAU,KAAK,OAAO;AAAA,MAClC;AAEA,UAAI,QAAQ,MAAM,KAAK,KAAK,SAAS,MAAM;AACzC,kBAAU,QAAQ,KAAK,KAAK;AAAA,MAC9B;AACA,UAAI,QAAQ,WAAW,KAAK,KAAK,WAAW;AAC1C,kBAAU,aAAa,KAAK,SAAS;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AAET,QAAI,KAAK,YAAY;AACnB,WAAK,SAAS,KAAK,YAAY,cAAc,cAAc,gBAAgB;AAC3E,WAAK,SAAS;AAId,UAAI,OAAO,KAAK,KAAK;AACnB,aAAK,YAAY,OAAO,KAAK,GAAG;AAAA,MAClC,OAAO;AACL,aAAK,QAAQ,kBAAkB,MAAM;AACnC,iBAAO,KAAK,cAAc,MAAM,EAAE,KAAK,SAAO,KAAK,YAAY,IAAI,GAAG,CAAC;AAAA,QACzE,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,gBAAgB;AAC1B,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,YAAY,IAAI,eAAe,KAAK,QAAQ,KAAK,gBAAgB,CAAC;AACvE,WAAK,cAAc,UAAU,KAAK,SAAS;AAC3C,WAAK,eAAe,KAAK,KAAK,SAAS;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,SAAS;AAC7B,SAAK,cAAc,QAAQ;AAC3B,QAAI,KAAK,YAAY;AACnB,YAAM,mBAAmB;AACzB,uBAAiB,iBAAiB,KAAK;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,QAAQ,SAAS;AACzB,SAAK,mBAAmB;AACxB,SAAK,UAAU,UAAU,QAAQ,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,GAAG,GAAG;AACV,SAAK,mBAAmB;AACxB,SAAK,UAAU,MAAM,GAAG,CAAC;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ;AACZ,SAAK,mBAAmB;AACxB,SAAK,UAAU,MAAM,MAAM;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,cAAc,SAAS;AACjC,SAAK,mBAAmB;AACxB,SAAK,UAAU,YAAY,cAAc,OAAO;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,UAAU,KAAK;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,UAAU;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,kBAAkB;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,WAAW;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,aAAa;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,cAAc,KAAK;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,cAAc;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,QAAQ;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,QAAQ;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,kBAAkB;AACpB,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,YAAY,QAAQ,QAAQ,KAAK,SAAS,IAAI,KAAK,eAAe,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU;AAAA,EACxG;AAAA,EACA,WAAW;AACT,QAAI,KAAK,QAAQ;AACf,YAAM,SAAS,KAAK,OAAO;AAC3B,aAAO,SAAS,KAAK,WAAW,OAAO,KAAK,oBAAoB,KAAK,MAAM,KAAK;AAChF,aAAO,QAAQ,KAAK,UAAU,OAAO,KAAK,oBAAoB,KAAK,KAAK,KAAK;AAAA,IAC/E;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY,CAAC;AAClC,WAAO,iCACF,UADE;AAAA;AAAA;AAAA,MAIL,QAAQ,KAAK,WAAW,QAAQ,UAAU,gBAAgB;AAAA,MAC1D,MAAM,KAAK,SAAS,QAAQ,QAAQ,gBAAgB;AAAA;AAAA;AAAA,MAGpD,WAAW,KAAK,aAAa,QAAQ,aAAa,gBAAgB;AAAA,MAClE,OAAO,KAAK,SAAS,QAAQ;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,CAAC,KAAK,cAAc,OAAO,cAAc,eAAe,YAAY;AACtE,YAAM,MAAM,8IAAmJ;AAAA,IACjK;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,eAAe;AAAA,MACf,eAAe;AAAA,MACf,UAAU;AAAA,MACV,aAAa;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,aAAa;AAAA,MACb,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,oBAAoB;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,eAAe,CAAC;AAAA,IAC7B,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,UAAU,GAAG,OAAO,CAAC;AACxB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAkB;AAExB,SAAS,oBAAoB,OAAO;AAClC,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,KAAK,KAAK,IAAI,QAAQ,GAAG,KAAK;AACvD;AACA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS;AAAA,EACvB,UAAU,OAAO,MAAM;AAAA,EACvB,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,KAAK,KAAK,YAAY;AACxB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,kBAAkB;AAAA,MACzB,CAAC;AACD,WAAK,mBAAmB;AACxB,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,KAAK,WAAW;AACxB,YAAM,MAAM,8IAAmJ;AAAA,IACjK;AAAA,EACF;AAAA,EACA,oBAAoB;AAAA,EAAC;AAAA,EACrB,UAAU;AAAA,EAAC;AAAA,EACX,YAAY;AAAA,EAAC;AAAA,EACb,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,UAAU,CAAC,cAAc;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAOH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS;AAAA,EACvB,QAAQ,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB;AAAA;AAAA,EAEA,4BAA4B,IAAI,aAAa;AAAA,EAC7C,WAAW;AACT,QAAI,KAAK,KAAK,YAAY;AACxB,UAAI,OAAO,KAAK,kBAAkB,KAAK,KAAK,WAAW;AACrD,aAAK,YAAY,KAAK,KAAK,WAAW,OAAO,KAAK,cAAc;AAAA,MAClE,OAAO;AACL,aAAK,MAAM,kBAAkB,MAAM;AACjC,kBAAQ,IAAI,CAAC,KAAK,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AAC7F,iBAAK,YAAYA,MAAK,IAAI,cAAc;AAAA,UAC1C,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,kBAAkB;AACjC,SAAK,MAAM,kBAAkB,MAAM;AACjC,WAAK,iBAAiB,IAAI,iBAAiB;AAC3C,WAAK,0BAA0B,KAAK,KAAK,cAAc;AACvD,WAAK,wBAAwB;AAC7B,WAAK,eAAe,OAAOA,IAAG;AAAA,IAChC,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,OAAO,IAAI;AAAA,EAClC;AAAA,EACA,0BAA0B;AACxB,QAAI,CAAC,KAAK,gBAAgB;AACxB,YAAM,MAAM,gKAAqK;AAAA,IACnL;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,IACnC,SAAS;AAAA,MACP,2BAA2B;AAAA,IAC7B;AAAA,IACA,UAAU,CAAC,mBAAmB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,OAAO,OAAO,SAAS;AAAA,EACvB,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA,EAClD,WAAW,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACjC,UAAU,IAAI,gBAAgB,MAAS;AAAA,EACvC,UAAU,IAAI,gBAAgB,MAAS;AAAA,EACvC,aAAa,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB;AAAA;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,SAAS,KAAK,WAAW,CAAC,CAAC;AAAA,EAClC;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,QAAQ,KAAK,MAAM;AAAA,EAC1B;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,QAAQ,KAAK,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,KAAK,cAAc,eAAe,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,cAAc,KAAK,cAAc,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvD,iBAAiB,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7D,aAAa,KAAK,cAAc,eAAe,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrD,gBAAgB,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3D,kBAAkB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/D,kBAAkB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/D,kBAAkB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/D,iBAAiB,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7D,kBAAkB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/D,gBAAgB,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3D,gBAAgB,KAAK,cAAc,eAAe,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,mBAAmB,KAAK,cAAc,eAAe,YAAY;AAAA;AAAA,EAEjE,oBAAoB,IAAI,aAAa;AAAA,EACrC,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,CAAC,KAAK,KAAK,YAAY;AACzB;AAAA,IACF;AACA,SAAK,gBAAgB,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,aAAW;AACxD,UAAI,OAAO,KAAK,UAAU,KAAK,KAAK,WAAW;AAC7C,aAAK,YAAY,KAAK,KAAK,WAAW,OAAO,KAAK,QAAQ,OAAO;AAAA,MACnE,OAAO;AACL,aAAK,QAAQ,kBAAkB,MAAM;AACnC,kBAAQ,IAAI,CAAC,KAAK,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AAC7F,iBAAK,YAAYA,MAAK,IAAI,QAAQ,OAAO;AAAA,UAC3C,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAYA,MAAK,mBAAmB,SAAS;AAI3C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,SAAS,IAAI,kBAAkB,OAAO;AAC3C,WAAK,mBAAmB;AACxB,WAAK,OAAO,OAAOA,IAAG;AACtB,WAAK,cAAc,UAAU,KAAK,MAAM;AACxC,WAAK,kBAAkB,KAAK,KAAK,MAAM;AACvC,WAAK,wBAAwB;AAC7B,WAAK,uBAAuB;AAC5B,WAAK,uBAAuB;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,QAAQ;AAC3B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,QAAQ,OAAO,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,UAAU;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,UAAU;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,YAAY;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,UAAU;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,WAAW;AAAA,EAChC;AAAA,EACA,kBAAkB;AAChB,WAAO,cAAc,CAAC,KAAK,UAAU,KAAK,SAAS,KAAK,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,SAAS,QAAQ,MAAM,MAAM;AACxG,YAAM,kBAAkB,iCACnB,UADmB;AAAA,QAEtB,QAAQ,UAAU,QAAQ;AAAA,QAC1B,QAAQ,WAAW,SAAY,SAAS,QAAQ;AAAA,MAClD;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,0BAA0B;AACxB,SAAK,SAAS,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,aAAW;AAClE,WAAK,mBAAmB;AACxB,WAAK,OAAO,WAAW,OAAO;AAAA,IAChC,CAAC;AAAA,EACH;AAAA,EACA,yBAAyB;AACvB,SAAK,QAAQ,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AAChE,UAAI,QAAQ;AACV,aAAK,mBAAmB;AACxB,aAAK,OAAO,UAAU,MAAM;AAAA,MAC9B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,yBAAyB;AACvB,SAAK,QAAQ,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AAChE,UAAI,WAAW,QAAW;AACxB,aAAK,mBAAmB;AACxB,aAAK,OAAO,UAAU,MAAM;AAAA,MAC9B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,QAAQ;AAChB,cAAM,MAAM,gJAAqJ;AAAA,MACnK;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,MACP,eAAe;AAAA,MACf,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,aAAa,OAAO,SAAS;AAAA,EAC7B,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlD,IAAI,WAAW,YAAY;AACzB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,KAAK,cAAc,eAAe,oBAAoB;AAAA;AAAA,EAE1E,gCAAgC,IAAI,aAAa;AAAA;AAAA,EAEjD;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,KAAK,WAAW,YAAY;AAC9B,UAAI,OAAO,KAAK,sBAAsB,KAAK,WAAW,WAAW;AAC/D,aAAK,YAAY,KAAK,WAAW,WAAW,OAAO,KAAK,kBAAkB;AAAA,MAC5E,OAAO;AACL,aAAK,QAAQ,kBAAkB,MAAM;AACnC,kBAAQ,IAAI,CAAC,KAAK,WAAW,YAAY,GAAG,OAAO,KAAK,cAAc,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AACrG,iBAAK,YAAYA,MAAK,IAAI,kBAAkB;AAAA,UAC9C,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,qBAAqB;AAIpC,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,qBAAqB,IAAI,oBAAoB,KAAK,gBAAgB,CAAC;AACxE,WAAK,mBAAmB;AACxB,WAAK,mBAAmB,OAAOA,IAAG;AAClC,WAAK,cAAc,UAAU,KAAK,kBAAkB;AACpD,WAAK,8BAA8B,KAAK,KAAK,kBAAkB;AAAA,IACjE,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,KAAK,oBAAoB;AAC3B,UAAI,QAAQ,SAAS,GAAG;AACtB,aAAK,mBAAmB,WAAW,KAAK,gBAAgB,CAAC;AAAA,MAC3D;AACA,UAAI,QAAQ,YAAY,KAAK,KAAK,gBAAgB,QAAW;AAC3D,aAAK,mBAAmB,cAAc,KAAK,WAAW;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,QAAQ;AAC3B,SAAK,oBAAoB,OAAO,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,mBAAmB;AACxB,WAAO,KAAK,mBAAmB,cAAc;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,SAAK,mBAAmB;AACxB,WAAO,KAAK,mBAAmB,SAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,mBAAmB;AACxB,WAAO,KAAK,mBAAmB,cAAc;AAAA,EAC/C;AAAA,EACA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY,CAAC;AAClC,WAAO,iCACF,UADE;AAAA,MAEL,YAAY,KAAK,eAAe,QAAQ;AAAA,MACxC,KAAK,KAAK,WAAW;AAAA,IACvB;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,oBAAoB;AAC5B,cAAM,MAAM,0KAAoL;AAAA,MAClM;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,IACvC,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,mBAAmB;AAAA,MACnB,+BAA+B;AAAA,IACjC;AAAA,IACA,UAAU,CAAC,uBAAuB;AAAA,IAClC,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,+BAA+B,CAAC;AAAA,MAC9B,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS;AAAA,EACvB,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA,EAClD,WAAW,IAAI,gBAAgB,CAAC;AAAA,EAChC,OAAO,IAAI,gBAAgB,EAAE;AAAA,EAC7B,UAAU,IAAI,gBAAgB,MAAS;AAAA,EACvC,aAAa,IAAI,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA,EAEA,IAAI,IAAI,KAAK;AACX,SAAK,KAAK,KAAK,GAAG;AAAA,EACpB;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,QAAQ,KAAK,MAAM;AAAA,EAC1B;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,IAAI,QAAQ,SAAS;AACnB,SAAK,SAAS,KAAK,OAAO;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,KAAK,cAAc,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpD,cAAc,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA,EAE1D,2BAA2B,IAAI,aAAa;AAAA,EAC5C,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,KAAK,KAAK,YAAY;AAIxB,WAAK,QAAQ,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AAChE,YAAI,KAAK,eAAe;AACtB,eAAK,cAAc,OAAO,IAAI;AAC9B,eAAK,gBAAgB;AAAA,QACvB;AACA,YAAI,CAAC,QAAQ;AACX;AAAA,QACF;AACA,YAAI,OAAO,KAAK,iBAAiB,KAAK,KAAK,WAAW;AACpD,eAAK,YAAY,KAAK,KAAK,WAAW,OAAO,KAAK,eAAe,MAAM;AAAA,QACzE,OAAO;AACL,eAAK,QAAQ,kBAAkB,MAAM;AACnC,oBAAQ,IAAI,CAAC,KAAK,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AAC7F,mBAAK,YAAYA,MAAK,IAAI,eAAe,MAAM;AAAA,YACjD,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,oBAAoB,QAAQ;AAI3C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,gBAAgB,IAAI,mBAAmB,KAAK,KAAK,SAAS,GAAG,QAAQ;AAAA,QACxE,WAAW,KAAK;AAAA,QAChB,SAAS,KAAK,SAAS;AAAA,MACzB,CAAC;AACD,WAAK,mBAAmB;AACxB,WAAK,cAAc,OAAOA,IAAG;AAC7B,WAAK,cAAc,UAAU,KAAK,aAAa;AAC/C,WAAK,yBAAyB,KAAK,KAAK,aAAa;AAErD,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,eAAe;AACpB,aAAK,wBAAwB;AAC7B,aAAK,oBAAoB;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,QAAQ;AAC3B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,eAAe,OAAO,IAAI;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,cAAc,UAAU;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACX,SAAK,mBAAmB;AACxB,WAAO,KAAK,cAAc,WAAW;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,SAAK,mBAAmB;AACxB,WAAO,KAAK,cAAc,OAAO;AAAA,EACnC;AAAA,EACA,0BAA0B;AACxB,SAAK,SAAS,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,aAAW;AAClE,UAAI,WAAW,MAAM;AACnB,aAAK,eAAe,WAAW,OAAO;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,SAAK,KAAK,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,SAAO;AAC1D,YAAM,UAAU,KAAK;AACrB,UAAI,SAAS;AACX,gBAAQ,IAAI,OAAO,GAAG;AAEtB,gBAAQ,OAAO,IAAI;AACnB,gBAAQ,OAAO,KAAK,KAAK,SAAS;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,eAAe;AACvB,cAAM,MAAM,8JAAmK;AAAA,MACjL;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,IAClC,QAAQ;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,aAAa;AAAA,MACb,0BAA0B;AAAA,IAC5B;AAAA,IACA,UAAU,CAAC,kBAAkB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,aAAa,OAAO,SAAS;AAAA,EAC7B,cAAc,OAAO,UAAU;AAAA,EAC/B,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA,EAClD,WAAW,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACjC,YAAY,IAAI,gBAAgB,MAAS;AAAA,EACzC,WAAW,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,SAAS,KAAK,WAAW,CAAC,CAAC;AAAA,EAClC;AAAA,EACA,IAAI,SAAS,UAAU;AACrB,SAAK,UAAU,KAAK,QAAQ;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,KAAK,cAAc,eAAe,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3D,iBAAiB,KAAK,cAAc,eAAe,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpE,WAAW,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvD,kBAAkB,KAAK,cAAc,eAAe,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtE,gBAAgB,KAAK,cAAc,eAAe,gBAAgB;AAAA;AAAA,EAElE,wBAAwB,IAAI,aAAa;AAAA,EACzC,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,KAAK,WAAW,YAAY;AAC9B,WAAK,gBAAgB,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,aAAW;AACxD,YAAI,OAAO,KAAK,YAAY;AAC1B,eAAK,YAAY,OAAO,KAAK,YAAY,OAAO;AAAA,QAClD,OAAO;AACL,eAAK,QAAQ,kBAAkB,MAAM;AACnC,mBAAO,KAAK,cAAc,MAAM,EAAE,KAAK,SAAO;AAC5C,mBAAK,YAAY,IAAI,YAAY,OAAO;AAAA,YAC1C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,uBAAuB,SAAS;AAI1C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,aAAa,IAAI,sBAAsB,OAAO;AACnD,WAAK,cAAc,UAAU,KAAK,UAAU;AAC5C,WAAK,sBAAsB,KAAK,KAAK,UAAU;AAC/C,WAAK,wBAAwB;AAC7B,WAAK,yBAAyB;AAAA,IAChC,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,QAAQ;AAC3B,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAGvB,QAAI,KAAK,YAAY;AACnB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACN,SAAK,mBAAmB;AACxB,SAAK,WAAW,MAAM;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,SAAK,mBAAmB;AACxB,WAAO,KAAK,WAAW,WAAW,KAAK;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,SAAK,mBAAmB;AACxB,WAAO,KAAK,WAAW,YAAY,KAAK;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,WAAW,UAAU;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B,uBAAuB,SAAS;AACxD,SAAK,KAAK;AAAA,MACR,WAAW,MAAM;AAAA,IACnB,GAAG,QAAW,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,QAAQ,aAAa,SAAS;AACjC,SAAK,mBAAmB;AACxB,SAAK,OAAO,cAAc,eAAe,cAAc,UAAU,CAAC,OAAO,WAAW;AAClF,YAAM,IAAI,MAAM,uGAA4G;AAAA,IAC9H;AACA,UAAM,eAAe,SAAS,OAAO,UAAU,IAAI;AAKnD,QAAI,KAAK,WAAW,IAAI,QAAQ,MAAM,gBAAgB,CAAC,cAAc;AAGnE,WAAK,YAAY,cAAc,MAAM,UAAU,UAAU,SAAS;AAClE,UAAI,SAAS;AACX,aAAK,WAAW,WAAW,OAAO;AAAA,MACpC;AACA,WAAK,WAAW,KAAK;AAAA,QACnB,KAAK,KAAK,WAAW;AAAA,QACrB,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,WAAO,cAAc,CAAC,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,SAAS,QAAQ,MAAM;AACtF,YAAM,kBAAkB,iCACnB,UADmB;AAAA,QAEtB,UAAU,YAAY,QAAQ;AAAA,QAC9B,SAAS,KAAK,YAAY;AAAA,MAC5B;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,0BAA0B;AACxB,SAAK,SAAS,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,aAAW;AAChE,WAAK,mBAAmB;AACxB,WAAK,WAAW,WAAW,OAAO;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EACA,2BAA2B;AACzB,SAAK,UAAU,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,cAAY;AAClE,UAAI,UAAU;AACZ,aAAK,mBAAmB;AACxB,aAAK,WAAW,YAAY,QAAQ;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,YAAY;AACpB,cAAM,MAAM,0JAAoK;AAAA,MAClL;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,IAC/B,WAAW,CAAC,GAAG,WAAW,MAAM;AAAA,IAChC,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,uBAAuB;AAAA,IACzB;AAAA,IACA,UAAU,CAAC,eAAe;AAAA,EAC5B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,OAAO,OAAO,SAAS;AAAA,EACvB,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA,EAClD,WAAW,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACjC,OAAO,IAAI,gBAAgB,EAAE;AAAA,EAC7B,aAAa,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,SAAS,KAAK,WAAW,CAAC,CAAC;AAAA,EAClC;AAAA,EACA,IAAI,IAAI,KAAK;AACX,SAAK,KAAK,KAAK,GAAG;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,KAAK,cAAc,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpD,yBAAyB,KAAK,cAAc,eAAe,yBAAyB;AAAA;AAAA;AAAA;AAAA,EAIpF,gBAAgB,KAAK,cAAc,eAAe,gBAAgB;AAAA;AAAA,EAElE,sBAAsB,IAAI,aAAa;AAAA,EACvC,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,KAAK,KAAK,YAAY;AACxB,WAAK,gBAAgB,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,aAAW;AACxD,YAAI,OAAO,KAAK,YAAY,KAAK,KAAK,WAAW;AAC/C,eAAK,YAAY,KAAK,KAAK,WAAW,OAAO,KAAK,UAAU,OAAO;AAAA,QACrE,OAAO;AACL,eAAK,QAAQ,kBAAkB,MAAM;AACnC,oBAAQ,IAAI,CAAC,KAAK,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AAC7F,mBAAK,YAAYA,MAAK,IAAI,UAAU,OAAO;AAAA,YAC7C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,kBAAkB,SAAS;AAI1C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,WAAW,IAAI,iBAAiB,OAAO;AAC5C,WAAK,mBAAmB;AACxB,WAAK,SAAS,OAAOA,IAAG;AACxB,WAAK,cAAc,UAAU,KAAK,QAAQ;AAC1C,WAAK,oBAAoB,KAAK,KAAK,QAAQ;AAC3C,WAAK,wBAAwB;AAC7B,WAAK,oBAAoB;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,QAAQ;AAC3B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,UAAU,OAAO,IAAI;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,SAAK,mBAAmB;AACxB,WAAO,KAAK,SAAS,mBAAmB;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,SAAK,mBAAmB;AACxB,WAAO,KAAK,SAAS,YAAY;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,SAAS,UAAU;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,SAAK,mBAAmB;AACxB,WAAO,KAAK,SAAS,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,SAAS,UAAU;AAAA,EACjC;AAAA,EACA,kBAAkB;AAChB,WAAO,cAAc,CAAC,KAAK,UAAU,KAAK,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,MAAM;AAC5E,YAAM,kBAAkB,iCACnB,UADmB;AAAA,QAEtB,KAAK,OAAO,QAAQ;AAAA,MACtB;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,0BAA0B;AACxB,SAAK,SAAS,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,aAAW;AAClE,UAAI,KAAK,UAAU;AACjB,aAAK,mBAAmB;AACxB,aAAK,SAAS,WAAW,OAAO;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,SAAK,KAAK,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,SAAO;AAC1D,UAAI,OAAO,KAAK,UAAU;AACxB,aAAK,mBAAmB;AACxB,aAAK,SAAS,OAAO,GAAG;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,UAAU;AAClB,cAAM,MAAM,oJAAyJ;AAAA,MACvK;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,KAAK;AAAA,IACP;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,eAAe;AAAA,MACf,qBAAqB;AAAA,IACvB;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,aAAa,IAAI,eAAe,YAAY;AAMlD,IAAM,2BAA2B;AAAA,EAC/B,UAAU;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACF;AAMA,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,aAAa,OAAO,SAAS;AAAA,EAC7B,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlD,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS,UAAU;AACrB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU,WAAW;AACvB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAK,MAAM;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,KAAK,cAAc,eAAe,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxE,WAAW,KAAK,cAAc,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpD,mBAAmB,KAAK,cAAc,eAAe,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxE,gBAAgB,KAAK,cAAc,eAAe,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,cAAc,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1D,UAAU,KAAK,cAAc,eAAe,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlD,aAAa,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxD,mBAAmB,KAAK,cAAc,eAAe,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxE,eAAe,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5D,cAAc,KAAK,cAAc,eAAe,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,cAAc,KAAK,cAAc,eAAe,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,eAAe,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5D,cAAc,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1D,eAAe,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5D,aAAa,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxD,kBAAkB,KAAK,cAAc,eAAe,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtE,gBAAgB,KAAK,cAAc,eAAe,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,eAAe,KAAK,cAAc,eAAe,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhE,eAAe,KAAK,cAAc,eAAe,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhE,iBAAiB,KAAK,cAAc,eAAe,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpE,gBAAgB,KAAK,cAAc,eAAe,gBAAgB;AAAA;AAAA,EAElE,oBAAoB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrC;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,CAAC,KAAK,WAAW,YAAY;AAC/B;AAAA,IACF;AACA,QAAI,OAAO,KAAK,UAAU,KAAK,WAAW,WAAW;AACnD,WAAK,YAAY,KAAK,WAAW,WAAW,OAAO,KAAK,MAAM;AAAA,IAChE,OAAO;AACL,WAAK,QAAQ,kBAAkB,MAAM;AACnC,gBAAQ,IAAI,CAAC,KAAK,WAAW,YAAY,GAAG,OAAO,KAAK,cAAc,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AACrG,eAAK,YAAYA,MAAK,IAAI,MAAM;AAAA,QAClC,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,mBAAmB;AAIlC,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,SAAS,IAAI,kBAAkB,KAAK,gBAAgB,CAAC;AAC1D,WAAK,mBAAmB;AACxB,WAAK,OAAO,OAAOA,IAAG;AACtB,WAAK,cAAc,UAAU,KAAK,MAAM;AACxC,WAAK,kBAAkB,KAAK,KAAK,MAAM;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ;AACV,UAAI,QAAQ,SAAS,GAAG;AACtB,eAAO,WAAW,KAAK,gBAAgB,CAAC;AAAA,MAC1C;AACA,UAAI,QAAQ,OAAO,KAAK,WAAW,QAAW;AAC5C,eAAO,SAAS,MAAM;AAAA,MACxB;AACA,UAAI,QAAQ,UAAU,KAAK,WAAW;AACpC,eAAO,YAAY,SAAS;AAAA,MAC9B;AACA,UAAI,QAAQ,OAAO,KAAK,WAAW,QAAW;AAC5C,eAAO,SAAS,MAAM;AAAA,MACxB;AACA,UAAI,QAAQ,WAAW,KAAK,eAAe,QAAW;AACpD,eAAO,aAAa,UAAU;AAAA,MAChC;AACA,UAAI,QAAQ,MAAM,KAAK,OAAO;AAC5B,eAAO,QAAQ,KAAK;AAAA,MACtB;AACA,UAAI,QAAQ,SAAS,KAAK,aAAa,QAAW;AAChD,eAAO,WAAW,QAAQ;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,kBAAkB,SAAS;AAChC,SAAK,cAAc,QAAQ;AAC3B,SAAK,QAAQ,OAAO,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,aAAa,KAAK;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,UAAU,KAAK;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,SAAK,mBAAmB;AACxB,WAAO,CAAC,CAAC,KAAK,OAAO,aAAa;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,QAAQ,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,SAAS,KAAK;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,WAAW,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,YAAY,KAAK;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,SAAS,KAAK;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,SAAS,KAAK;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,WAAW;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,OAAO,UAAU,KAAK;AAAA,EACpC;AAAA;AAAA,EAEA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK,SAAS,QAAQ,QAAQ,KAAK,MAAM,IAAI,KAAK,kBAAkB,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU;AAAA,EACrG;AAAA;AAAA,EAEA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,WAAO,iCACF,UADE;AAAA,MAEL,OAAO,KAAK,UAAU,QAAQ;AAAA,MAC9B,UAAU,KAAK,aAAa,QAAQ;AAAA,MACpC,OAAO,KAAK,UAAU,QAAQ;AAAA,MAC9B,WAAW,KAAK,cAAc,QAAQ;AAAA,MACtC,KAAK,KAAK,WAAW;AAAA,MACrB,MAAM,KAAK,SAAS,QAAQ;AAAA,MAC5B,SAAS,KAAK,YAAY,QAAQ;AAAA,IACpC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,QAAQ;AAChB,cAAM,MAAM,gJAAqJ;AAAA,MACnK;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,MACX,SAAS;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,aAAa;AAAA,MACb,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,oBAAoB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,4BAA4B,CAAC;AAUnC,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,aAAa,OAAO,SAAS;AAAA,EAC7B,UAAU,OAAO,MAAM;AAAA,EACvB,kBAAkB,oBAAI,IAAI;AAAA,EAC1B,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA,EAClD,WAAW,IAAI,QAAQ;AAAA;AAAA,EAEvB,iBAAiB,KAAK,WAAW;AAAA,EACjC,cAAc,MAAM;AAAA,EACpB,IAAI,cAAc,eAAe;AAC/B,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,YAAY,aAAa;AAC3B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA;AAAA,EACA,IAAI,WAAW,YAAY;AACzB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA;AAAA,EACA,IAAI,aAAa,cAAc;AAC7B,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,IAAI,kBAAkB,mBAAmB;AACvC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA,IAAI,SAAS,UAAU;AACrB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA;AAAA,EACA,IAAI,aAAa,cAAc;AAC7B,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,IAAI,eAAe,gBAAgB;AACjC,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA;AAAA,EACA,IAAI,UAAU,WAAW;AACvB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA;AAAA,EACA,IAAI,WAAW,YAAY;AACzB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA,IAAI,mBAAmB,oBAAoB;AACzC,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA,IAAI,YAAY,aAAa;AAC3B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,KAAK,cAAc,eAAe,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrE,gBAAgB,KAAK,cAAc,eAAe,eAAe;AAAA;AAAA,EAEjE,eAAe,KAAK,cAAc,eAAe,OAAO;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA,EAEA,6BAA6B,IAAI,aAAa;AAAA,EAC9C,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,KAAK,gBAAgB;AACvB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,WAAW,YAAY,EAAE,KAAK,CAAAA,SAAO;AACxC,cAAI,OAAO,oBAAoB,eAAe,OAAO,cAAc,eAAe,YAAY;AAC5F,kBAAM,MAAM,0KAAoL;AAAA,UAClM;AAIA,eAAK,kBAAkB,KAAK,QAAQ,kBAAkB,MAAM;AAC1D,mBAAO,IAAI,gBAAgBA,MAAK,CAAC,GAAG,KAAK,gBAAgB,CAAC;AAAA,UAC5D,CAAC;AACD,eAAK,mBAAmB;AACxB,eAAK,cAAc,UAAU,KAAK,eAAe;AACjD,eAAK,2BAA2B,KAAK,KAAK,eAAe;AAAA,QAC3D,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,gBAAgB;AACvB,UAAI,KAAK,iBAAiB;AACxB,aAAK,uBAAuB;AAAA,MAC9B,OAAO;AACL,aAAK,2BAA2B,KAAK,KAAK,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,uBAAuB,CAAC;AAAA,MACvH;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW;AACb,UAAI,QAAQ,SAAS,GAAG;AACtB,kBAAU,WAAW,KAAK,gBAAgB,CAAC;AAAA,MAC7C;AACA,UAAI,QAAQ,aAAa,GAAG;AAC1B,kBAAU,cAAc;AAAA,MAC1B;AACA,UAAI,QAAQ,eAAe,KAAK,mBAAmB,QAAW;AAC5D,kBAAU,iBAAiB,cAAc;AAAA,MAC3C;AACA,UAAI,QAAQ,aAAa,KAAK,iBAAiB,QAAW;AACxD,kBAAU,eAAe,YAAY;AAAA,MACvC;AACA,UAAI,QAAQ,YAAY,KAAK,CAAC,CAAC,aAAa;AAC1C,kBAAU,cAAc,WAAW;AAAA,MACrC;AACA,UAAI,QAAQ,cAAc,KAAK,kBAAkB,QAAW;AAC1D,kBAAU,gBAAgB,aAAa;AAAA,MACzC;AACA,UAAI,QAAQ,mBAAmB,KAAK,uBAAuB,QAAW;AACpE,kBAAU,qBAAqB,kBAAkB;AAAA,MACnD;AACA,UAAI,QAAQ,UAAU,KAAK,cAAc,QAAW;AAClD,kBAAU,YAAY,SAAS;AAAA,MACjC;AACA,UAAI,QAAQ,cAAc,KAAK,kBAAkB,QAAW;AAC1D,kBAAU,gBAAgB,aAAa;AAAA,MACzC;AACA,UAAI,QAAQ,gBAAgB,KAAK,oBAAoB,QAAW;AAC9D,kBAAU,kBAAkB,eAAe;AAAA,MAC7C;AACA,UAAI,QAAQ,WAAW,KAAK,eAAe,QAAW;AACpD,kBAAU,aAAa,UAAU;AAAA,MACnC;AACA,UAAI,QAAQ,YAAY,KAAK,aAAa;AACxC,kBAAU,cAAc,WAAW;AAAA,MACrC;AACA,UAAI,QAAQ,SAAS,KAAK,aAAa,QAAW;AAChD,kBAAU,WAAW,QAAQ;AAAA,MAC/B;AACA,UAAI,QAAQ,oBAAoB,KAAK,wBAAwB,QAAW;AACtE,kBAAU,sBAAsB,mBAAmB;AAAA,MACrD;AACA,UAAI,QAAQ,QAAQ,KAAK,SAAS;AAChC,kBAAU,UAAU,OAAO;AAAA,MAC7B;AACA,UAAI,QAAQ,OAAO,KAAK,WAAW,QAAW;AAC5C,kBAAU,SAAS,MAAM;AAAA,MAC3B;AACA,UAAI,QAAQ,QAAQ,KAAK,YAAY,QAAW;AAC9C,kBAAU,UAAU,OAAO;AAAA,MAC7B;AACA,UAAI,QAAQ,aAAa,KAAK,iBAAiB,QAAW;AACxD,kBAAU,eAAe,YAAY;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AACvB,SAAK,cAAc,QAAQ;AAC3B,SAAK,iBAAiB,OAAO,IAAI;AAAA,EACnC;AAAA,EACA,gBAAgB,SAAS;AACvB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB,gBAAgB,OAAO;AAAA,EAC9C;AAAA,EACA,mBAAmB;AACjB,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,iBAAiB;AAAA,EAC/C;AAAA,EACA,iBAAiB;AACf,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,eAAe;AAAA,EAC7C;AAAA,EACA,gBAAgB;AACd,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,cAAc;AAAA,EAC5C;AAAA,EACA,kBAAkB;AAChB,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,gBAAgB;AAAA,EAC9C;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,YAAY;AAAA,EAC1C;AAAA,EACA,uBAAuB;AACrB,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,qBAAqB;AAAA,EACnD;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,YAAY;AAAA,EAC1C;AAAA,EACA,kBAAkB;AAChB,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,gBAAgB;AAAA,EAC9C;AAAA,EACA,oBAAoB;AAClB,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,kBAAkB;AAAA,EAChD;AAAA,EACA,eAAe;AACb,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,aAAa;AAAA,EAC3C;AAAA,EACA,gBAAgB;AACd,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,cAAc;AAAA,EAC5C;AAAA,EACA,aAAa;AACX,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,WAAW;AAAA,EACzC;AAAA,EACA,wBAAwB;AACtB,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,sBAAsB;AAAA,EACpD;AAAA,EACA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,UAAU;AAAA,EACxC;AAAA,EACA,WAAW;AACT,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,SAAS;AAAA,EACvC;AAAA,EACA,mBAAmB;AACjB,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,iBAAiB;AAAA,EAC/C;AAAA,EACA,kBAAkB;AAChB,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,gBAAgB;AAAA,EAC9C;AAAA,EACA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,UAAU;AAAA,EACxC;AAAA,EACA,iBAAiB;AACf,SAAK,mBAAmB;AACxB,WAAO,KAAK,gBAAgB,eAAe;AAAA,EAC7C;AAAA,EACA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,WAAO,iCACF,UADE;AAAA,MAEL,aAAa,KAAK,eAAe,QAAQ;AAAA,MACzC,eAAe,KAAK,kBAAkB,QAAQ;AAAA,MAC9C,WAAW,KAAK,aAAa,QAAQ;AAAA,MACrC,aAAa,KAAK,gBAAgB,QAAQ;AAAA,MAC1C,YAAY,KAAK,eAAe,QAAQ;AAAA,MACxC,cAAc,KAAK,iBAAiB,QAAQ;AAAA,MAC5C,mBAAmB,KAAK,sBAAsB,QAAQ;AAAA,MACtD,UAAU,KAAK,aAAa,QAAQ;AAAA,MACpC,cAAc,KAAK,iBAAiB,QAAQ;AAAA,MAC5C,gBAAgB,KAAK,mBAAmB,QAAQ;AAAA,MAChD,WAAW,KAAK,cAAc,QAAQ;AAAA,MACtC,YAAY,KAAK,eAAe,QAAQ;AAAA,MACxC,SAAS,KAAK,YAAY,QAAQ;AAAA,MAClC,oBAAoB,KAAK,uBAAuB,QAAQ;AAAA,MACxD,QAAQ,KAAK,WAAW,QAAQ;AAAA,MAChC,OAAO,KAAK,UAAU,QAAQ;AAAA,MAC9B,QAAQ,KAAK,WAAW,QAAQ;AAAA,MAChC,aAAa,KAAK,gBAAgB,QAAQ;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,SAAK,mBAAmB;AACxB,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,oBAAoB,KAAK,QAAQ,EAAE,KAAK,aAAW;AACtD,cAAM,iBAAiB,CAAC;AACxB,mBAAW,UAAU,SAAS;AAC5B,eAAK,gBAAgB,IAAI,MAAM;AAC/B,yBAAe,KAAK,MAAM;AAAA,QAC5B;AACA,aAAK,gBAAgB,WAAW,cAAc;AAAA,MAChD,CAAC;AAAA,IACH,CAAC;AACD,SAAK,SAAS,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,sBAAoB;AACjF,WAAK,mBAAmB;AACxB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,oBAAoB,gBAAgB,EAAE,KAAK,aAAW;AACzD,gBAAM,aAAa,IAAI,IAAI,OAAO;AAClC,gBAAM,eAAe,CAAC;AACtB,gBAAM,kBAAkB,CAAC;AACzB,qBAAW,UAAU,MAAM,KAAK,UAAU,GAAG;AAC3C,gBAAI,CAAC,KAAK,gBAAgB,IAAI,MAAM,GAAG;AACrC,mBAAK,gBAAgB,IAAI,MAAM;AAC/B,2BAAa,KAAK,MAAM;AAAA,YAC1B;AAAA,UACF;AACA,qBAAW,UAAU,MAAM,KAAK,KAAK,eAAe,GAAG;AACrD,gBAAI,CAAC,WAAW,IAAI,MAAM,GAAG;AAC3B,8BAAgB,KAAK,MAAM;AAAA,YAC7B;AAAA,UACF;AACA,eAAK,gBAAgB,WAAW,cAAc,IAAI;AAClD,eAAK,gBAAgB,cAAc,iBAAiB,IAAI;AACxD,eAAK,gBAAgB,QAAQ;AAC7B,qBAAW,UAAU,iBAAiB;AACpC,iBAAK,gBAAgB,OAAO,MAAM;AAAA,UACpC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB,SAAS;AAC3B,WAAO,QAAQ,IAAI,QAAQ,IAAI,qBAAmB,gBAAgB,eAAe,CAAC,CAAC;AAAA,EACrF;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,iBAAiB;AACzB,cAAM,MAAM,uJAA4J;AAAA,MAC1K;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAA8B;AAAA,EACjE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iCAAiC,CAAC;AAAA,IAC/C,gBAAgB,SAAS,4CAA4C,IAAI,KAAK,UAAU;AACtF,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,WAAW,CAAC;AAAA,MAC1C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,UAAU;AAAA,MACV,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,oBAAoB;AAAA,MACpB,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,4BAA4B;AAAA,IAC9B;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,IAC/B,UAAU,CAAI,oBAAoB;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,OAAO,SAAS;AAAA,EACvB,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA,EAClD,WAAW,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACjC,SAAS,IAAI,gBAAgB,MAAS;AAAA,EACtC,aAAa,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,SAAS,KAAK,WAAW,CAAC,CAAC;AAAA,EAClC;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,KAAK,cAAc,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA,EAIxD,kBAAkB,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA,EAI9D,cAAc,KAAK,cAAc,eAAe,MAAM;AAAA;AAAA;AAAA;AAAA,EAItD,iBAAiB,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA,EAI5D,mBAAmB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA,EAIhE,mBAAmB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA,EAIhE,mBAAmB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA,EAIhE,kBAAkB,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA,EAI9D,mBAAmB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA,EAIhE,iBAAiB,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA,EAI5D,oBAAoB,KAAK,cAAc,eAAe,YAAY;AAAA;AAAA,EAElE,qBAAqB,IAAI,aAAa;AAAA,EACtC,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,KAAK,KAAK,YAAY;AACxB,WAAK,gBAAgB,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,aAAW;AACxD,YAAI,OAAO,KAAK,WAAW,KAAK,KAAK,WAAW;AAC9C,eAAK,YAAY,KAAK,KAAK,WAAW,OAAO,KAAK,SAAS,OAAO;AAAA,QACpE,OAAO;AACL,eAAK,QAAQ,kBAAkB,MAAM;AACnC,oBAAQ,IAAI,CAAC,KAAK,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AAC7F,mBAAK,YAAYA,MAAK,IAAI,SAAS,OAAO;AAAA,YAC5C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,oBAAoB,SAAS;AAI5C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,UAAU,IAAI,mBAAmB,OAAO;AAC7C,WAAK,mBAAmB;AACxB,WAAK,QAAQ,OAAOA,IAAG;AACvB,WAAK,cAAc,UAAU,KAAK,OAAO;AACzC,WAAK,mBAAmB,KAAK,KAAK,OAAO;AACzC,WAAK,wBAAwB;AAC7B,WAAK,qBAAqB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,QAAQ;AAC3B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,SAAS,OAAO,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,SAAK,mBAAmB;AACxB,WAAO,KAAK,QAAQ,aAAa;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,SAAK,mBAAmB;AACxB,WAAO,KAAK,QAAQ,YAAY;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,SAAK,mBAAmB;AACxB,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,SAAK,mBAAmB;AACxB,WAAO,KAAK,QAAQ,SAAS;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACX,SAAK,mBAAmB;AACxB,WAAO,KAAK,QAAQ,WAAW;AAAA,EACjC;AAAA,EACA,kBAAkB;AAChB,WAAO,cAAc,CAAC,KAAK,UAAU,KAAK,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,SAAS,KAAK,MAAM;AAChF,YAAM,kBAAkB,iCACnB,UADmB;AAAA,QAEtB,OAAO,SAAS,QAAQ;AAAA,MAC1B;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,0BAA0B;AACxB,SAAK,SAAS,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,aAAW;AAClE,WAAK,mBAAmB;AACxB,WAAK,QAAQ,WAAW,OAAO;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,SAAK,OAAO,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS;AAC9D,UAAI,OAAO;AACT,aAAK,mBAAmB;AACxB,aAAK,QAAQ,SAAS,KAAK;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,SAAS;AACjB,cAAM,MAAM,kJAAuJ;AAAA,MACrK;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,IAC3B,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,IACtB;AAAA,IACA,UAAU,CAAC,YAAY;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,OAAO,OAAO,SAAS;AAAA,EACvB,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA,EAClD,WAAW,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACjC,QAAQ,IAAI,gBAAgB,MAAS;AAAA,EACrC,aAAa,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,SAAS,KAAK,WAAW,CAAC,CAAC;AAAA,EAClC;AAAA,EACA,IAAI,KAAK,MAAM;AACb,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,KAAK,cAAc,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA,EAIzD,mBAAmB,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA,EAI/D,eAAe,KAAK,cAAc,eAAe,MAAM;AAAA;AAAA;AAAA;AAAA,EAIvD,kBAAkB,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA,EAI7D,oBAAoB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA,EAIjE,oBAAoB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA,EAIjE,oBAAoB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA,EAIjE,mBAAmB,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA,EAI/D,oBAAoB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA,EAIjE,kBAAkB,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA,EAI7D,qBAAqB,KAAK,cAAc,eAAe,YAAY;AAAA;AAAA,EAEnE,sBAAsB,IAAI,aAAa;AAAA,EACvC,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,KAAK,KAAK,YAAY;AACxB,WAAK,gBAAgB,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,aAAW;AACxD,YAAI,OAAO,KAAK,YAAY,KAAK,KAAK,WAAW;AAC/C,eAAK,YAAY,KAAK,KAAK,WAAW,OAAO,KAAK,UAAU,OAAO;AAAA,QACrE,OAAO;AACL,eAAK,QAAQ,kBAAkB,MAAM;AACnC,oBAAQ,IAAI,CAAC,KAAK,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AAC7F,mBAAK,YAAYA,MAAK,IAAI,UAAU,OAAO;AAAA,YAC7C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,qBAAqB,SAAS;AAI7C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,WAAW,IAAI,oBAAoB,OAAO;AAC/C,WAAK,mBAAmB;AACxB,WAAK,SAAS,OAAOA,IAAG;AACxB,WAAK,cAAc,UAAU,KAAK,QAAQ;AAC1C,WAAK,oBAAoB,KAAK,KAAK,QAAQ;AAC3C,WAAK,wBAAwB;AAC7B,WAAK,qBAAqB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,QAAQ;AAC3B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,UAAU,OAAO,IAAI;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,SAAK,mBAAmB;AACxB,WAAO,KAAK,SAAS,aAAa;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,SAAK,mBAAmB;AACxB,WAAO,KAAK,SAAS,YAAY;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,SAAK,mBAAmB;AACxB,WAAO,KAAK,SAAS,QAAQ;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACX,SAAK,mBAAmB;AACxB,WAAO,KAAK,SAAS,WAAW;AAAA,EAClC;AAAA,EACA,kBAAkB;AAChB,WAAO,cAAc,CAAC,KAAK,UAAU,KAAK,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,SAAS,IAAI,MAAM;AAC9E,YAAM,kBAAkB,iCACnB,UADmB;AAAA,QAEtB,MAAM,QAAQ,QAAQ;AAAA,MACxB;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,0BAA0B;AACxB,SAAK,SAAS,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,aAAW;AAClE,WAAK,mBAAmB;AACxB,WAAK,SAAS,WAAW,OAAO;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,SAAK,MAAM,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,UAAQ;AAC5D,UAAI,MAAM;AACR,aAAK,mBAAmB;AACxB,aAAK,SAAS,QAAQ,IAAI;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,UAAU;AAClB,cAAM,MAAM,oJAAyJ;AAAA,MACvK;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,IACvB;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS;AAAA,EACvB,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA,EAClD,WAAW,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACjC,UAAU,IAAI,gBAAgB,MAAS;AAAA,EACvC,aAAa,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,SAAS,KAAK,WAAW,CAAC,CAAC;AAAA,EAClC;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,QAAQ,KAAK,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,KAAK,cAAc,eAAe,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,iBAAiB,KAAK,cAAc,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1D,oBAAoB,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhE,gBAAgB,KAAK,cAAc,eAAe,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxD,mBAAmB,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,qBAAqB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,qBAAqB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,qBAAqB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,oBAAoB,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhE,qBAAqB,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,mBAAmB,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,sBAAsB,KAAK,cAAc,eAAe,YAAY;AAAA;AAAA,EAEpE,uBAAuB,IAAI,aAAa;AAAA,EACxC,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,KAAK,KAAK,YAAY;AACxB,WAAK,gBAAgB,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,aAAW;AACxD,YAAI,OAAO,KAAK,aAAa,KAAK,KAAK,WAAW;AAChD,eAAK,YAAY,KAAK,KAAK,WAAW,OAAO,KAAK,WAAW,OAAO;AAAA,QACtE,OAAO;AACL,eAAK,QAAQ,kBAAkB,MAAM;AACnC,oBAAQ,IAAI,CAAC,KAAK,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AAC7F,mBAAK,YAAYA,MAAK,IAAI,WAAW,OAAO;AAAA,YAC9C,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,sBAAsB,SAAS;AAI9C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,YAAY,IAAI,qBAAqB,OAAO;AACjD,WAAK,mBAAmB;AACxB,WAAK,UAAU,OAAOA,IAAG;AACzB,WAAK,cAAc,UAAU,KAAK,SAAS;AAC3C,WAAK,qBAAqB,KAAK,KAAK,SAAS;AAC7C,WAAK,wBAAwB;AAC7B,WAAK,uBAAuB;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,QAAQ;AAC3B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,WAAW,OAAO,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,UAAU;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,aAAa;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,YAAY;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,SAAK,mBAAmB;AACxB,WAAO,KAAK,UAAU,WAAW;AAAA,EACnC;AAAA,EACA,kBAAkB;AAChB,WAAO,cAAc,CAAC,KAAK,UAAU,KAAK,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,SAAS,MAAM,MAAM;AAClF,YAAM,kBAAkB,iCACnB,UADmB;AAAA,QAEtB,QAAQ,UAAU,QAAQ;AAAA,MAC5B;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,0BAA0B;AACxB,SAAK,SAAS,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,aAAW;AAClE,WAAK,mBAAmB;AACxB,WAAK,UAAU,WAAW,OAAO;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,yBAAyB;AACvB,SAAK,QAAQ,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AAChE,UAAI,QAAQ;AACV,aAAK,mBAAmB;AACxB,aAAK,UAAU,UAAU,MAAM;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,WAAW;AACnB,cAAM,MAAM,sJAA2J;AAAA,MACzK;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,MACP,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,IACxB;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS;AAAA,EACvB,UAAU,OAAO,MAAM;AAAA,EACvB,eAAe,IAAI,gBAAgB,IAAI;AAAA,EACvC,aAAa,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY,aAAa;AAC3B,SAAK,aAAa,KAAK,WAAW;AAAA,EACpC;AAAA;AAAA,EAEA,0BAA0B,IAAI,aAAa;AAAA,EAC3C,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,KAAK,KAAK,YAAY;AACxB,WAAK,gBAAgB,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,aAAW;AACxD,YAAI,OAAO,KAAK,gBAAgB,KAAK,KAAK,WAAW;AACnD,eAAK,YAAY,KAAK,KAAK,WAAW,OAAO,KAAK,cAAc,OAAO;AAAA,QACzE,OAAO;AACL,eAAK,QAAQ,kBAAkB,MAAM;AACnC,oBAAQ,IAAI,CAAC,KAAK,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AAC7F,mBAAK,YAAYA,MAAK,IAAI,cAAc,OAAO;AAAA,YACjD,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,kBAAkB,SAAS;AAC1C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,eAAe,IAAI,iBAAiB,OAAO;AAChD,WAAK,mBAAmB;AACxB,WAAK,aAAa,OAAOA,IAAG;AAC5B,WAAK,wBAAwB,KAAK,KAAK,YAAY;AACnD,WAAK,4BAA4B;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,cAAc,OAAO,IAAI;AAAA,EAChC;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,aAAa,KAAK,IAAI,iBAAe;AAC/C,YAAM,kBAAkB;AAAA,QACtB;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,8BAA8B;AAC5B,SAAK,gBAAgB,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,aAAW;AAC3E,WAAK,mBAAmB;AACxB,WAAK,aAAa,WAAW,OAAO;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,cAAc;AACtB,YAAM,MAAM,8JAAmK;AAAA,IACjL;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,QAAQ;AAAA,MACN,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,yBAAyB;AAAA,IAC3B;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS;AAAA,EACvB,QAAQ,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB;AAAA;AAAA,EAEA,0BAA0B,IAAI,aAAa;AAAA,EAC3C,WAAW;AACT,QAAI,KAAK,KAAK,YAAY;AACxB,UAAI,OAAO,KAAK,gBAAgB,KAAK,KAAK,WAAW;AACnD,aAAK,YAAY,KAAK,KAAK,WAAW,OAAO,KAAK,YAAY;AAAA,MAChE,OAAO;AACL,aAAK,MAAM,kBAAkB,MAAM;AACjC,kBAAQ,IAAI,CAAC,KAAK,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AAC7F,iBAAK,YAAYA,MAAK,IAAI,YAAY;AAAA,UACxC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,kBAAkB;AACjC,SAAK,MAAM,kBAAkB,MAAM;AACjC,WAAK,eAAe,IAAI,iBAAiB;AACzC,WAAK,wBAAwB,KAAK,KAAK,YAAY;AACnD,WAAK,wBAAwB;AAC7B,WAAK,aAAa,OAAOA,IAAG;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,OAAO,IAAI;AAAA,EAChC;AAAA,EACA,0BAA0B;AACxB,QAAI,CAAC,KAAK,cAAc;AACtB,YAAM,MAAM,8JAAmK;AAAA,IACjL;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,SAAS;AAAA,MACP,yBAAyB;AAAA,IAC3B;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,aAAa,OAAO,SAAS;AAAA,EAC7B,UAAU,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,IAAI,KAAK,MAAM;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA,EAEA,qBAAqB,IAAI,aAAa;AAAA,EACtC,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,KAAK,WAAW,YAAY;AAC9B,UAAI,CAAC,OAAO,QAAQ,MAAM,iBAAiB,CAAC,OAAO,QAAQ,KAAK,kBAAkB,OAAO,cAAc,eAAe,YAAY;AAChI,cAAM,MAAM,sOAAgP;AAAA,MAC9P;AACA,UAAI,OAAO,KAAK,eAAe,gBAAgB,KAAK,WAAW,WAAW;AACxE,aAAK,YAAY,KAAK,WAAW,WAAW,OAAO,KAAK,cAAc,YAAY;AAAA,MACpF,OAAO;AACL,aAAK,QAAQ,kBAAkB,MAAM;AACnC,kBAAQ,IAAI,CAAC,KAAK,WAAW,YAAY,GAAG,OAAO,KAAK,cAAc,eAAe,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AAC5G,iBAAK,YAAYA,MAAK,IAAI,YAAY;AAAA,UACxC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,oBAAoB;AAInC,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,UAAU,IAAI,mBAAmB,KAAK,gBAAgB,CAAC;AAC5D,WAAK,mBAAmB;AACxB,WAAK,QAAQ,OAAOA,IAAG;AACvB,WAAK,mBAAmB,KAAK,KAAK,OAAO;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,UAAI,QAAQ,SAAS,GAAG;AACtB,gBAAQ,WAAW,KAAK,gBAAgB,CAAC;AAAA,MAC3C;AACA,UAAI,QAAQ,MAAM,KAAK,UAAU,QAAW;AAC1C,gBAAQ,QAAQ,KAAK,eAAe,KAAK,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,OAAO,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,mBAAmB;AACxB,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAC9B;AAAA;AAAA,EAEA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY,CAAC;AAClC,WAAO,iCACF,UADE;AAAA,MAEL,MAAM,KAAK,eAAe,KAAK,SAAS,QAAQ,QAAQ,CAAC,CAAC;AAAA,MAC1D,KAAK,KAAK,WAAW;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,MAAM;AACnB,UAAM,SAAS,CAAC;AAChB,SAAK,QAAQ,UAAQ;AACnB,aAAO,KAAK,gBAAgB,IAAI,IAAI,IAAI,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK,GAAG,IAAI,IAAI;AAAA,IACvF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,SAAS;AACjB,cAAM,MAAM,uJAA4J;AAAA,MAC1K;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,oBAAoB;AAAA,IACtB;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,IAC5B,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,SAAS,gBAAgB,OAAO;AAC9B,SAAO,SAAS,OAAO,MAAM,QAAQ,YAAY,OAAO,MAAM,QAAQ;AACxE;AAMA,IAAM,yBAAyB;AAAA,EAC7B,UAAU;AAAA,IACR,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACF;AAMA,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,aAAa,OAAO,SAAS;AAAA,EAC7B,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,IAAI,gBAAgB,OAAO,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlD,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS,UAAU;AACrB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa,WAAW;AAC1B,SAAK,aAAa;AAAA,EACpB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO,QAAQ;AACjB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,KAAK,cAAc,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA,EAIpD,cAAc,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA,EAI1D,cAAc,KAAK,cAAc,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA,EAI1D,eAAe,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA;AAAA;AAAA,EAI5D,aAAa,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA,EAIxD,gBAAgB,KAAK,cAAc,eAAe,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,UAAU,KAAK,cAAc,eAAe,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlD,aAAa,KAAK,cAAc,eAAe,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxD,eAAe,KAAK,cAAc,eAAe,WAAW;AAAA;AAAA,EAE5D,oBAAoB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrC;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,QAAI,CAAC,KAAK,WAAW,YAAY;AAC/B;AAAA,IACF;AACA,QAAI,OAAO,KAAK,QAAQ,yBAAyB,KAAK,WAAW,WAAW;AAC1E,WAAK,YAAY,KAAK,WAAW,WAAW,OAAO,KAAK,OAAO,qBAAqB;AAAA,IACtF,OAAO;AACL,WAAK,QAAQ,kBAAkB,MAAM;AACnC,gBAAQ,IAAI,CAAC,KAAK,WAAW,YAAY,GAAG,OAAO,KAAK,cAAc,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAACA,MAAK,GAAG,MAAM;AACrG,eAAK,YAAYA,MAAK,IAAI,qBAAqB;AAAA,QACjD,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAYA,MAAK,2BAA2B;AAI1C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,iBAAiB,IAAI,0BAA0B,KAAK,gBAAgB,CAAC;AAC1E,WAAK,mBAAmB;AACxB,WAAK,eAAe,MAAMA;AAC1B,WAAK,cAAc,UAAU,KAAK,cAAc;AAChD,WAAK,kBAAkB,KAAK,KAAK,cAAc;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,gBAAgB;AAClB,UAAI,QAAQ,OAAO,GAAG;AACpB,uBAAe,QAAQ;AAAA,MACzB;AACA,UAAI,QAAQ,cAAc,GAAG;AAC3B,uBAAe,eAAe;AAAA,MAChC;AACA,UAAI,QAAQ,SAAS,GAAG;AACtB,uBAAe,UAAU;AAAA,MAC3B;AACA,UAAI,QAAQ,UAAU,GAAG;AACvB,uBAAe,WAAW;AAAA,MAC5B;AACA,UAAI,QAAQ,QAAQ,GAAG;AACrB,uBAAe,SAAS;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,kBAAkB,SAAS;AAChC,SAAK,cAAc,QAAQ;AAC3B,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,MAAM;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,YAAY;AACV,SAAK,mBAAmB;AACxB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK,iBAAiB,QAAQ,QAAQ,KAAK,cAAc,IAAI,KAAK,kBAAkB,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU;AAAA,EACrH;AAAA;AAAA,EAEA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,WAAO,iCACF,UADE;AAAA,MAEL,OAAO,KAAK,UAAU,QAAQ;AAAA,MAC9B,UAAU,KAAK,aAAa,QAAQ;AAAA,MACpC,SAAS,KAAK,YAAY,QAAQ;AAAA,MAClC,QAAQ,KAAK,WAAW,QAAQ;AAAA,MAChC,cAAc,KAAK,cAAc,QAAQ;AAAA,MACzC,KAAK,KAAK,WAAW;AAAA,IACvB;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,gBAAgB;AACxB,cAAM,MAAM,gJAAqJ;AAAA,MACnK;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,IACnC,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,MACV,SAAS;AAAA,MACT,cAAc;AAAA,MACd,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,aAAa;AAAA,MACb,aAAa;AAAA,MACb,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAC,mBAAmB;AAAA,IAC9B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,oBAAoB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,aAAa,OAAO,SAAS;AAAA,EAC7B,UAAU,OAAO,MAAM;AAAA,EACvB,kBAAkB,oBAAI,IAAI;AAAA,EAC1B,0BAA0B,IAAI,gBAAgB,KAAK,OAAO;AAAA,EAC1D,uBAAuB,aAAa;AAAA;AAAA,EAEpC,iBAAiB,KAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA,kBAAkB,KAAK,wBAAwB,eAAe,iBAAiB;AAAA;AAAA,EAE/E,gBAAgB,KAAK,wBAAwB,eAAe,eAAe;AAAA;AAAA,EAE3E,eAAe,IAAI,aAAa;AAAA;AAAA,EAEhC,6BAA6B,IAAI,aAAa;AAAA,EAC9C;AAAA;AAAA,EAEA;AAAA,EACM,WAAW;AAAA;AACf,UAAI,KAAK,gBAAgB;AACvB,cAAM,KAAK,eAAe;AAG1B,aAAK,wBAAwB,UAAU,KAAK,WAAW,SAAS;AAAA,MAClE;AAAA,IACF;AAAA;AAAA,EACM,YAAY,SAAS;AAAA;AACzB,YAAM,SAAS,QAAQ,UAAU,KAAK,QAAQ,WAAW;AAEzD,UAAI,KAAK,mBAAmB,UAAU,CAAC,OAAO,cAAc,GAAG;AAC7D,cAAM,KAAK,eAAe;AAAA,MAC5B;AAAA,IACF;AAAA;AAAA,EACA,cAAc;AACZ,SAAK,qBAAqB,YAAY;AACtC,SAAK,wBAAwB,QAAQ;AACrC,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACM,iBAAiB;AAAA;AACrB,UAAI,CAAC,iBAAiB,oBAAoB,OAAO,cAAc,eAAe,YAAY;AACxF,cAAM,MAAM,kKAA4K;AAAA,MAC1L;AACA,YAAMA,OAAM,MAAM,KAAK,WAAW,YAAY;AAC9C,WAAK,gBAAgB;AAIrB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,kBAAkB,IAAI,gBAAgB,gBAAgB;AAAA,UACzD,KAAAA;AAAA,UACA,UAAU,KAAK;AAAA,UACf,WAAW,KAAK;AAAA,UAChB,gBAAgB,CAAC,OAAO,SAASA,SAAQ;AACvC,gBAAI,KAAK,aAAa,UAAU,QAAQ;AACtC,mBAAK,QAAQ,IAAI,MAAM,KAAK,aAAa,KAAK,OAAO,CAAC;AAAA,YACxD,OAAO;AACL,8BAAgB,6BAA6B,OAAO,SAASA,IAAG;AAAA,YAClE;AAAA,UACF;AAAA,QACF,CAAC;AACD,aAAK,2BAA2B,KAAK,KAAK,eAAe;AAAA,MAC3D,CAAC;AACD,YAAM,KAAK,uBAAuB;AAAA,IACpC;AAAA;AAAA,EACM,yBAAyB;AAAA;AAC7B,WAAK,mBAAmB;AACxB,YAAM,iBAAiB,CAAC;AACxB,YAAM,UAAU,MAAM,KAAK,oBAAoB,KAAK,SAAS,QAAQ,CAAC;AACtE,iBAAW,UAAU,SAAS;AAC5B,aAAK,gBAAgB,IAAI,MAAM;AAC/B,uBAAe,KAAK,MAAM;AAAA,MAC5B;AACA,WAAK,gBAAgB,WAAW,cAAc;AAC9C,WAAK,qBAAqB,YAAY;AACtC,WAAK,uBAAuB,KAAK,SAAS,QAAQ,UAAU,CAAM,qBAAoB;AACpF,aAAK,mBAAmB;AACxB,cAAM,aAAa,IAAI,IAAI,MAAM,KAAK,oBAAoB,gBAAgB,CAAC;AAC3E,cAAM,eAAe,CAAC;AACtB,cAAM,kBAAkB,CAAC;AACzB,mBAAW,UAAU,MAAM,KAAK,UAAU,GAAG;AAC3C,cAAI,CAAC,KAAK,gBAAgB,IAAI,MAAM,GAAG;AACrC,iBAAK,gBAAgB,IAAI,MAAM;AAC/B,yBAAa,KAAK,MAAM;AAAA,UAC1B;AAAA,QACF;AACA,mBAAW,UAAU,MAAM,KAAK,KAAK,eAAe,GAAG;AACrD,cAAI,CAAC,WAAW,IAAI,MAAM,GAAG;AAC3B,4BAAgB,KAAK,MAAM;AAAA,UAC7B;AAAA,QACF;AACA,aAAK,gBAAgB,WAAW,cAAc,IAAI;AAClD,aAAK,gBAAgB,cAAc,iBAAiB,IAAI;AACxD,aAAK,gBAAgB,OAAO;AAC5B,mBAAW,UAAU,iBAAiB;AACpC,eAAK,gBAAgB,OAAO,MAAM;AAAA,QACpC;AAAA,MACF,EAAC;AAAA,IACH;AAAA;AAAA,EACA,kBAAkB;AAIhB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,oBAAoB,SAAS;AAC3B,WAAO,QAAQ,IAAI,QAAQ,IAAI,YAAU,OAAO,eAAe,CAAC,CAAC;AAAA,EACnE;AAAA,EACA,qBAAqB;AACnB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,KAAK,WAAW,WAAW;AAC9B,cAAM,MAAM,8IAAmJ;AAAA,MACjK;AACA,UAAI,CAAC,KAAK,iBAAiB;AACzB,cAAM,MAAM,uJAA4J;AAAA,MAC1K;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,IACpC,gBAAgB,SAAS,kCAAkC,IAAI,KAAK,UAAU;AAC5E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,YAAY,CAAC;AAAA,MAC3C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,4BAA4B;AAAA,IAC9B;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,IAC/B,UAAU,CAAI,oBAAoB;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAa,CAAC,WAAW,cAAc,mBAAmB,WAAW,uBAAuB,kBAAkB,iBAAiB,eAAe,aAAa,WAAW,mBAAmB,8BAA8B,YAAY,aAAa,cAAc,iBAAiB,iBAAiB,kBAAkB;AACxT,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,WAAW,cAAc,mBAAmB,WAAW,uBAAuB,kBAAkB,iBAAiB,eAAe,aAAa,WAAW,mBAAmB,8BAA8B,YAAY,aAAa,cAAc,iBAAiB,iBAAiB,kBAAkB;AAAA,IAC9S,SAAS,CAAC,WAAW,cAAc,mBAAmB,WAAW,uBAAuB,kBAAkB,iBAAiB,eAAe,aAAa,WAAW,mBAAmB,8BAA8B,YAAY,aAAa,cAAc,iBAAiB,iBAAiB,kBAAkB;AAAA,EAChT,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,UAAU,OAAO,MAAM;AAAA,EACvB;AAAA,EACA,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMf,MAAM,SAAS;AACb,WAAO,IAAI,WAAW,cAAY;AAChC,WAAK,YAAY,EAAE,KAAK,aAAW;AACjC,gBAAQ,MAAM,SAAS,CAAC,QAAQ,WAAW;AACzC,eAAK,QAAQ,IAAI,MAAM;AACrB,qBAAS,KAAK;AAAA,cACZ,QAAQ,UAAU;AAAA,cAClB;AAAA,YACF,CAAC;AACD,qBAAS,SAAS;AAAA,UACpB,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,oBAAoB;AAC5B,UAAI,OAAO,KAAK,mBAAmB;AACjC,aAAK,qBAAqB,IAAI,OAAO,KAAK,kBAAkB;AAAA,MAC9D,OAAO;AACL,eAAO,OAAO,KAAK,cAAc,QAAQ,EAAE,KAAK,SAAO;AACrD,eAAK,qBAAqB,IAAI,IAAI,kBAAkB;AACpD,iBAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,QAAQ,QAAQ,KAAK,kBAAkB;AAAA,EAChD;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,IAC9B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,UAAU,OAAO,MAAM;AAAA,EACvB;AAAA,EACA,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA,EAIf,QAAQ,SAAS;AACf,WAAO,IAAI,WAAW,cAAY;AAChC,WAAK,aAAa,EAAE,KAAK,cAAY;AACnC,iBAAS,QAAQ,SAAS,CAAC,SAAS,WAAW;AAC7C,eAAK,QAAQ,IAAI,MAAM;AACrB,qBAAS,KAAK;AAAA,cACZ,SAAS,WAAW,CAAC;AAAA,cACrB;AAAA,YACF,CAAC;AACD,qBAAS,SAAS;AAAA,UACpB,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AACb,QAAI,CAAC,KAAK,WAAW;AACnB,UAAI,OAAO,KAAK,UAAU;AACxB,aAAK,YAAY,IAAI,OAAO,KAAK,SAAS;AAAA,MAC5C,OAAO;AACL,eAAO,OAAO,KAAK,cAAc,WAAW,EAAE,KAAK,SAAO;AACxD,eAAK,YAAY,IAAI,IAAI,SAAS;AAClC,iBAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,EACvC;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,aAAY;AAAA,IACrB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;", "names": ["map"]}