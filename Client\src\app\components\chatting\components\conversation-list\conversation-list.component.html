<div class="conversation-list-container">
    <h2>Your Conversations</h2>

    <div *ngIf="loading" class="loading">
        <div class="spinner"></div>
        <p>Loading conversations...</p>
    </div>

    <div *ngIf="!loading && conversations.length === 0" class="no-conversations">
        <p>You don't have any conversations yet.</p>
    </div>

    <div class="conversation-list">
        <div *ngFor="let conversation of conversations" class="conversation-item"
            [class.unread]="hasUnreadMessages(conversation)" (click)="openConversation(conversation)">

            <div class="avatar">
                <img [src]="  getOtherUser(conversation)?.profilePictureUrl || '/assets/default-avatar.png'"
                    [alt]="getOtherUser(conversation)?.firstName + ' ' + getOtherUser(conversation)?.lastName">
            </div>

            <div class="conversation-details">
                <div class="header">
                    <h3 class="name">{{ getOtherUser(conversation)?.firstName }} {{ getOtherUser(conversation)?.lastName
                        }}</h3>
                    <span class="time" *ngIf="conversation.messages && conversation.messages.length > 0">
                        {{ conversation.messages[0].sentAt | date:'shortTime' }}
                    </span>
                </div>

                <p class="last-message">{{ getLastMessage(conversation) }}</p>
            </div>

            <div *ngIf="hasUnreadMessages(conversation)" class="unread-indicator"></div>
        </div>
    </div>
</div>