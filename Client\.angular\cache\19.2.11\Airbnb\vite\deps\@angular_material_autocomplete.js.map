{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/autocomplete.mjs"], "sourcesContent": ["import { c as MAT_OPTION_PARENT_COMPONENT, M as MatOption, d as MAT_OPTGROUP, e as MatOptionSelectionChange, _ as _countGroupLabelsBeforeOption, b as _getOptionScrollPosition } from './option-ChV6uQgD.mjs';\nconst _c0 = [\"panel\"];\nconst _c1 = [\"*\"];\nfunction MatAutocomplete_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1, 0);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formFieldId_r1 = ctx.id;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1._classList);\n    i0.ɵɵclassProp(\"mat-mdc-autocomplete-visible\", ctx_r1.showPanel)(\"mat-mdc-autocomplete-hidden\", !ctx_r1.showPanel)(\"mat-autocomplete-panel-animations-enabled\", !ctx_r1._animationsDisabled)(\"mat-primary\", ctx_r1._color === \"primary\")(\"mat-accent\", ctx_r1._color === \"accent\")(\"mat-warn\", ctx_r1._color === \"warn\");\n    i0.ɵɵproperty(\"id\", ctx_r1.id);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1._getPanelAriaLabelledby(formFieldId_r1));\n  }\n}\nexport { a as MatOptgroup } from './option-ChV6uQgD.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ChangeDetectorRef, ElementRef, ANIMATION_MODULE_TYPE, EventEmitter, booleanAttribute, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, ContentChildren, Input, Output, Directive, forwardRef, EnvironmentInjector, ViewContainerRef, NgZone, Renderer2, afterNextRender, NgModule } from '@angular/core';\nimport { ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { _IdGenerator, ActiveDescendantKeyManager, removeAriaReferencedId, addAriaReferencedId } from '@angular/cdk/a11y';\nimport { Platform, _getFocusedElementPierceShadowDom, _getEventTarget } from '@angular/cdk/platform';\nimport { Subscription, Subject, merge, of, defer, Observable } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { hasModifierKey, ESCAPE, ENTER, TAB, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { filter, map, startWith, switchMap, tap, delay, take } from 'rxjs/operators';\nimport { h as MAT_FORM_FIELD } from './form-field-B4o2BB25.mjs';\nimport { M as MatOptionModule } from './index-DOxJc1m4.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/private';\nimport './pseudo-checkbox-CJ7seqQH.mjs';\nimport './structural-styles-BQUT6wsL.mjs';\nimport '@angular/common';\nimport '@angular/cdk/observers/private';\nimport './index-SYVYjXwK.mjs';\nimport './pseudo-checkbox-module-CAX2sutq.mjs';\n\n/** Event object that is emitted when an autocomplete option is selected. */\nclass MatAutocompleteSelectedEvent {\n  source;\n  option;\n  constructor(/** Reference to the autocomplete panel that emitted the event. */\n  source, /** Option that was selected. */\n  option) {\n    this.source = source;\n    this.option = option;\n  }\n}\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = new InjectionToken('mat-autocomplete-default-options', {\n  providedIn: 'root',\n  factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    autoActiveFirstOption: false,\n    autoSelectActiveOption: false,\n    hideSingleSelectionIndicator: false,\n    requireSelection: false,\n    hasBackdrop: false\n  };\n}\n/** Autocomplete component. */\nclass MatAutocomplete {\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  _defaults = inject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS);\n  _animationsDisabled = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  }) === 'NoopAnimations';\n  _activeOptionChanges = Subscription.EMPTY;\n  /** Manages active item in option list based on key events. */\n  _keyManager;\n  /** Whether the autocomplete panel should be visible, depending on option length. */\n  showPanel = false;\n  /** Whether the autocomplete panel is open. */\n  get isOpen() {\n    return this._isOpen && this.showPanel;\n  }\n  _isOpen = false;\n  /** Latest trigger that opened the autocomplete. */\n  _latestOpeningTrigger;\n  /** @docs-private Sets the theme color of the panel. */\n  _setColor(value) {\n    this._color = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** @docs-private theme color of the panel */\n  _color;\n  // The @ViewChild query for TemplateRef here needs to be static because some code paths\n  // lead to the overlay being created before change detection has finished for this component.\n  // Notably, another component may trigger `focus` on the autocomplete-trigger.\n  /** @docs-private */\n  template;\n  /** Element for the panel containing the autocomplete options. */\n  panel;\n  /** Reference to all options within the autocomplete. */\n  options;\n  /** Reference to all option groups within the autocomplete. */\n  optionGroups;\n  /** Aria label of the autocomplete. */\n  ariaLabel;\n  /** Input that can be used to specify the `aria-labelledby` attribute. */\n  ariaLabelledby;\n  /** Function that maps an option's control value to its display value in the trigger. */\n  displayWith = null;\n  /**\n   * Whether the first option should be highlighted when the autocomplete panel is opened.\n   * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n   */\n  autoActiveFirstOption;\n  /** Whether the active option should be selected as the user is navigating. */\n  autoSelectActiveOption;\n  /**\n   * Whether the user is required to make a selection when they're interacting with the\n   * autocomplete. If the user moves away from the autocomplete without selecting an option from\n   * the list, the value will be reset. If the user opens the panel and closes it without\n   * interacting or selecting a value, the initial value will be kept.\n   */\n  requireSelection;\n  /**\n   * Specify the width of the autocomplete panel.  Can be any CSS sizing value, otherwise it will\n   * match the width of its host.\n   */\n  panelWidth;\n  /** Whether ripples are disabled within the autocomplete panel. */\n  disableRipple;\n  /** Event that is emitted whenever an option from the list is selected. */\n  optionSelected = new EventEmitter();\n  /** Event that is emitted when the autocomplete panel is opened. */\n  opened = new EventEmitter();\n  /** Event that is emitted when the autocomplete panel is closed. */\n  closed = new EventEmitter();\n  /** Emits whenever an option is activated. */\n  optionActivated = new EventEmitter();\n  /**\n   * Takes classes set on the host mat-autocomplete element and applies them to the panel\n   * inside the overlay container to allow for easy styling.\n   */\n  set classList(value) {\n    this._classList = value;\n    this._elementRef.nativeElement.className = '';\n  }\n  _classList;\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncParentProperties();\n  }\n  _hideSingleSelectionIndicator;\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties() {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n  id = inject(_IdGenerator).getId('mat-autocomplete-');\n  /**\n   * Tells any descendant `mat-optgroup` to use the inert a11y pattern.\n   * @docs-private\n   */\n  inertGroups;\n  constructor() {\n    const platform = inject(Platform);\n    // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n    // Safari using VoiceOver. We should occasionally check back to see whether the bug\n    // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n    // option altogether.\n    this.inertGroups = platform?.SAFARI || false;\n    this.autoActiveFirstOption = !!this._defaults.autoActiveFirstOption;\n    this.autoSelectActiveOption = !!this._defaults.autoSelectActiveOption;\n    this.requireSelection = !!this._defaults.requireSelection;\n    this._hideSingleSelectionIndicator = this._defaults.hideSingleSelectionIndicator ?? false;\n  }\n  ngAfterContentInit() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withWrap().skipPredicate(this._skipPredicate);\n    this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n      if (this.isOpen) {\n        this.optionActivated.emit({\n          source: this,\n          option: this.options.toArray()[index] || null\n        });\n      }\n    });\n    // Set the initial visibility state.\n    this._setVisibility();\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._activeOptionChanges.unsubscribe();\n  }\n  /**\n   * Sets the panel scrollTop. This allows us to manually scroll to display options\n   * above or below the fold, as they are not actually being focused when active.\n   */\n  _setScrollTop(scrollTop) {\n    if (this.panel) {\n      this.panel.nativeElement.scrollTop = scrollTop;\n    }\n  }\n  /** Returns the panel's scrollTop. */\n  _getScrollTop() {\n    return this.panel ? this.panel.nativeElement.scrollTop : 0;\n  }\n  /** Panel should hide itself when the option list is empty. */\n  _setVisibility() {\n    this.showPanel = !!this.options?.length;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits the `select` event. */\n  _emitSelectEvent(option) {\n    const event = new MatAutocompleteSelectedEvent(this, option);\n    this.optionSelected.emit(event);\n  }\n  /** Gets the aria-labelledby for the autocomplete panel. */\n  _getPanelAriaLabelledby(labelId) {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n  // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n  // recommendation.\n  //\n  // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n  // makes a few exceptions for compound widgets.\n  //\n  // From [Developing a Keyboard Interface](\n  // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n  //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n  //   Listbox...\"\n  //\n  // The user can focus disabled options using the keyboard, but the user cannot click disabled\n  // options.\n  _skipPredicate() {\n    return false;\n  }\n  static ɵfac = function MatAutocomplete_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatAutocomplete)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatAutocomplete,\n    selectors: [[\"mat-autocomplete\"]],\n    contentQueries: function MatAutocomplete_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n      }\n    },\n    viewQuery: function MatAutocomplete_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(TemplateRef, 7);\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-autocomplete\"],\n    inputs: {\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      displayWith: \"displayWith\",\n      autoActiveFirstOption: [2, \"autoActiveFirstOption\", \"autoActiveFirstOption\", booleanAttribute],\n      autoSelectActiveOption: [2, \"autoSelectActiveOption\", \"autoSelectActiveOption\", booleanAttribute],\n      requireSelection: [2, \"requireSelection\", \"requireSelection\", booleanAttribute],\n      panelWidth: \"panelWidth\",\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      classList: [0, \"class\", \"classList\"],\n      hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute]\n    },\n    outputs: {\n      optionSelected: \"optionSelected\",\n      opened: \"opened\",\n      closed: \"closed\",\n      optionActivated: \"optionActivated\"\n    },\n    exportAs: [\"matAutocomplete\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_OPTION_PARENT_COMPONENT,\n      useExisting: MatAutocomplete\n    }])],\n    ngContentSelectors: _c1,\n    decls: 1,\n    vars: 0,\n    consts: [[\"panel\", \"\"], [\"role\", \"listbox\", 1, \"mat-mdc-autocomplete-panel\", \"mdc-menu-surface\", \"mdc-menu-surface--open\", 3, \"id\"]],\n    template: function MatAutocomplete_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, MatAutocomplete_ng_template_0_Template, 3, 17, \"ng-template\");\n      }\n    },\n    styles: [\"div.mat-mdc-autocomplete-panel{width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;box-sizing:border-box;position:relative;border-radius:var(--mat-autocomplete-container-shape, var(--mat-sys-corner-extra-small));box-shadow:var(--mat-autocomplete-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));background-color:var(--mat-autocomplete-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-autocomplete-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden;pointer-events:none}@keyframes _mat-autocomplete-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}.mat-autocomplete-panel-animations-enabled{animation:_mat-autocomplete-enter 120ms cubic-bezier(0, 0, 0.2, 1)}mat-autocomplete{display:none}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocomplete, [{\n    type: Component,\n    args: [{\n      selector: 'mat-autocomplete',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      exportAs: 'matAutocomplete',\n      host: {\n        'class': 'mat-mdc-autocomplete'\n      },\n      providers: [{\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatAutocomplete\n      }],\n      template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div\\n    class=\\\"mat-mdc-autocomplete-panel mdc-menu-surface mdc-menu-surface--open\\\"\\n    role=\\\"listbox\\\"\\n    [id]=\\\"id\\\"\\n    [class]=\\\"_classList\\\"\\n    [class.mat-mdc-autocomplete-visible]=\\\"showPanel\\\"\\n    [class.mat-mdc-autocomplete-hidden]=\\\"!showPanel\\\"\\n    [class.mat-autocomplete-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [class.mat-primary]=\\\"_color === 'primary'\\\"\\n    [class.mat-accent]=\\\"_color === 'accent'\\\"\\n    [class.mat-warn]=\\\"_color === 'warn'\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n    #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"div.mat-mdc-autocomplete-panel{width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;box-sizing:border-box;position:relative;border-radius:var(--mat-autocomplete-container-shape, var(--mat-sys-corner-extra-small));box-shadow:var(--mat-autocomplete-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));background-color:var(--mat-autocomplete-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-autocomplete-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden;pointer-events:none}@keyframes _mat-autocomplete-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}.mat-autocomplete-panel-animations-enabled{animation:_mat-autocomplete-enter 120ms cubic-bezier(0, 0, 0.2, 1)}mat-autocomplete{display:none}\\n\"]\n    }]\n  }], () => [], {\n    template: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }],\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    displayWith: [{\n      type: Input\n    }],\n    autoActiveFirstOption: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoSelectActiveOption: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    requireSelection: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    panelWidth: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    optionSelected: [{\n      type: Output\n    }],\n    opened: [{\n      type: Output\n    }],\n    closed: [{\n      type: Output\n    }],\n    optionActivated: [{\n      type: Output\n    }],\n    classList: [{\n      type: Input,\n      args: ['class']\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\nclass MatAutocompleteOrigin {\n  elementRef = inject(ElementRef);\n  constructor() {}\n  static ɵfac = function MatAutocompleteOrigin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatAutocompleteOrigin)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatAutocompleteOrigin,\n    selectors: [[\"\", \"matAutocompleteOrigin\", \"\"]],\n    exportAs: [\"matAutocompleteOrigin\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[matAutocompleteOrigin]',\n      exportAs: 'matAutocompleteOrigin'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatAutocompleteTrigger),\n  multi: true\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\nfunction getMatAutocompleteMissingPanelError() {\n  return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' + 'Make sure that the id passed to the `matAutocomplete` is correct and that ' + \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = new InjectionToken('mat-autocomplete-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY\n};\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\nclass MatAutocompleteTrigger {\n  _environmentInjector = inject(EnvironmentInjector);\n  _element = inject(ElementRef);\n  _overlay = inject(Overlay);\n  _viewContainerRef = inject(ViewContainerRef);\n  _zone = inject(NgZone);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _formField = inject(MAT_FORM_FIELD, {\n    optional: true,\n    host: true\n  });\n  _viewportRuler = inject(ViewportRuler);\n  _scrollStrategy = inject(MAT_AUTOCOMPLETE_SCROLL_STRATEGY);\n  _renderer = inject(Renderer2);\n  _defaults = inject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _overlayRef;\n  _portal;\n  _componentDestroyed = false;\n  _initialized = new Subject();\n  _keydownSubscription;\n  _outsideClickSubscription;\n  _cleanupWindowBlur;\n  /** Old value of the native input. Used to work around issues with the `input` event on IE. */\n  _previousValue;\n  /** Value of the input element when the panel was attached (even if there are no options). */\n  _valueOnAttach;\n  /** Value on the previous keydown event. */\n  _valueOnLastKeydown;\n  /** Strategy that is used to position the panel. */\n  _positionStrategy;\n  /** Whether or not the label state is being overridden. */\n  _manuallyFloatingLabel = false;\n  /** The subscription for closing actions (some are bound to document). */\n  _closingActionsSubscription;\n  /** Subscription to viewport size changes. */\n  _viewportSubscription = Subscription.EMPTY;\n  /** Implements BreakpointObserver to be used to detect handset landscape */\n  _breakpointObserver = inject(BreakpointObserver);\n  _handsetLandscapeSubscription = Subscription.EMPTY;\n  /**\n   * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n   * closed autocomplete from being reopened if the user switches to another browser tab and then\n   * comes back.\n   */\n  _canOpenOnNextFocus = true;\n  /** Value inside the input before we auto-selected an option. */\n  _valueBeforeAutoSelection;\n  /**\n   * Current option that we have auto-selected as the user is navigating,\n   * but which hasn't been propagated to the model value yet.\n   */\n  _pendingAutoselectedOption;\n  /** Stream of keyboard events that can close the panel. */\n  _closeKeyEventStream = new Subject();\n  /**\n   * Event handler for when the window is blurred. Needs to be an\n   * arrow function in order to preserve the context.\n   */\n  _windowBlurHandler = () => {\n    // If the user blurred the window while the autocomplete is focused, it means that it'll be\n    // refocused when they come back. In this case we want to skip the first focus event, if the\n    // pane was closed, in order to avoid reopening it unintentionally.\n    this._canOpenOnNextFocus = this.panelOpen || !this._hasFocus();\n  };\n  /** `View -> model callback called when value changes` */\n  _onChange = () => {};\n  /** `View -> model callback called when autocomplete has been touched` */\n  _onTouched = () => {};\n  /** The autocomplete panel to be attached to this trigger. */\n  autocomplete;\n  /**\n   * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n   * will render the panel underneath the trigger if there is enough space for it to fit in\n   * the viewport, otherwise the panel will be shown above it. If the position is set to\n   * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n   * whether it fits completely in the viewport.\n   */\n  position = 'auto';\n  /**\n   * Reference relative to which to position the autocomplete panel.\n   * Defaults to the autocomplete trigger element.\n   */\n  connectedTo;\n  /**\n   * `autocomplete` attribute to be set on the input element.\n   * @docs-private\n   */\n  autocompleteAttribute = 'off';\n  /**\n   * Whether the autocomplete is disabled. When disabled, the element will\n   * act as a regular input and the user won't be able to open the panel.\n   */\n  autocompleteDisabled;\n  constructor() {}\n  /** Class to apply to the panel when it's above the input. */\n  _aboveClass = 'mat-mdc-autocomplete-panel-above';\n  ngAfterViewInit() {\n    this._initialized.next();\n    this._initialized.complete();\n    this._cleanupWindowBlur = this._renderer.listen('window', 'blur', this._windowBlurHandler);\n  }\n  ngOnChanges(changes) {\n    if (changes['position'] && this._positionStrategy) {\n      this._setStrategyPositions(this._positionStrategy);\n      if (this.panelOpen) {\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._cleanupWindowBlur?.();\n    this._handsetLandscapeSubscription.unsubscribe();\n    this._viewportSubscription.unsubscribe();\n    this._componentDestroyed = true;\n    this._destroyPanel();\n    this._closeKeyEventStream.complete();\n    this._clearFromModal();\n  }\n  /** Whether or not the autocomplete panel is open. */\n  get panelOpen() {\n    return this._overlayAttached && this.autocomplete.showPanel;\n  }\n  _overlayAttached = false;\n  /** Opens the autocomplete suggestion panel. */\n  openPanel() {\n    this._openPanelInternal();\n  }\n  /** Closes the autocomplete suggestion panel. */\n  closePanel() {\n    this._resetLabel();\n    if (!this._overlayAttached) {\n      return;\n    }\n    if (this.panelOpen) {\n      // Only emit if the panel was visible.\n      // `afterNextRender` always runs outside of the Angular zone, so all the subscriptions from\n      // `_subscribeToClosingActions()` are also outside of the Angular zone.\n      // We should manually run in Angular zone to update UI after panel closing.\n      this._zone.run(() => {\n        this.autocomplete.closed.emit();\n      });\n    }\n    // Only reset if this trigger is the latest one that opened the\n    // autocomplete since another may have taken it over.\n    if (this.autocomplete._latestOpeningTrigger === this) {\n      this.autocomplete._isOpen = false;\n      this.autocomplete._latestOpeningTrigger = null;\n    }\n    this._overlayAttached = false;\n    this._pendingAutoselectedOption = null;\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n      this._closingActionsSubscription.unsubscribe();\n    }\n    this._updatePanelState();\n    // Note that in some cases this can end up being called after the component is destroyed.\n    // Add a check to ensure that we don't try to run change detection on a destroyed view.\n    if (!this._componentDestroyed) {\n      // We need to trigger change detection manually, because\n      // `fromEvent` doesn't seem to do it at the proper time.\n      // This ensures that the label is reset when the\n      // user clicks outside.\n      this._changeDetectorRef.detectChanges();\n    }\n    // Remove aria-owns attribute when the autocomplete is no longer visible.\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', this.autocomplete.id);\n    }\n  }\n  /**\n   * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n   * within the viewport.\n   */\n  updatePosition() {\n    if (this._overlayAttached) {\n      this._overlayRef.updatePosition();\n    }\n  }\n  /**\n   * A stream of actions that should close the autocomplete panel, including\n   * when an option is selected, on blur, and when TAB is pressed.\n   */\n  get panelClosingActions() {\n    return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached)) : of()).pipe(\n    // Normalize the output so we return a consistent type.\n    map(event => event instanceof MatOptionSelectionChange ? event : null));\n  }\n  /** Stream of changes to the selection state of the autocomplete options. */\n  optionSelections = defer(() => {\n    const options = this.autocomplete ? this.autocomplete.options : null;\n    if (options) {\n      return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n    }\n    // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n    // Return a stream that we'll replace with the real one once everything is in place.\n    return this._initialized.pipe(switchMap(() => this.optionSelections));\n  });\n  /** The currently active option, coerced to MatOption type. */\n  get activeOption() {\n    if (this.autocomplete && this.autocomplete._keyManager) {\n      return this.autocomplete._keyManager.activeItem;\n    }\n    return null;\n  }\n  /** Stream of clicks outside of the autocomplete panel. */\n  _getOutsideClickStream() {\n    return new Observable(observer => {\n      const listener = event => {\n        // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n        // fall back to check the first element in the path of the click event.\n        const clickTarget = _getEventTarget(event);\n        const formField = this._formField ? this._formField.getConnectedOverlayOrigin().nativeElement : null;\n        const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n        if (this._overlayAttached && clickTarget !== this._element.nativeElement &&\n        // Normally focus moves inside `mousedown` so this condition will almost always be\n        // true. Its main purpose is to handle the case where the input is focused from an\n        // outside click which propagates up to the `body` listener within the same sequence\n        // and causes the panel to close immediately (see #3106).\n        !this._hasFocus() && (!formField || !formField.contains(clickTarget)) && (!customOrigin || !customOrigin.contains(clickTarget)) && !!this._overlayRef && !this._overlayRef.overlayElement.contains(clickTarget)) {\n          observer.next(event);\n        }\n      };\n      const cleanups = [this._renderer.listen('document', 'click', listener), this._renderer.listen('document', 'auxclick', listener), this._renderer.listen('document', 'touchend', listener)];\n      return () => {\n        cleanups.forEach(current => current());\n      };\n    });\n  }\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value) {\n    Promise.resolve(null).then(() => this._assignOptionValue(value));\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this._element.nativeElement.disabled = isDisabled;\n  }\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const hasModifier = hasModifierKey(event);\n    // Prevent the default action on all escape key presses. This is here primarily to bring IE\n    // in line with other browsers. By default, pressing escape on IE will cause it to revert\n    // the input value to the one that it had on focus, however it won't dispatch any events\n    // which means that the model value will be out of sync with the view.\n    if (keyCode === ESCAPE && !hasModifier) {\n      event.preventDefault();\n    }\n    this._valueOnLastKeydown = this._element.nativeElement.value;\n    if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n      this.activeOption._selectViaInteraction();\n      this._resetActiveItem();\n      event.preventDefault();\n    } else if (this.autocomplete) {\n      const prevActiveItem = this.autocomplete._keyManager.activeItem;\n      const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n      if (keyCode === TAB || isArrowKey && !hasModifier && this.panelOpen) {\n        this.autocomplete._keyManager.onKeydown(event);\n      } else if (isArrowKey && this._canOpen()) {\n        this._openPanelInternal(this._valueOnLastKeydown);\n      }\n      if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n        this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n        if (this.autocomplete.autoSelectActiveOption && this.activeOption) {\n          if (!this._pendingAutoselectedOption) {\n            this._valueBeforeAutoSelection = this._valueOnLastKeydown;\n          }\n          this._pendingAutoselectedOption = this.activeOption;\n          this._assignOptionValue(this.activeOption.value);\n        }\n      }\n    }\n  }\n  _handleInput(event) {\n    let target = event.target;\n    let value = target.value;\n    // Based on `NumberValueAccessor` from forms.\n    if (target.type === 'number') {\n      value = value == '' ? null : parseFloat(value);\n    }\n    // If the input has a placeholder, IE will fire the `input` event on page load,\n    // focus and blur, in addition to when the user actually changed the value. To\n    // filter out all of the extra events, we save the value on focus and between\n    // `input` events, and we check whether it changed.\n    // See: https://connect.microsoft.com/IE/feedback/details/885747/\n    if (this._previousValue !== value) {\n      this._previousValue = value;\n      this._pendingAutoselectedOption = null;\n      // If selection is required we don't write to the CVA while the user is typing.\n      // At the end of the selection either the user will have picked something\n      // or we'll reset the value back to null.\n      if (!this.autocomplete || !this.autocomplete.requireSelection) {\n        this._onChange(value);\n      }\n      if (!value) {\n        this._clearPreviousSelectedOption(null, false);\n      } else if (this.panelOpen && !this.autocomplete.requireSelection) {\n        // Note that we don't reset this when `requireSelection` is enabled,\n        // because the option will be reset when the panel is closed.\n        const selectedOption = this.autocomplete.options?.find(option => option.selected);\n        if (selectedOption) {\n          const display = this._getDisplayValue(selectedOption.value);\n          if (value !== display) {\n            selectedOption.deselect(false);\n          }\n        }\n      }\n      if (this._canOpen() && this._hasFocus()) {\n        // When the `input` event fires, the input's value will have already changed. This means\n        // that if we take the `this._element.nativeElement.value` directly, it'll be one keystroke\n        // behind. This can be a problem when the user selects a value, changes a character while\n        // the input still has focus and then clicks away (see #28432). To work around it, we\n        // capture the value in `keydown` so we can use it here.\n        const valueOnAttach = this._valueOnLastKeydown ?? this._element.nativeElement.value;\n        this._valueOnLastKeydown = null;\n        this._openPanelInternal(valueOnAttach);\n      }\n    }\n  }\n  _handleFocus() {\n    if (!this._canOpenOnNextFocus) {\n      this._canOpenOnNextFocus = true;\n    } else if (this._canOpen()) {\n      this._previousValue = this._element.nativeElement.value;\n      this._attachOverlay(this._previousValue);\n      this._floatLabel(true);\n    }\n  }\n  _handleClick() {\n    if (this._canOpen() && !this.panelOpen) {\n      this._openPanelInternal();\n    }\n  }\n  /** Whether the input currently has focus. */\n  _hasFocus() {\n    return _getFocusedElementPierceShadowDom() === this._element.nativeElement;\n  }\n  /**\n   * In \"auto\" mode, the label will animate down as soon as focus is lost.\n   * This causes the value to jump when selecting an option with the mouse.\n   * This method manually floats the label until the panel can be closed.\n   * @param shouldAnimate Whether the label should be animated when it is floated.\n   */\n  _floatLabel(shouldAnimate = false) {\n    if (this._formField && this._formField.floatLabel === 'auto') {\n      if (shouldAnimate) {\n        this._formField._animateAndLockLabel();\n      } else {\n        this._formField.floatLabel = 'always';\n      }\n      this._manuallyFloatingLabel = true;\n    }\n  }\n  /** If the label has been manually elevated, return it to its normal state. */\n  _resetLabel() {\n    if (this._manuallyFloatingLabel) {\n      if (this._formField) {\n        this._formField.floatLabel = 'auto';\n      }\n      this._manuallyFloatingLabel = false;\n    }\n  }\n  /**\n   * This method listens to a stream of panel closing actions and resets the\n   * stream every time the option list changes.\n   */\n  _subscribeToClosingActions() {\n    const initialRender = new Observable(subscriber => {\n      afterNextRender(() => {\n        subscriber.next();\n      }, {\n        injector: this._environmentInjector\n      });\n    });\n    const optionChanges = this.autocomplete.options?.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()),\n    // Defer emitting to the stream until the next tick, because changing\n    // bindings in here will cause \"changed after checked\" errors.\n    delay(0)) ?? of();\n    // When the options are initially rendered, and when the option list changes...\n    return merge(initialRender, optionChanges).pipe(\n    // create a new stream of panelClosingActions, replacing any previous streams\n    // that were created, and flatten it so our stream only emits closing events...\n    switchMap(() => this._zone.run(() => {\n      // `afterNextRender` always runs outside of the Angular zone, thus we have to re-enter\n      // the Angular zone. This will lead to change detection being called outside of the Angular\n      // zone and the `autocomplete.opened` will also emit outside of the Angular.\n      const wasOpen = this.panelOpen;\n      this._resetActiveItem();\n      this._updatePanelState();\n      this._changeDetectorRef.detectChanges();\n      if (this.panelOpen) {\n        this._overlayRef.updatePosition();\n      }\n      if (wasOpen !== this.panelOpen) {\n        // If the `panelOpen` state changed, we need to make sure to emit the `opened` or\n        // `closed` event, because we may not have emitted it. This can happen\n        // - if the users opens the panel and there are no options, but the\n        //   options come in slightly later or as a result of the value changing,\n        // - if the panel is closed after the user entered a string that did not match any\n        //   of the available options,\n        // - if a valid string is entered after an invalid one.\n        if (this.panelOpen) {\n          this._emitOpened();\n        } else {\n          this.autocomplete.closed.emit();\n        }\n      }\n      return this.panelClosingActions;\n    })),\n    // when the first closing event occurs...\n    take(1))\n    // set the value, close the panel, and complete.\n    .subscribe(event => this._setValueAndClose(event));\n  }\n  /**\n   * Emits the opened event once it's known that the panel will be shown and stores\n   * the state of the trigger right before the opening sequence was finished.\n   */\n  _emitOpened() {\n    this.autocomplete.opened.emit();\n  }\n  /** Destroys the autocomplete suggestion panel. */\n  _destroyPanel() {\n    if (this._overlayRef) {\n      this.closePanel();\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n  }\n  /** Given a value, returns the string that should be shown within the input. */\n  _getDisplayValue(value) {\n    const autocomplete = this.autocomplete;\n    return autocomplete && autocomplete.displayWith ? autocomplete.displayWith(value) : value;\n  }\n  _assignOptionValue(value) {\n    const toDisplay = this._getDisplayValue(value);\n    if (value == null) {\n      this._clearPreviousSelectedOption(null, false);\n    }\n    // Simply falling back to an empty string if the display value is falsy does not work properly.\n    // The display value can also be the number zero and shouldn't fall back to an empty string.\n    this._updateNativeInputValue(toDisplay != null ? toDisplay : '');\n  }\n  _updateNativeInputValue(value) {\n    // If it's used within a `MatFormField`, we should set it through the property so it can go\n    // through change detection.\n    if (this._formField) {\n      this._formField._control.value = value;\n    } else {\n      this._element.nativeElement.value = value;\n    }\n    this._previousValue = value;\n  }\n  /**\n   * This method closes the panel, and if a value is specified, also sets the associated\n   * control to that value. It will also mark the control as dirty if this interaction\n   * stemmed from the user.\n   */\n  _setValueAndClose(event) {\n    const panel = this.autocomplete;\n    const toSelect = event ? event.source : this._pendingAutoselectedOption;\n    if (toSelect) {\n      this._clearPreviousSelectedOption(toSelect);\n      this._assignOptionValue(toSelect.value);\n      // TODO(crisbeto): this should wait until the animation is done, otherwise the value\n      // gets reset while the panel is still animating which looks glitchy. It'll likely break\n      // some tests to change it at this point.\n      this._onChange(toSelect.value);\n      panel._emitSelectEvent(toSelect);\n      this._element.nativeElement.focus();\n    } else if (panel.requireSelection && this._element.nativeElement.value !== this._valueOnAttach) {\n      this._clearPreviousSelectedOption(null);\n      this._assignOptionValue(null);\n      this._onChange(null);\n    }\n    this.closePanel();\n  }\n  /**\n   * Clear any previous selected option and emit a selection change event for this option\n   */\n  _clearPreviousSelectedOption(skip, emitEvent) {\n    // Null checks are necessary here, because the autocomplete\n    // or its options may not have been assigned yet.\n    this.autocomplete?.options?.forEach(option => {\n      if (option !== skip && option.selected) {\n        option.deselect(emitEvent);\n      }\n    });\n  }\n  _openPanelInternal(valueOnAttach = this._element.nativeElement.value) {\n    this._attachOverlay(valueOnAttach);\n    this._floatLabel();\n    // Add aria-owns attribute when the autocomplete becomes visible.\n    if (this._trackedModal) {\n      const panelId = this.autocomplete.id;\n      addAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n  }\n  _attachOverlay(valueOnAttach) {\n    if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatAutocompleteMissingPanelError();\n    }\n    let overlayRef = this._overlayRef;\n    if (!overlayRef) {\n      this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n        id: this._formField?.getLabelId()\n      });\n      overlayRef = this._overlay.create(this._getOverlayConfig());\n      this._overlayRef = overlayRef;\n      this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n        if (this.panelOpen && overlayRef) {\n          overlayRef.updateSize({\n            width: this._getPanelWidth()\n          });\n        }\n      });\n      // Subscribe to the breakpoint events stream to detect when screen is in\n      // handsetLandscape.\n      this._handsetLandscapeSubscription = this._breakpointObserver.observe(Breakpoints.HandsetLandscape).subscribe(result => {\n        const isHandsetLandscape = result.matches;\n        // Check if result.matches Breakpoints.HandsetLandscape. Apply HandsetLandscape\n        // settings to prevent overlay cutoff in that breakpoint. Fixes b/284148377\n        if (isHandsetLandscape) {\n          this._positionStrategy.withFlexibleDimensions(true).withGrowAfterOpen(true).withViewportMargin(8);\n        } else {\n          this._positionStrategy.withFlexibleDimensions(false).withGrowAfterOpen(false).withViewportMargin(0);\n        }\n      });\n    } else {\n      // Update the trigger, panel width and direction, in case anything has changed.\n      this._positionStrategy.setOrigin(this._getConnectedElement());\n      overlayRef.updateSize({\n        width: this._getPanelWidth()\n      });\n    }\n    if (overlayRef && !overlayRef.hasAttached()) {\n      overlayRef.attach(this._portal);\n      this._valueOnAttach = valueOnAttach;\n      this._valueOnLastKeydown = null;\n      this._closingActionsSubscription = this._subscribeToClosingActions();\n    }\n    const wasOpen = this.panelOpen;\n    this.autocomplete._isOpen = this._overlayAttached = true;\n    this.autocomplete._latestOpeningTrigger = this;\n    this.autocomplete._setColor(this._formField?.color);\n    this._updatePanelState();\n    this._applyModalPanelOwnership();\n    // We need to do an extra `panelOpen` check in here, because the\n    // autocomplete won't be shown if there are no options.\n    if (this.panelOpen && wasOpen !== this.panelOpen) {\n      this._emitOpened();\n    }\n  }\n  /** Handles keyboard events coming from the overlay panel. */\n  _handlePanelKeydown = event => {\n    // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n    // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n    if (event.keyCode === ESCAPE && !hasModifierKey(event) || event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey')) {\n      // If the user had typed something in before we autoselected an option, and they decided\n      // to cancel the selection, restore the input value to the one they had typed in.\n      if (this._pendingAutoselectedOption) {\n        this._updateNativeInputValue(this._valueBeforeAutoSelection ?? '');\n        this._pendingAutoselectedOption = null;\n      }\n      this._closeKeyEventStream.next();\n      this._resetActiveItem();\n      // We need to stop propagation, otherwise the event will eventually\n      // reach the input itself and cause the overlay to be reopened.\n      event.stopPropagation();\n      event.preventDefault();\n    }\n  };\n  /** Updates the panel's visibility state and any trigger state tied to id. */\n  _updatePanelState() {\n    this.autocomplete._setVisibility();\n    // Note that here we subscribe and unsubscribe based on the panel's visiblity state,\n    // because the act of subscribing will prevent events from reaching other overlays and\n    // we don't want to block the events if there are no options.\n    if (this.panelOpen) {\n      const overlayRef = this._overlayRef;\n      if (!this._keydownSubscription) {\n        // Use the `keydownEvents` in order to take advantage of\n        // the overlay event targeting provided by the CDK overlay.\n        this._keydownSubscription = overlayRef.keydownEvents().subscribe(this._handlePanelKeydown);\n      }\n      if (!this._outsideClickSubscription) {\n        // Subscribe to the pointer events stream so that it doesn't get picked up by other overlays.\n        // TODO(crisbeto): we should switch `_getOutsideClickStream` eventually to use this stream,\n        // but the behvior isn't exactly the same and it ends up breaking some internal tests.\n        this._outsideClickSubscription = overlayRef.outsidePointerEvents().subscribe();\n      }\n    } else {\n      this._keydownSubscription?.unsubscribe();\n      this._outsideClickSubscription?.unsubscribe();\n      this._keydownSubscription = this._outsideClickSubscription = null;\n    }\n  }\n  _getOverlayConfig() {\n    return new OverlayConfig({\n      positionStrategy: this._getOverlayPosition(),\n      scrollStrategy: this._scrollStrategy(),\n      width: this._getPanelWidth(),\n      direction: this._dir ?? undefined,\n      hasBackdrop: this._defaults?.hasBackdrop,\n      backdropClass: this._defaults?.backdropClass,\n      panelClass: this._defaults?.overlayPanelClass\n    });\n  }\n  _getOverlayPosition() {\n    // Set default Overlay Position\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getConnectedElement()).withFlexibleDimensions(false).withPush(false);\n    this._setStrategyPositions(strategy);\n    this._positionStrategy = strategy;\n    return strategy;\n  }\n  /** Sets the positions on a position strategy based on the directive's input state. */\n  _setStrategyPositions(positionStrategy) {\n    // Note that we provide horizontal fallback positions, even though by default the dropdown\n    // width matches the input, because consumers can override the width. See #18854.\n    const belowPositions = [{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top'\n    }];\n    // The overlay edge connected to the trigger should have squared corners, while\n    // the opposite end has rounded corners. We apply a CSS class to swap the\n    // border-radius based on the overlay position.\n    const panelClass = this._aboveClass;\n    const abovePositions = [{\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass\n    }, {\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom',\n      panelClass\n    }];\n    let positions;\n    if (this.position === 'above') {\n      positions = abovePositions;\n    } else if (this.position === 'below') {\n      positions = belowPositions;\n    } else {\n      positions = [...belowPositions, ...abovePositions];\n    }\n    positionStrategy.withPositions(positions);\n  }\n  _getConnectedElement() {\n    if (this.connectedTo) {\n      return this.connectedTo.elementRef;\n    }\n    return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n  }\n  _getPanelWidth() {\n    return this.autocomplete.panelWidth || this._getHostWidth();\n  }\n  /** Returns the width of the input element, so the panel width can match it. */\n  _getHostWidth() {\n    return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n  }\n  /**\n   * Reset the active item to -1. This is so that pressing arrow keys will activate the correct\n   * option.\n   *\n   * If the consumer opted-in to automatically activatating the first option, activate the first\n   * *enabled* option.\n   */\n  _resetActiveItem() {\n    const autocomplete = this.autocomplete;\n    if (autocomplete.autoActiveFirstOption) {\n      // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n      // because it activates the first option that passes the skip predicate, rather than the\n      // first *enabled* option.\n      let firstEnabledOptionIndex = -1;\n      for (let index = 0; index < autocomplete.options.length; index++) {\n        const option = autocomplete.options.get(index);\n        if (!option.disabled) {\n          firstEnabledOptionIndex = index;\n          break;\n        }\n      }\n      autocomplete._keyManager.setActiveItem(firstEnabledOptionIndex);\n    } else {\n      autocomplete._keyManager.setActiveItem(-1);\n    }\n  }\n  /** Determines whether the panel can be opened. */\n  _canOpen() {\n    const element = this._element.nativeElement;\n    return !element.readOnly && !element.disabled && !this.autocompleteDisabled;\n  }\n  /** Scrolls to a particular option in the list. */\n  _scrollToOption(index) {\n    // Given that we are not actually focusing active options, we must manually adjust scroll\n    // to reveal options below the fold. First, we find the offset of the option from the top\n    // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n    // the panel height + the option height, so the active option will be just visible at the\n    // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n    // will become the offset. If that offset is visible within the panel already, the scrollTop is\n    // not adjusted.\n    const autocomplete = this.autocomplete;\n    const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n    if (index === 0 && labelCount === 1) {\n      // If we've got one group label before the option and we're at the top option,\n      // scroll the list to the top. This is better UX than scrolling the list to the\n      // top of the option, because it allows the user to read the top group's label.\n      autocomplete._setScrollTop(0);\n    } else if (autocomplete.panel) {\n      const option = autocomplete.options.toArray()[index];\n      if (option) {\n        const element = option._getHostElement();\n        const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n        autocomplete._setScrollTop(newScrollPosition);\n      }\n    }\n  }\n  /**\n   * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n   * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n   * panel. Track the modal we have changed so we can undo the changes on destroy.\n   */\n  _trackedModal = null;\n  /**\n   * If the autocomplete trigger is inside of an `aria-modal` element, connect\n   * that modal to the options panel with `aria-owns`.\n   *\n   * For some browser + screen reader combinations, when navigation is inside\n   * of an `aria-modal` element, the screen reader treats everything outside\n   * of that modal as hidden or invisible.\n   *\n   * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n   * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n   * from reaching the panel.\n   *\n   * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n   * the options panel. This effectively communicates to assistive technology that the\n   * options panel is part of the same interaction as the modal.\n   *\n   * At time of this writing, this issue is present in VoiceOver.\n   * See https://github.com/angular/components/issues/20694\n   */\n  _applyModalPanelOwnership() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modal = this._element.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    if (!modal) {\n      // Most commonly, the autocomplete trigger is not inside a modal.\n      return;\n    }\n    const panelId = this.autocomplete.id;\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n    addAriaReferencedId(modal, 'aria-owns', panelId);\n    this._trackedModal = modal;\n  }\n  /** Clears the references to the listbox overlay element from the modal it was added to. */\n  _clearFromModal() {\n    if (this._trackedModal) {\n      const panelId = this.autocomplete.id;\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      this._trackedModal = null;\n    }\n  }\n  static ɵfac = function MatAutocompleteTrigger_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatAutocompleteTrigger)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatAutocompleteTrigger,\n    selectors: [[\"input\", \"matAutocomplete\", \"\"], [\"textarea\", \"matAutocomplete\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-autocomplete-trigger\"],\n    hostVars: 7,\n    hostBindings: function MatAutocompleteTrigger_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focusin\", function MatAutocompleteTrigger_focusin_HostBindingHandler() {\n          return ctx._handleFocus();\n        })(\"blur\", function MatAutocompleteTrigger_blur_HostBindingHandler() {\n          return ctx._onTouched();\n        })(\"input\", function MatAutocompleteTrigger_input_HostBindingHandler($event) {\n          return ctx._handleInput($event);\n        })(\"keydown\", function MatAutocompleteTrigger_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        })(\"click\", function MatAutocompleteTrigger_click_HostBindingHandler() {\n          return ctx._handleClick();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"autocomplete\", ctx.autocompleteAttribute)(\"role\", ctx.autocompleteDisabled ? null : \"combobox\")(\"aria-autocomplete\", ctx.autocompleteDisabled ? null : \"list\")(\"aria-activedescendant\", ctx.panelOpen && ctx.activeOption ? ctx.activeOption.id : null)(\"aria-expanded\", ctx.autocompleteDisabled ? null : ctx.panelOpen.toString())(\"aria-controls\", ctx.autocompleteDisabled || !ctx.panelOpen ? null : ctx.autocomplete == null ? null : ctx.autocomplete.id)(\"aria-haspopup\", ctx.autocompleteDisabled ? null : \"listbox\");\n      }\n    },\n    inputs: {\n      autocomplete: [0, \"matAutocomplete\", \"autocomplete\"],\n      position: [0, \"matAutocompletePosition\", \"position\"],\n      connectedTo: [0, \"matAutocompleteConnectedTo\", \"connectedTo\"],\n      autocompleteAttribute: [0, \"autocomplete\", \"autocompleteAttribute\"],\n      autocompleteDisabled: [2, \"matAutocompleteDisabled\", \"autocompleteDisabled\", booleanAttribute]\n    },\n    exportAs: [\"matAutocompleteTrigger\"],\n    features: [i0.ɵɵProvidersFeature([MAT_AUTOCOMPLETE_VALUE_ACCESSOR]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `input[matAutocomplete], textarea[matAutocomplete]`,\n      host: {\n        'class': 'mat-mdc-autocomplete-trigger',\n        '[attr.autocomplete]': 'autocompleteAttribute',\n        '[attr.role]': 'autocompleteDisabled ? null : \"combobox\"',\n        '[attr.aria-autocomplete]': 'autocompleteDisabled ? null : \"list\"',\n        '[attr.aria-activedescendant]': '(panelOpen && activeOption) ? activeOption.id : null',\n        '[attr.aria-expanded]': 'autocompleteDisabled ? null : panelOpen.toString()',\n        '[attr.aria-controls]': '(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id',\n        '[attr.aria-haspopup]': 'autocompleteDisabled ? null : \"listbox\"',\n        // Note: we use `focusin`, as opposed to `focus`, in order to open the panel\n        // a little earlier. This avoids issues where IE delays the focusing of the input.\n        '(focusin)': '_handleFocus()',\n        '(blur)': '_onTouched()',\n        '(input)': '_handleInput($event)',\n        '(keydown)': '_handleKeydown($event)',\n        '(click)': '_handleClick()'\n      },\n      exportAs: 'matAutocompleteTrigger',\n      providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR]\n    }]\n  }], () => [], {\n    autocomplete: [{\n      type: Input,\n      args: ['matAutocomplete']\n    }],\n    position: [{\n      type: Input,\n      args: ['matAutocompletePosition']\n    }],\n    connectedTo: [{\n      type: Input,\n      args: ['matAutocompleteConnectedTo']\n    }],\n    autocompleteAttribute: [{\n      type: Input,\n      args: ['autocomplete']\n    }],\n    autocompleteDisabled: [{\n      type: Input,\n      args: [{\n        alias: 'matAutocompleteDisabled',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatAutocompleteModule {\n  static ɵfac = function MatAutocompleteModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatAutocompleteModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatAutocompleteModule,\n    imports: [OverlayModule, MatOptionModule, MatCommonModule, MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin],\n    exports: [CdkScrollableModule, MatAutocomplete, MatOptionModule, MatCommonModule, MatAutocompleteTrigger, MatAutocompleteOrigin]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n    imports: [OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatOptionModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, MatOptionModule, MatCommonModule, MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin],\n      exports: [CdkScrollableModule, MatAutocomplete, MatOptionModule, MatCommonModule, MatAutocompleteTrigger, MatAutocompleteOrigin],\n      providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, MatOption, getMatAutocompleteMissingPanelError };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,iBAAiB,IAAI;AAC3B,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,UAAU;AAC/B,IAAG,YAAY,gCAAgC,OAAO,SAAS,EAAE,+BAA+B,CAAC,OAAO,SAAS,EAAE,6CAA6C,CAAC,OAAO,mBAAmB,EAAE,eAAe,OAAO,WAAW,SAAS,EAAE,cAAc,OAAO,WAAW,QAAQ,EAAE,YAAY,OAAO,WAAW,MAAM;AACvT,IAAG,WAAW,MAAM,OAAO,EAAE;AAC7B,IAAG,YAAY,cAAc,OAAO,aAAa,IAAI,EAAE,mBAAmB,OAAO,wBAAwB,cAAc,CAAC;AAAA,EAC1H;AACF;AA6BA,IAAM,+BAAN,MAAmC;AAAA,EACjC;AAAA,EACA;AAAA,EACA,YACA,QACA,QAAQ;AACN,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AACF;AAEA,IAAM,mCAAmC,IAAI,eAAe,oCAAoC;AAAA,EAC9F,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,2CAA2C;AAClD,SAAO;AAAA,IACL,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,8BAA8B;AAAA,IAC9B,kBAAkB;AAAA,IAClB,aAAa;AAAA,EACf;AACF;AAEA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc,OAAO,UAAU;AAAA,EAC/B,YAAY,OAAO,gCAAgC;AAAA,EACnD,sBAAsB,OAAO,uBAAuB;AAAA,IAClD,UAAU;AAAA,EACZ,CAAC,MAAM;AAAA,EACP,uBAAuB,aAAa;AAAA;AAAA,EAEpC;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,IAAI,SAAS;AACX,WAAO,KAAK,WAAW,KAAK;AAAA,EAC9B;AAAA,EACA,UAAU;AAAA;AAAA,EAEV;AAAA;AAAA,EAEA,UAAU,OAAO;AACf,SAAK,SAAS;AACd,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,kBAAkB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa;AAClB,SAAK,YAAY,cAAc,YAAY;AAAA,EAC7C;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,+BAA+B;AACjC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,6BAA6B,OAAO;AACtC,SAAK,gCAAgC;AACrC,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA;AAAA;AAAA,EAEA,wBAAwB;AACtB,QAAI,KAAK,SAAS;AAChB,iBAAW,UAAU,KAAK,SAAS;AACjC,eAAO,mBAAmB,aAAa;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,KAAK,OAAO,YAAY,EAAE,MAAM,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnD;AAAA,EACA,cAAc;AACZ,UAAM,WAAW,OAAO,QAAQ;AAKhC,SAAK,cAAc,UAAU,UAAU;AACvC,SAAK,wBAAwB,CAAC,CAAC,KAAK,UAAU;AAC9C,SAAK,yBAAyB,CAAC,CAAC,KAAK,UAAU;AAC/C,SAAK,mBAAmB,CAAC,CAAC,KAAK,UAAU;AACzC,SAAK,gCAAgC,KAAK,UAAU,gCAAgC;AAAA,EACtF;AAAA,EACA,qBAAqB;AACnB,SAAK,cAAc,IAAI,2BAA2B,KAAK,OAAO,EAAE,SAAS,EAAE,cAAc,KAAK,cAAc;AAC5G,SAAK,uBAAuB,KAAK,YAAY,OAAO,UAAU,WAAS;AACrE,UAAI,KAAK,QAAQ;AACf,aAAK,gBAAgB,KAAK;AAAA,UACxB,QAAQ;AAAA,UACR,QAAQ,KAAK,QAAQ,QAAQ,EAAE,KAAK,KAAK;AAAA,QAC3C,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAED,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,QAAQ;AAC1B,SAAK,qBAAqB,YAAY;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,WAAW;AACvB,QAAI,KAAK,OAAO;AACd,WAAK,MAAM,cAAc,YAAY;AAAA,IACvC;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK,QAAQ,KAAK,MAAM,cAAc,YAAY;AAAA,EAC3D;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,YAAY,CAAC,CAAC,KAAK,SAAS;AACjC,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,iBAAiB,QAAQ;AACvB,UAAM,QAAQ,IAAI,6BAA6B,MAAM,MAAM;AAC3D,SAAK,eAAe,KAAK,KAAK;AAAA,EAChC;AAAA;AAAA,EAEA,wBAAwB,SAAS;AAC/B,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT;AACA,UAAM,kBAAkB,UAAU,UAAU,MAAM;AAClD,WAAO,KAAK,iBAAiB,kBAAkB,KAAK,iBAAiB;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,IAChC,gBAAgB,SAAS,+BAA+B,IAAI,KAAK,UAAU;AACzE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,WAAW,CAAC;AACxC,QAAG,eAAe,UAAU,cAAc,CAAC;AAAA,MAC7C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU;AAC3D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,sBAAsB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,aAAa,CAAC;AAC7B,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAC/D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,sBAAsB;AAAA,IACrC,QAAQ;AAAA,MACN,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,MACvD,aAAa;AAAA,MACb,uBAAuB,CAAC,GAAG,yBAAyB,yBAAyB,gBAAgB;AAAA,MAC7F,wBAAwB,CAAC,GAAG,0BAA0B,0BAA0B,gBAAgB;AAAA,MAChG,kBAAkB,CAAC,GAAG,oBAAoB,oBAAoB,gBAAgB;AAAA,MAC9E,YAAY;AAAA,MACZ,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,WAAW,CAAC,GAAG,SAAS,WAAW;AAAA,MACnC,8BAA8B,CAAC,GAAG,gCAAgC,gCAAgC,gBAAgB;AAAA,IACpH;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,iBAAiB;AAAA,IACnB;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,IAC5B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,QAAQ,WAAW,GAAG,8BAA8B,oBAAoB,0BAA0B,GAAG,IAAI,CAAC;AAAA,IACnI,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,WAAW,GAAG,wCAAwC,GAAG,IAAI,aAAa;AAAA,MAC/E;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,wyCAAwyC;AAAA,IACjzC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,wyCAAwyC;AAAA,IACnzC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,aAAa,OAAO,UAAU;AAAA,EAC9B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,yBAAyB,EAAE,CAAC;AAAA,IAC7C,UAAU,CAAC,uBAAuB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,kCAAkC;AAAA,EACtC,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,sBAAsB;AAAA,EACpD,OAAO;AACT;AAKA,SAAS,sCAAsC;AAC7C,SAAO,MAAM,2MAAqN;AACpO;AAEA,IAAM,mCAAmC,IAAI,eAAe,oCAAoC;AAAA,EAC9F,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,EACnD;AACF,CAAC;AAMD,SAAS,yCAAyC,SAAS;AACzD,SAAO,MAAM,QAAQ,iBAAiB,WAAW;AACnD;AAMA,IAAM,oDAAoD;AAAA,EACxD,SAAS;AAAA,EACT,MAAM,CAAC,OAAO;AAAA,EACd,YAAY;AACd;AAEA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,uBAAuB,OAAO,mBAAmB;AAAA,EACjD,WAAW,OAAO,UAAU;AAAA,EAC5B,WAAW,OAAO,OAAO;AAAA,EACzB,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,QAAQ,OAAO,MAAM;AAAA,EACrB,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,aAAa,OAAO,gBAAgB;AAAA,IAClC,UAAU;AAAA,IACV,MAAM;AAAA,EACR,CAAC;AAAA,EACD,iBAAiB,OAAO,aAAa;AAAA,EACrC,kBAAkB,OAAO,gCAAgC;AAAA,EACzD,YAAY,OAAO,SAAS;AAAA,EAC5B,YAAY,OAAO,kCAAkC;AAAA,IACnD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA;AAAA,EACA,sBAAsB;AAAA,EACtB,eAAe,IAAI,QAAQ;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,yBAAyB;AAAA;AAAA,EAEzB;AAAA;AAAA,EAEA,wBAAwB,aAAa;AAAA;AAAA,EAErC,sBAAsB,OAAO,kBAAkB;AAAA,EAC/C,gCAAgC,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7C,sBAAsB;AAAA;AAAA,EAEtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA,uBAAuB,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC,qBAAqB,MAAM;AAIzB,SAAK,sBAAsB,KAAK,aAAa,CAAC,KAAK,UAAU;AAAA,EAC/D;AAAA;AAAA,EAEA,YAAY,MAAM;AAAA,EAAC;AAAA;AAAA,EAEnB,aAAa,MAAM;AAAA,EAAC;AAAA;AAAA,EAEpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB;AAAA,EACA,cAAc;AAAA,EAAC;AAAA;AAAA,EAEf,cAAc;AAAA,EACd,kBAAkB;AAChB,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,SAAS;AAC3B,SAAK,qBAAqB,KAAK,UAAU,OAAO,UAAU,QAAQ,KAAK,kBAAkB;AAAA,EAC3F;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,UAAU,KAAK,KAAK,mBAAmB;AACjD,WAAK,sBAAsB,KAAK,iBAAiB;AACjD,UAAI,KAAK,WAAW;AAClB,aAAK,YAAY,eAAe;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,qBAAqB;AAC1B,SAAK,8BAA8B,YAAY;AAC/C,SAAK,sBAAsB,YAAY;AACvC,SAAK,sBAAsB;AAC3B,SAAK,cAAc;AACnB,SAAK,qBAAqB,SAAS;AACnC,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK,oBAAoB,KAAK,aAAa;AAAA,EACpD;AAAA,EACA,mBAAmB;AAAA;AAAA,EAEnB,YAAY;AACV,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,YAAY;AACjB,QAAI,CAAC,KAAK,kBAAkB;AAC1B;AAAA,IACF;AACA,QAAI,KAAK,WAAW;AAKlB,WAAK,MAAM,IAAI,MAAM;AACnB,aAAK,aAAa,OAAO,KAAK;AAAA,MAChC,CAAC;AAAA,IACH;AAGA,QAAI,KAAK,aAAa,0BAA0B,MAAM;AACpD,WAAK,aAAa,UAAU;AAC5B,WAAK,aAAa,wBAAwB;AAAA,IAC5C;AACA,SAAK,mBAAmB;AACxB,SAAK,6BAA6B;AAClC,QAAI,KAAK,eAAe,KAAK,YAAY,YAAY,GAAG;AACtD,WAAK,YAAY,OAAO;AACxB,WAAK,4BAA4B,YAAY;AAAA,IAC/C;AACA,SAAK,kBAAkB;AAGvB,QAAI,CAAC,KAAK,qBAAqB;AAK7B,WAAK,mBAAmB,cAAc;AAAA,IACxC;AAEA,QAAI,KAAK,eAAe;AACtB,6BAAuB,KAAK,eAAe,aAAa,KAAK,aAAa,EAAE;AAAA,IAC9E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,QAAI,KAAK,kBAAkB;AACzB,WAAK,YAAY,eAAe;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,sBAAsB;AACxB,WAAO,MAAM,KAAK,kBAAkB,KAAK,aAAa,YAAY,OAAO,KAAK,OAAO,MAAM,KAAK,gBAAgB,CAAC,GAAG,KAAK,sBAAsB,KAAK,uBAAuB,GAAG,KAAK,cAAc,KAAK,YAAY,YAAY,EAAE,KAAK,OAAO,MAAM,KAAK,gBAAgB,CAAC,IAAI,GAAG,CAAC,EAAE;AAAA;AAAA,MAElR,IAAI,WAAS,iBAAiB,2BAA2B,QAAQ,IAAI;AAAA,IAAC;AAAA,EACxE;AAAA;AAAA,EAEA,mBAAmB,MAAM,MAAM;AAC7B,UAAM,UAAU,KAAK,eAAe,KAAK,aAAa,UAAU;AAChE,QAAI,SAAS;AACX,aAAO,QAAQ,QAAQ,KAAK,UAAU,OAAO,GAAG,UAAU,MAAM,MAAM,GAAG,QAAQ,IAAI,YAAU,OAAO,iBAAiB,CAAC,CAAC,CAAC;AAAA,IAC5H;AAGA,WAAO,KAAK,aAAa,KAAK,UAAU,MAAM,KAAK,gBAAgB,CAAC;AAAA,EACtE,CAAC;AAAA;AAAA,EAED,IAAI,eAAe;AACjB,QAAI,KAAK,gBAAgB,KAAK,aAAa,aAAa;AACtD,aAAO,KAAK,aAAa,YAAY;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,yBAAyB;AACvB,WAAO,IAAI,WAAW,cAAY;AAChC,YAAM,WAAW,WAAS;AAGxB,cAAM,cAAc,gBAAgB,KAAK;AACzC,cAAM,YAAY,KAAK,aAAa,KAAK,WAAW,0BAA0B,EAAE,gBAAgB;AAChG,cAAM,eAAe,KAAK,cAAc,KAAK,YAAY,WAAW,gBAAgB;AACpF,YAAI,KAAK,oBAAoB,gBAAgB,KAAK,SAAS;AAAA;AAAA;AAAA;AAAA,QAK3D,CAAC,KAAK,UAAU,MAAM,CAAC,aAAa,CAAC,UAAU,SAAS,WAAW,OAAO,CAAC,gBAAgB,CAAC,aAAa,SAAS,WAAW,MAAM,CAAC,CAAC,KAAK,eAAe,CAAC,KAAK,YAAY,eAAe,SAAS,WAAW,GAAG;AAC/M,mBAAS,KAAK,KAAK;AAAA,QACrB;AAAA,MACF;AACA,YAAM,WAAW,CAAC,KAAK,UAAU,OAAO,YAAY,SAAS,QAAQ,GAAG,KAAK,UAAU,OAAO,YAAY,YAAY,QAAQ,GAAG,KAAK,UAAU,OAAO,YAAY,YAAY,QAAQ,CAAC;AACxL,aAAO,MAAM;AACX,iBAAS,QAAQ,aAAW,QAAQ,CAAC;AAAA,MACvC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,WAAW,OAAO;AAChB,YAAQ,QAAQ,IAAI,EAAE,KAAK,MAAM,KAAK,mBAAmB,KAAK,CAAC;AAAA,EACjE;AAAA;AAAA,EAEA,iBAAiB,IAAI;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,iBAAiB,YAAY;AAC3B,SAAK,SAAS,cAAc,WAAW;AAAA,EACzC;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,UAAU,MAAM;AACtB,UAAM,cAAc,eAAe,KAAK;AAKxC,QAAI,YAAY,UAAU,CAAC,aAAa;AACtC,YAAM,eAAe;AAAA,IACvB;AACA,SAAK,sBAAsB,KAAK,SAAS,cAAc;AACvD,QAAI,KAAK,gBAAgB,YAAY,SAAS,KAAK,aAAa,CAAC,aAAa;AAC5E,WAAK,aAAa,sBAAsB;AACxC,WAAK,iBAAiB;AACtB,YAAM,eAAe;AAAA,IACvB,WAAW,KAAK,cAAc;AAC5B,YAAM,iBAAiB,KAAK,aAAa,YAAY;AACrD,YAAM,aAAa,YAAY,YAAY,YAAY;AACvD,UAAI,YAAY,OAAO,cAAc,CAAC,eAAe,KAAK,WAAW;AACnE,aAAK,aAAa,YAAY,UAAU,KAAK;AAAA,MAC/C,WAAW,cAAc,KAAK,SAAS,GAAG;AACxC,aAAK,mBAAmB,KAAK,mBAAmB;AAAA,MAClD;AACA,UAAI,cAAc,KAAK,aAAa,YAAY,eAAe,gBAAgB;AAC7E,aAAK,gBAAgB,KAAK,aAAa,YAAY,mBAAmB,CAAC;AACvE,YAAI,KAAK,aAAa,0BAA0B,KAAK,cAAc;AACjE,cAAI,CAAC,KAAK,4BAA4B;AACpC,iBAAK,4BAA4B,KAAK;AAAA,UACxC;AACA,eAAK,6BAA6B,KAAK;AACvC,eAAK,mBAAmB,KAAK,aAAa,KAAK;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,SAAS,MAAM;AACnB,QAAI,QAAQ,OAAO;AAEnB,QAAI,OAAO,SAAS,UAAU;AAC5B,cAAQ,SAAS,KAAK,OAAO,WAAW,KAAK;AAAA,IAC/C;AAMA,QAAI,KAAK,mBAAmB,OAAO;AACjC,WAAK,iBAAiB;AACtB,WAAK,6BAA6B;AAIlC,UAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,aAAa,kBAAkB;AAC7D,aAAK,UAAU,KAAK;AAAA,MACtB;AACA,UAAI,CAAC,OAAO;AACV,aAAK,6BAA6B,MAAM,KAAK;AAAA,MAC/C,WAAW,KAAK,aAAa,CAAC,KAAK,aAAa,kBAAkB;AAGhE,cAAM,iBAAiB,KAAK,aAAa,SAAS,KAAK,YAAU,OAAO,QAAQ;AAChF,YAAI,gBAAgB;AAClB,gBAAM,UAAU,KAAK,iBAAiB,eAAe,KAAK;AAC1D,cAAI,UAAU,SAAS;AACrB,2BAAe,SAAS,KAAK;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,SAAS,KAAK,KAAK,UAAU,GAAG;AAMvC,cAAM,gBAAgB,KAAK,uBAAuB,KAAK,SAAS,cAAc;AAC9E,aAAK,sBAAsB;AAC3B,aAAK,mBAAmB,aAAa;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe;AACb,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,sBAAsB;AAAA,IAC7B,WAAW,KAAK,SAAS,GAAG;AAC1B,WAAK,iBAAiB,KAAK,SAAS,cAAc;AAClD,WAAK,eAAe,KAAK,cAAc;AACvC,WAAK,YAAY,IAAI;AAAA,IACvB;AAAA,EACF;AAAA,EACA,eAAe;AACb,QAAI,KAAK,SAAS,KAAK,CAAC,KAAK,WAAW;AACtC,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,kCAAkC,MAAM,KAAK,SAAS;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,gBAAgB,OAAO;AACjC,QAAI,KAAK,cAAc,KAAK,WAAW,eAAe,QAAQ;AAC5D,UAAI,eAAe;AACjB,aAAK,WAAW,qBAAqB;AAAA,MACvC,OAAO;AACL,aAAK,WAAW,aAAa;AAAA,MAC/B;AACA,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,wBAAwB;AAC/B,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,aAAa;AAAA,MAC/B;AACA,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,6BAA6B;AAC3B,UAAM,gBAAgB,IAAI,WAAW,gBAAc;AACjD,sBAAgB,MAAM;AACpB,mBAAW,KAAK;AAAA,MAClB,GAAG;AAAA,QACD,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AACD,UAAM,gBAAgB,KAAK,aAAa,SAAS,QAAQ;AAAA,MAAK,IAAI,MAAM,KAAK,kBAAkB,oBAAoB,CAAC;AAAA;AAAA;AAAA,MAGpH,MAAM,CAAC;AAAA,IAAC,KAAK,GAAG;AAEhB,WAAO,MAAM,eAAe,aAAa,EAAE;AAAA;AAAA;AAAA,MAG3C,UAAU,MAAM,KAAK,MAAM,IAAI,MAAM;AAInC,cAAM,UAAU,KAAK;AACrB,aAAK,iBAAiB;AACtB,aAAK,kBAAkB;AACvB,aAAK,mBAAmB,cAAc;AACtC,YAAI,KAAK,WAAW;AAClB,eAAK,YAAY,eAAe;AAAA,QAClC;AACA,YAAI,YAAY,KAAK,WAAW;AAQ9B,cAAI,KAAK,WAAW;AAClB,iBAAK,YAAY;AAAA,UACnB,OAAO;AACL,iBAAK,aAAa,OAAO,KAAK;AAAA,UAChC;AAAA,QACF;AACA,eAAO,KAAK;AAAA,MACd,CAAC,CAAC;AAAA;AAAA,MAEF,KAAK,CAAC;AAAA,IAAC,EAEN,UAAU,WAAS,KAAK,kBAAkB,KAAK,CAAC;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,SAAK,aAAa,OAAO,KAAK;AAAA,EAChC;AAAA;AAAA,EAEA,gBAAgB;AACd,QAAI,KAAK,aAAa;AACpB,WAAK,WAAW;AAChB,WAAK,YAAY,QAAQ;AACzB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,OAAO;AACtB,UAAM,eAAe,KAAK;AAC1B,WAAO,gBAAgB,aAAa,cAAc,aAAa,YAAY,KAAK,IAAI;AAAA,EACtF;AAAA,EACA,mBAAmB,OAAO;AACxB,UAAM,YAAY,KAAK,iBAAiB,KAAK;AAC7C,QAAI,SAAS,MAAM;AACjB,WAAK,6BAA6B,MAAM,KAAK;AAAA,IAC/C;AAGA,SAAK,wBAAwB,aAAa,OAAO,YAAY,EAAE;AAAA,EACjE;AAAA,EACA,wBAAwB,OAAO;AAG7B,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,SAAS,QAAQ;AAAA,IACnC,OAAO;AACL,WAAK,SAAS,cAAc,QAAQ;AAAA,IACtC;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,OAAO;AACvB,UAAM,QAAQ,KAAK;AACnB,UAAM,WAAW,QAAQ,MAAM,SAAS,KAAK;AAC7C,QAAI,UAAU;AACZ,WAAK,6BAA6B,QAAQ;AAC1C,WAAK,mBAAmB,SAAS,KAAK;AAItC,WAAK,UAAU,SAAS,KAAK;AAC7B,YAAM,iBAAiB,QAAQ;AAC/B,WAAK,SAAS,cAAc,MAAM;AAAA,IACpC,WAAW,MAAM,oBAAoB,KAAK,SAAS,cAAc,UAAU,KAAK,gBAAgB;AAC9F,WAAK,6BAA6B,IAAI;AACtC,WAAK,mBAAmB,IAAI;AAC5B,WAAK,UAAU,IAAI;AAAA,IACrB;AACA,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,6BAA6B,MAAM,WAAW;AAG5C,SAAK,cAAc,SAAS,QAAQ,YAAU;AAC5C,UAAI,WAAW,QAAQ,OAAO,UAAU;AACtC,eAAO,SAAS,SAAS;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,gBAAgB,KAAK,SAAS,cAAc,OAAO;AACpE,SAAK,eAAe,aAAa;AACjC,SAAK,YAAY;AAEjB,QAAI,KAAK,eAAe;AACtB,YAAM,UAAU,KAAK,aAAa;AAClC,0BAAoB,KAAK,eAAe,aAAa,OAAO;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,eAAe,eAAe;AAC5B,QAAI,CAAC,KAAK,iBAAiB,OAAO,cAAc,eAAe,YAAY;AACzE,YAAM,oCAAoC;AAAA,IAC5C;AACA,QAAI,aAAa,KAAK;AACtB,QAAI,CAAC,YAAY;AACf,WAAK,UAAU,IAAI,eAAe,KAAK,aAAa,UAAU,KAAK,mBAAmB;AAAA,QACpF,IAAI,KAAK,YAAY,WAAW;AAAA,MAClC,CAAC;AACD,mBAAa,KAAK,SAAS,OAAO,KAAK,kBAAkB,CAAC;AAC1D,WAAK,cAAc;AACnB,WAAK,wBAAwB,KAAK,eAAe,OAAO,EAAE,UAAU,MAAM;AACxE,YAAI,KAAK,aAAa,YAAY;AAChC,qBAAW,WAAW;AAAA,YACpB,OAAO,KAAK,eAAe;AAAA,UAC7B,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAGD,WAAK,gCAAgC,KAAK,oBAAoB,QAAQ,YAAY,gBAAgB,EAAE,UAAU,YAAU;AACtH,cAAM,qBAAqB,OAAO;AAGlC,YAAI,oBAAoB;AACtB,eAAK,kBAAkB,uBAAuB,IAAI,EAAE,kBAAkB,IAAI,EAAE,mBAAmB,CAAC;AAAA,QAClG,OAAO;AACL,eAAK,kBAAkB,uBAAuB,KAAK,EAAE,kBAAkB,KAAK,EAAE,mBAAmB,CAAC;AAAA,QACpG;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AAEL,WAAK,kBAAkB,UAAU,KAAK,qBAAqB,CAAC;AAC5D,iBAAW,WAAW;AAAA,QACpB,OAAO,KAAK,eAAe;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,QAAI,cAAc,CAAC,WAAW,YAAY,GAAG;AAC3C,iBAAW,OAAO,KAAK,OAAO;AAC9B,WAAK,iBAAiB;AACtB,WAAK,sBAAsB;AAC3B,WAAK,8BAA8B,KAAK,2BAA2B;AAAA,IACrE;AACA,UAAM,UAAU,KAAK;AACrB,SAAK,aAAa,UAAU,KAAK,mBAAmB;AACpD,SAAK,aAAa,wBAAwB;AAC1C,SAAK,aAAa,UAAU,KAAK,YAAY,KAAK;AAClD,SAAK,kBAAkB;AACvB,SAAK,0BAA0B;AAG/B,QAAI,KAAK,aAAa,YAAY,KAAK,WAAW;AAChD,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAEA,sBAAsB,WAAS;AAG7B,QAAI,MAAM,YAAY,UAAU,CAAC,eAAe,KAAK,KAAK,MAAM,YAAY,YAAY,eAAe,OAAO,QAAQ,GAAG;AAGvH,UAAI,KAAK,4BAA4B;AACnC,aAAK,wBAAwB,KAAK,6BAA6B,EAAE;AACjE,aAAK,6BAA6B;AAAA,MACpC;AACA,WAAK,qBAAqB,KAAK;AAC/B,WAAK,iBAAiB;AAGtB,YAAM,gBAAgB;AACtB,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,aAAa,eAAe;AAIjC,QAAI,KAAK,WAAW;AAClB,YAAM,aAAa,KAAK;AACxB,UAAI,CAAC,KAAK,sBAAsB;AAG9B,aAAK,uBAAuB,WAAW,cAAc,EAAE,UAAU,KAAK,mBAAmB;AAAA,MAC3F;AACA,UAAI,CAAC,KAAK,2BAA2B;AAInC,aAAK,4BAA4B,WAAW,qBAAqB,EAAE,UAAU;AAAA,MAC/E;AAAA,IACF,OAAO;AACL,WAAK,sBAAsB,YAAY;AACvC,WAAK,2BAA2B,YAAY;AAC5C,WAAK,uBAAuB,KAAK,4BAA4B;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,WAAO,IAAI,cAAc;AAAA,MACvB,kBAAkB,KAAK,oBAAoB;AAAA,MAC3C,gBAAgB,KAAK,gBAAgB;AAAA,MACrC,OAAO,KAAK,eAAe;AAAA,MAC3B,WAAW,KAAK,QAAQ;AAAA,MACxB,aAAa,KAAK,WAAW;AAAA,MAC7B,eAAe,KAAK,WAAW;AAAA,MAC/B,YAAY,KAAK,WAAW;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AAEpB,UAAM,WAAW,KAAK,SAAS,SAAS,EAAE,oBAAoB,KAAK,qBAAqB,CAAC,EAAE,uBAAuB,KAAK,EAAE,SAAS,KAAK;AACvI,SAAK,sBAAsB,QAAQ;AACnC,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,sBAAsB,kBAAkB;AAGtC,UAAM,iBAAiB,CAAC;AAAA,MACtB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,GAAG;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAID,UAAM,aAAa,KAAK;AACxB,UAAM,iBAAiB,CAAC;AAAA,MACtB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV;AAAA,IACF,CAAC;AACD,QAAI;AACJ,QAAI,KAAK,aAAa,SAAS;AAC7B,kBAAY;AAAA,IACd,WAAW,KAAK,aAAa,SAAS;AACpC,kBAAY;AAAA,IACd,OAAO;AACL,kBAAY,CAAC,GAAG,gBAAgB,GAAG,cAAc;AAAA,IACnD;AACA,qBAAiB,cAAc,SAAS;AAAA,EAC1C;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,aAAa;AACpB,aAAO,KAAK,YAAY;AAAA,IAC1B;AACA,WAAO,KAAK,aAAa,KAAK,WAAW,0BAA0B,IAAI,KAAK;AAAA,EAC9E;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,aAAa,cAAc,KAAK,cAAc;AAAA,EAC5D;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK,qBAAqB,EAAE,cAAc,sBAAsB,EAAE;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB;AACjB,UAAM,eAAe,KAAK;AAC1B,QAAI,aAAa,uBAAuB;AAItC,UAAI,0BAA0B;AAC9B,eAAS,QAAQ,GAAG,QAAQ,aAAa,QAAQ,QAAQ,SAAS;AAChE,cAAM,SAAS,aAAa,QAAQ,IAAI,KAAK;AAC7C,YAAI,CAAC,OAAO,UAAU;AACpB,oCAA0B;AAC1B;AAAA,QACF;AAAA,MACF;AACA,mBAAa,YAAY,cAAc,uBAAuB;AAAA,IAChE,OAAO;AACL,mBAAa,YAAY,cAAc,EAAE;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,UAAU,KAAK,SAAS;AAC9B,WAAO,CAAC,QAAQ,YAAY,CAAC,QAAQ,YAAY,CAAC,KAAK;AAAA,EACzD;AAAA;AAAA,EAEA,gBAAgB,OAAO;AAQrB,UAAM,eAAe,KAAK;AAC1B,UAAM,aAAa,8BAA8B,OAAO,aAAa,SAAS,aAAa,YAAY;AACvG,QAAI,UAAU,KAAK,eAAe,GAAG;AAInC,mBAAa,cAAc,CAAC;AAAA,IAC9B,WAAW,aAAa,OAAO;AAC7B,YAAM,SAAS,aAAa,QAAQ,QAAQ,EAAE,KAAK;AACnD,UAAI,QAAQ;AACV,cAAM,UAAU,OAAO,gBAAgB;AACvC,cAAM,oBAAoB,yBAAyB,QAAQ,WAAW,QAAQ,cAAc,aAAa,cAAc,GAAG,aAAa,MAAM,cAAc,YAAY;AACvK,qBAAa,cAAc,iBAAiB;AAAA,MAC9C;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBhB,4BAA4B;AAO1B,UAAM,QAAQ,KAAK,SAAS,cAAc,QAAQ,mDAAmD;AACrG,QAAI,CAAC,OAAO;AAEV;AAAA,IACF;AACA,UAAM,UAAU,KAAK,aAAa;AAClC,QAAI,KAAK,eAAe;AACtB,6BAAuB,KAAK,eAAe,aAAa,OAAO;AAAA,IACjE;AACA,wBAAoB,OAAO,aAAa,OAAO;AAC/C,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,kBAAkB;AAChB,QAAI,KAAK,eAAe;AACtB,YAAM,UAAU,KAAK,aAAa;AAClC,6BAAuB,KAAK,eAAe,aAAa,OAAO;AAC/D,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,mBAAmB,EAAE,GAAG,CAAC,YAAY,mBAAmB,EAAE,CAAC;AAAA,IACjF,WAAW,CAAC,GAAG,8BAA8B;AAAA,IAC7C,UAAU;AAAA,IACV,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,SAAS,oDAAoD;AACpF,iBAAO,IAAI,aAAa;AAAA,QAC1B,CAAC,EAAE,QAAQ,SAAS,iDAAiD;AACnE,iBAAO,IAAI,WAAW;AAAA,QACxB,CAAC,EAAE,SAAS,SAAS,gDAAgD,QAAQ;AAC3E,iBAAO,IAAI,aAAa,MAAM;AAAA,QAChC,CAAC,EAAE,WAAW,SAAS,kDAAkD,QAAQ;AAC/E,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC,EAAE,SAAS,SAAS,kDAAkD;AACrE,iBAAO,IAAI,aAAa;AAAA,QAC1B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,IAAI,qBAAqB,EAAE,QAAQ,IAAI,uBAAuB,OAAO,UAAU,EAAE,qBAAqB,IAAI,uBAAuB,OAAO,MAAM,EAAE,yBAAyB,IAAI,aAAa,IAAI,eAAe,IAAI,aAAa,KAAK,IAAI,EAAE,iBAAiB,IAAI,uBAAuB,OAAO,IAAI,UAAU,SAAS,CAAC,EAAE,iBAAiB,IAAI,wBAAwB,CAAC,IAAI,YAAY,OAAO,IAAI,gBAAgB,OAAO,OAAO,IAAI,aAAa,EAAE,EAAE,iBAAiB,IAAI,uBAAuB,OAAO,SAAS;AAAA,MAC/gB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,cAAc,CAAC,GAAG,mBAAmB,cAAc;AAAA,MACnD,UAAU,CAAC,GAAG,2BAA2B,UAAU;AAAA,MACnD,aAAa,CAAC,GAAG,8BAA8B,aAAa;AAAA,MAC5D,uBAAuB,CAAC,GAAG,gBAAgB,uBAAuB;AAAA,MAClE,sBAAsB,CAAC,GAAG,2BAA2B,wBAAwB,gBAAgB;AAAA,IAC/F;AAAA,IACA,UAAU,CAAC,wBAAwB;AAAA,IACnC,UAAU,CAAI,mBAAmB,CAAC,+BAA+B,CAAC,GAAM,oBAAoB;AAAA,EAC9F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,uBAAuB;AAAA,QACvB,eAAe;AAAA,QACf,4BAA4B;AAAA,QAC5B,gCAAgC;AAAA,QAChC,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA;AAAA;AAAA,QAGxB,aAAa;AAAA,QACb,UAAU;AAAA,QACV,WAAW;AAAA,QACX,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AAAA,MACA,UAAU;AAAA,MACV,WAAW,CAAC,+BAA+B;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,eAAe,iBAAiB,iBAAiB,iBAAiB,wBAAwB,qBAAqB;AAAA,IACzH,SAAS,CAAC,qBAAqB,iBAAiB,iBAAiB,iBAAiB,wBAAwB,qBAAqB;AAAA,EACjI,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,iDAAiD;AAAA,IAC7D,SAAS,CAAC,eAAe,iBAAiB,iBAAiB,qBAAqB,iBAAiB,eAAe;AAAA,EAClH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe,iBAAiB,iBAAiB,iBAAiB,wBAAwB,qBAAqB;AAAA,MACzH,SAAS,CAAC,qBAAqB,iBAAiB,iBAAiB,iBAAiB,wBAAwB,qBAAqB;AAAA,MAC/H,WAAW,CAAC,iDAAiD;AAAA,IAC/D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}