/* Airbnb-themed Forgot Password Styling */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f7f7f7;
    padding: 20px;
}

.login-card {
    background: white;
    padding: 32px;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    width: 100%;
    max-width: 500px;
    border: 1px solid #ebebeb;
}

h1 {
    font-size: 26px;
    font-weight: 600;
    color: #222222;
    margin-bottom: 8px;
    font-family: 'Circular', -apple-system, BlinkMacSystemFont, sans-serif;
}

h2 {
    font-size: 18px;
    font-weight: 400;
    color: #717171;
    margin-bottom: 24px;
    font-family: 'Circular', -apple-system, BlinkMacSystemFont, sans-serif;
}

.instruction {
    color: #717171;
    margin-bottom: 24px;
    font-size: 16px;
    line-height: 1.5;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #222222;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #b0b0b0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s;
    font-family: 'Circular', -apple-system, BlinkMacSystemFont, sans-serif;
}

.form-control:focus {
    outline: none;
    border-color: #222222;
}

.error-message {
    color: #ff385c;
    font-size: 14px;
    margin-top: 8px;
}

.btn-primary {
    width: 100%;
    padding: 14px 24px;
    background-color: #ff385c;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    font-family: 'Circular', -apple-system, BlinkMacSystemFont, sans-serif;
}

.btn-primary:hover {
    background-color: #e61e4d;
}

.btn-primary:disabled {
    background-color: #dddddd;
    cursor: not-allowed;
}

.alert {
    padding: 16px;
    border-radius: 8px;
    margin: 20px 0;
    font-size: 14px;
}

.alert-success {
    background-color: #f0f9f0;
    color: #22863a;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background-color: #fdf2f2;
    color: #cc1f1f;
    border: 1px solid #f5c6cb;
}

.back-to-login {
    text-align: center;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #ebebeb;
}

.btn-link {
    background: none;
    border: none;
    color: #ff385c;
    font-weight: 500;
    cursor: pointer;
    font-size: 14px;
    padding: 8px;
    text-decoration: underline;
    transition: color 0.2s;
}

.btn-link:hover {
    color: #e61e4d;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .login-card {
        padding: 24px;
    }

    h1 {
        font-size: 22px;
    }

    h2 {
        font-size: 16px;
    }
}