import {
  By,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerGesturesPlugin,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  Title,
  VERSION,
  disableDebugTools,
  enableDebugTools,
  provideClientHydration,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache
} from "./chunk-BDZ5MY3I.js";
import {
  BrowserDomAdapter,
  BrowserGetTestability,
  BrowserModule,
  DomEventsPlugin,
  DomRendererFactory2,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  KeyEventsPlugin,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  SharedStylesHost,
  bootstrapApplication,
  createApplication,
  platformBrowser,
  provideProtractorTestingSupport
} from "./chunk-H36WY7UY.js";
import "./chunk-ZV3VSC5G.js";
import {
  getDOM
} from "./chunk-IGXYBYDH.js";
import "./chunk-UDW6LASK.js";
import "./chunk-6RGQPQ3H.js";
import "./chunk-64UGZ4AE.js";
import "./chunk-BXL443VD.js";
import "./chunk-WS3URHHF.js";
import "./chunk-5GCCG324.js";
export {
  BrowserModule,
  By,
  DomSanitizer,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  Title,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache,
  BrowserDomAdapter as ɵBrowserDomAdapter,
  BrowserGetTestability as ɵBrowserGetTestability,
  DomEventsPlugin as ɵDomEventsPlugin,
  DomRendererFactory2 as ɵDomRendererFactory2,
  DomSanitizerImpl as ɵDomSanitizerImpl,
  HammerGesturesPlugin as ɵHammerGesturesPlugin,
  KeyEventsPlugin as ɵKeyEventsPlugin,
  SharedStylesHost as ɵSharedStylesHost,
  getDOM as ɵgetDOM
};
