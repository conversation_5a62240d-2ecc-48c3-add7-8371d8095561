.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: linear-gradient(to bottom, #f8f9fa, #ffffff);
  min-height: 100vh;
}

.header {
  width: 100%;
  margin-bottom: 20px;
}

.filters-bar {
  width: 100%;
  display: flex;
  justify-content: center; /* Center the filter buttons */
  position: sticky;
  top: 80px; /* Stick below the header (adjust based on your header height) */
  z-index: 10;
  background-color: white;
  padding: 16px 0;
  border-bottom: 1px solid #ebebeb;
  margin-bottom: 24px;
  height: 100px; /* Default height */
  transition: height 0.3s ease, padding 0.3s ease; /* Smooth transition for height and padding */
}

.filters-bar.scrolled {
  height: 60px; /* Reduced height when scrolled */
  padding: 8px 0; /* Reduced padding when scrolled */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Optional: Add a shadow for visual feedback */
}

/* Ensure the filter buttons wrapper handles overflow */
.filters-wrapper {
  display: flex;
  max-width: 1200px; /* Constrain the width to match typical content width */
  width: 100%;
  overflow-x: auto; /* Allow horizontal scrolling if there are too many filters */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on mobile */
  padding: 0 16px;
  gap: 16px; /* Space between filter buttons */
}

/* Hide scrollbar for a cleaner look */
.filters-wrapper::-webkit-scrollbar {
  display: none;
}

.filters-wrapper {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Style for filter buttons */
.filter-buttons {
  display: flex;
  align-items: center;
  gap: 16px; /* Space between buttons */
  white-space: nowrap; /* Prevent buttons from wrapping */
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  background: none;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease, border-color 0.2s ease;
}

.filter-button .material-icons {
  font-size: 16px; /* Smaller icons */
}

.filter-button:hover {
  background: #f7f7f7;
}

.filter-button.active {
  border-color: #ff385c;
  background: #fff;
  font-weight: bold;
}

.filter-button.reset-button {
  background-color: #f5f5f5;
  border-color: #ddd;
}

.filter-button.reset-button:hover {
  background-color: #e5e5e5;
  border-color: #ccc;
}

.filter-button.reset-button .material-icons {
  font-size: 14px;
}

.filters-bar.scrolled {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.property-grid {
  margin-top: 30px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding-bottom: 40px;
  position: relative;
}

.property-card {
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  max-width: 100%;
}

.property-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.image-container {
  position: relative;
  width: 100%;
  padding-top: 66.67%; /* 3:2 aspect ratio */
  border-radius: 12px 12px 0 0;
  overflow: hidden;
}

.property-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.property-card:hover .property-image {
  transform: scale(1.08);
}

.arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.95);
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #222;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.2s, transform 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.arrow .material-icons {
  font-size: 16px;
}

.property-card:hover .arrow {
  opacity: 1;
}

.arrow:hover {
  transform: translateY(-50%) scale(1.1);
}

.left-arrow {
  left: 10px;
}

.right-arrow {
  right: 10px;
}

.favorite {
  position: absolute;
  top: 15px;
  right: 15px;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: 2;
}

.favorite .material-icons {
  color: white;
  font-size: 18px;
  text-shadow: 0 0 6px rgba(0, 0, 0, 0.6);
  transition: transform 0.2s, color 0.2s;
}

.favorite .material-icons:hover {
  transform: scale(1.2);
}

.favorite .material-icons.favorited {
  color: #ff385c;
}

.image-dots {
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 5px;
  z-index: 2;
}

.image-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
  opacity: 0.6;
  transition: opacity 0.2s, transform 0.2s;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.image-dots span.active {
  opacity: 1;
  transform: scale(1.3);
}

.property-info {
  padding: 14px 12px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
  background-color: #ffffff;
  border-radius: 0 0 12px 12px;
}

.property-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.property-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #222;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.property-location-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
}

.property-location {
  font-size: 14px;
  font-weight: 600;
  color: #222222;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.property-rating {
  display: flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
  font-size: 13px;
}

.property-rating .material-icons {
  font-size: 16px;
  color: #FF385C;
}

.new-tag {
  font-weight: 600;
  font-size: 12px;
  margin-left: 6px;
  color: #222;
}

.property-distance {
  color: #717171;
  font-size: 13px;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.property-dates {
  color: #717171;
  font-size: 14px;
  margin-bottom: 0;
}

.property-price {
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.price {
  font-weight: 600;
  font-size: 14px;
  color: #222222;
}

.per-night {
  color: #717171;
  font-size: 13px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #717171;
}

.spinner {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 4px solid rgba(255, 56, 92, 0.1);
  border-top-color: #FF385C;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 100px 0;
  color: #717171;
}

.empty-state .material-icons {
  font-size: 64px;
  margin-bottom: 16px;
  color: #FF385C;
}

.empty-state h3 {
  margin-bottom: 8px;
  font-size: 24px;
  color: #222222;
}

@media (min-width: 576px) {
  .property-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .property-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    
  }
}

@media (min-width: 992px) {
  .property-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }
  
}

.upload-images {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.upload-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.upload-dialog-content {
  background: white;
  padding: 24px;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.upload-dialog-content h2 {
  margin: 0 0 20px;
  color: #222;
}

/* Success snackbar */
::ng-deep .success-snackbar {
  background: #4caf50;
  color: white;
}

/* Error snackbar */
::ng-deep .error-snackbar {
  background: #f44336;
  color: white;
}

.fab {
  position: fixed;
  bottom: 80px; /* Above the sticky footer (60px height + padding) */
  right: 1rem;
  background-color: #222;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0 1rem;
  border-radius: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.fab mat-icon {
  font-size: 1.2rem;
  width: 1.2rem;
  height: 1.2rem;
}

.no-properties {
  text-align: center;
  padding: 2rem;
  color: #666;
}

/* Media Queries for Desktop View */
@media (min-width: 769px) {
  .fab {
    bottom: 1rem; /* No sticky footer in desktop view */
  }
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'liga';
}

.badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background-color: #FF385C;
  color: white;
  padding: 5px 10px;
  border-radius: 30px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  letter-spacing: 0.5px;
}

/* Styling for the background pattern */
body {
  background-color: #f8f9fa;
  background-image: 
    radial-gradient(circle at 25px 25px, #f0f0f0 2%, transparent 0%), 
    radial-gradient(circle at 75px 75px, #f0f0f0 2%, transparent 0%);
  background-size: 100px 100px;
}

/* Add a subtle hover animation to the cards */
@keyframes cardHover {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-3px);
  }
}

.property-card:hover {
  animation: cardHover 0.3s forwards;
}

/* Container queries for better responsive behavior */
@media (max-width: 576px) {
  .container {
    padding: 12px;
  }
  
  .property-grid {
    gap: 16px;
  }
}

.sticky-nav {
  transition: transform 0.3s ease;
  transform: translateY(0);
}

/* Pagination Styles */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  padding: 24px 0;
  border-top: 1px solid #ebebeb;
  margin-top: 24px;
}

.pagination-button {
  background: #ffffff;
  border: 1px solid #ebebeb;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #222;
  transition: background 0.2s, transform 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pagination-button:hover {
  background: #f8f9fa;
  transform: scale(1.1);
}

.pagination-button:disabled {
  background: #f8f9fa;
  color: #717171;
  cursor: not-allowed;
  transform: none;
}

.pagination-button .material-icons {
  font-size: 16px;
}

.pagination-numbers {
  display: flex;
  gap: 8px;
}

.pagination-number {
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #717171;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background 0.2s, color 0.2s;
}

.pagination-number:hover {
  background: #f8f9fa;
  color: #222;
}

.pagination-number.active {
  background: #FF385C;
  color: #ffffff;
  font-weight: 600;
}

/* Price filter dialog */
.price-filter-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.price-filter-content {
  background-color: white;
  border-radius: 12px;
  padding: 24px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.price-filter-content h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #222;
}

.price-inputs {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.price-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.price-input-group label {
  font-size: 14px;
  color: #717171;
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-symbol {
  position: absolute;
  left: 12px;
  color: #717171;
}

.price-input-group input {
  width: 100%;
  padding: 12px 12px 12px 24px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}

.price-filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: none;
  cursor: pointer;
  font-size: 14px;
}

.apply-button {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  background-color: #FF385C;
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.apply-button:hover {
  background-color: #E31C5F;
}

.cancel-button:hover {
  background-color: #f5f5f5;
}