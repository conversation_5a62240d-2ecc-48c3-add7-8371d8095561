:host {
    display: block;
    min-height: 100vh;
    background-color: #f7f7f7;
}

.header {
    background-color: white;
    padding: 1rem 2rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 0;
    z-index: 1000;
    height: 75px;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.header-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    font-weight: 600;
    color: #ff385c;
    font-size: 1.5rem;
}

.airbnb-icon {
    width: 30px;
    height: 30px;
    margin-right: 8px;
}

.progress-bar {
    height: 4px;
    background-color: #e4e4e4;
    width: 100%;
    margin-top: .75rem;
    margin-bottom: 6rem;
}

.progress {
    height: 100%;
    background-color: #ff5a5f;
    transition: width 0.3s ease;
}

.right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.question-button, .exit-button {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s;
}

.question-button {
    background-color: white;
    border: 1px solid #dddddd;
    color: #222222;
}

.exit-button {
    background-color: white;
    border: 1px solid #dddddd;
    color: #222222;
}

.question-button:hover, .exit-button:hover {
    background-color: #f7f7f7;
}

.main-content {
    max-width: 1300px;

    width: 100%;
    margin: 3rem auto;
    padding: 0 2rem;
    position: relative;
    height: 50px;
    max-height: 50px;
    min-height: calc(100vh - 80px); /* Adjust based on your header height */
}

.content-section {
    background-color: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    margin: .5rem auto;
    max-width: 800px;
}

.step-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #222222;
}

.step-subtitle {
    font-size: 1rem;
    color: #717171;
    margin-bottom: 3rem;
}

.property-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.2rem;
}

.property-type-option {
    border: 1px solid #dddddd;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    background-color: white;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.category-icon {
    font-size: 2rem;
    height: 2rem;
    width: 2rem;
    color: #222222;
}

.category-name {
    font-size: 1rem;
    font-weight: 600;
    color: #222222;
}

.category-description {
    font-size: 0.875rem;
    color: #717171;
    font-weight: normal;
}

.property-type-option:hover {
    border-color: #222222;
    transform: translateY(-2px);
}

.property-type-option.selected {
    border-color: #ff385c;
    background-color: #fff8f8;
}

.property-type-option.selected .category-icon {
    color: #ff385c;
}

.form-fields {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
    padding: 20px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #222;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-control:focus {
    border-color: #FF5A5F;
    outline: none;
}

.basics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
}

.counter {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.counter-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 1px solid #dddddd;
    background-color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
}

.counter-btn:hover {
    border-color: #222222;
}

.amenities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

.amenity-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.amenity-option:hover {
    border-color: #FF5A5F;
    transform: translateY(-2px);
}

.amenity-option.selected {
    border-color: #FF5A5F;
    background-color: #fff5f5;
}

.amenity-icon {
    font-size: 24px;
    width:35px ;
    height: 35px;
    margin-bottom: 0.5rem;
    color: #FF5A5F;
}
.amenity-icon img{
    width: 35px;
    height: 35px;
    object-fit: cover;
    border-radius: 50%;
}

.amenity-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
    text-align: center;
}

.amenity-description {
    font-size: 0.875rem;
    color: #666;
    text-align: center;
}

.image-upload-container {
    margin-top: 2rem;
}

.upload-area {
    border: 2px dashed #dddddd;
    border-radius: 8px;
    padding: 3rem;
    text-align: center;
    margin-bottom: 2rem;
    transition: all 0.2s;
}

.upload-area:hover {
    border-color: #ff385c;
}

.upload-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: #ff385c;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-weight: 500;
}

.upload-btn:hover {
    background-color: #e31c5f;
}

.image-preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.image-preview {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    height: 150px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.remove-btn:hover {
    background-color: rgba(0, 0, 0, 0.7);
}

.footer {
    background-color: white;
    padding: 1.5rem 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.08);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
}

.footer-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    height: 20px;
}

.previous-button, .next-button, .submit-button {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.previous-button {
    background-color: white;
    border: 1px solid #dddddd;
    color: #222222;
}

.next-button, .submit-button {
    background-color: #ff385c;
    border: 1px solid #ff385c;
    color: white;
}

.previous-button:hover {
    background-color: #f7f7f7;
}

.next-button:hover, .submit-button:hover {
    background-color: #e31c5f;
    border-color: #e31c5f;
}

.new-listing {
    text-align: center;
    padding: 2rem;
}

.new-listing-btn {
    display: block;
    width: 100%;
    max-width: 500px;
    margin: 2rem auto;
    padding: 1rem;
    background-color: white;
    border: 1px solid #dddddd;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.new-listing-btn:hover {
    border-color: #ff385c;
    color: #ff385c;
}

.instruction {
    text-align: center;
    padding: 2rem;
}

.instruction-list {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
    text-align: left;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.instruction-list li {
    padding: 0.5rem 0;
    font-size: 1.1rem;
    color: #222222;
}

.start-button {
    display: block;
    width: 100%;
    max-width: 500px;
    margin: 2rem auto;
    padding: 1rem;
    background-color: #ff385c;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.start-button:hover {
    background-color: #e31c5f;
}

.error-message {
    background-color: #fff8f8;
    color: #ff385c;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    border: 1px solid #ff385c;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .header-container, .footer-container {
        padding: 0 1rem;
    }
    
    .main-content {
        padding: 0 1rem;
    }
    
    .property-type-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .step-title {
        font-size: 1.5rem;
    }
    
    .content-section {
        padding: 1.5rem;
    }
}

/* Add margin to the bottom of the form to prevent content from being hidden behind the fixed footer */
form {
    margin-bottom: 100px;
}

.location-coordinates {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-control[readonly] {
    background-color: #f8f9fa;
    cursor: not-allowed;
    opacity: 0.8;
}

.sharing-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.sharing-type-option {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 1.5rem;
    border: 1px solid #ddd;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    text-align: left;
}

.form-step{
    /* justify-content: center; */
    align-items: center;
    display: grid;
  

}

.sharing-type-option:hover {
    border-color: #000;
}

.sharing-type-option.selected {
    border-color: #000;
    background-color: #f7f7f7;
}

.sharing-type-option .type-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0.5rem 0;
}

.sharing-type-option .type-description {
    font-size: 0.9rem;
    color: #717171;
    margin-top: 0.25rem;
}

.mt-4 {
    margin-top: 2rem;
}

.map-container {
    height: 300px;
    width: 100%;
    max-width: 600px;
    margin: 0px auto;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.map{
    height: 100%;
}
.progress-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto 2rem;
    padding: 0 1rem;
}

.progress-bar {
    height: 4px;
    background-color: #e4e4e4;
    border-radius: 2px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: #ff385c;
    transition: width 0.3s ease;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    color: #717171;
    font-size: 0.875rem;
}

.step-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.step-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #f7f7f7;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.step-number.active {
    background-color: #ff385c;
    color: white;
}

.step-number.completed {
    background-color: #00a699;
    color: white;
}


