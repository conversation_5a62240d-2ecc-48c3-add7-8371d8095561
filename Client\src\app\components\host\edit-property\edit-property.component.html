<!-- Add this at the top of your template -->
<div class="toast-container position-fixed top-0 end-0 p-3">
  <ngb-toast
    *ngIf="showToast"
    [class]="'bg-' + toastType"
    [autohide]="true"
    [delay]="3000"
    (hidden)="showToast = false">
    <div class="text-white">{{ toastMessage }}</div>
  </ngb-toast>
</div>

<!-- Header -->
<div class="header-container">
  <div class="container">
    <div class="header-content">
      <div class="header-title">
        <button class="btn btn-link text-dark p-0" (click)="cancel()">
          <i class="material-icons">arrow_back</i>
        </button>
        <h1 class="h3 mb-0">Edit listing</h1>
      </div>
      <div class="header-actions">
        <button class="btn btn-outline-secondary" (click)="cancel()">
          <i class="material-icons">close</i>
          Cancel
        </button>
        <button class="btn btn-primary" 
                (click)="onSubmit()" 
                [disabled]="propertyForm.invalid || saving"
                style="background-color: #ff5a5f !important; border-color: #ff5a5f !important; color: white !important; display: flex; align-items: center; gap: 8px; padding: 10px 20px; border-radius: 4px; font-weight: 500; opacity: 1 !important; visibility: visible !important;">
          <span *ngIf="!saving">
            <i class="material-icons">save</i>
            Save changes
          </span>
          <span *ngIf="saving" class="d-flex align-items-center gap-2">
            <div class="spinner-border spinner-border-sm"></div>
            Saving...
          </span>
        </button>
        <button class="dropdown-item text-danger" (click)="deleteProperty(property.id)">
          <span class="material-icons me-2">delete</span>Delete
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="container-fluid py-4">
  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="alert alert-danger" role="alert">
    <i class="material-icons">error_outline</i>
    {{ error }}
  </div>

  <!-- Edit Form -->
  <form [formGroup]="propertyForm" (ngSubmit)="onSubmit()" *ngIf="!loading">
    <div class="row">
      <!-- Left Navigation -->
      <div class="col-md-3">
        <div class="card">
          <div class="card-body p-0">
            <div class="list-group list-group-flush">
              <a *ngFor="let section of sections" 
                 class="list-group-item list-group-item-action" 
                 [class.active]="activeSection === section.id"
                 (click)="setActiveSection(section.id)">
                <i class="material-icons">{{ getSectionIcon(section.id) }}</i>
                {{ section.label }}
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Content -->
      <div class="col-md-9">
        <!-- Basic Info Section -->
        <div class="card mb-4" *ngIf="activeSection === 'basic'">
          <div class="card-body">
            <h5 class="card-title">
              <i class="material-icons">info</i>
              Basic Information
            </h5>
            
            <div class="row g-3">
              <!-- Title -->
              <div class="col-12">
                <label class="form-label">
                  <i class="material-icons">title</i>
                  Title
                </label>
                <input type="text" 
                       class="form-control" 
                       formControlName="title"
                       [class.is-invalid]="propertyForm.get('title')?.invalid && propertyForm.get('title')?.touched">
                <div class="invalid-feedback" *ngIf="propertyForm.get('title')?.errors?.['required']">
                  Title is required
                </div>
                <div class="invalid-feedback" *ngIf="propertyForm.get('title')?.errors?.['minlength']">
                  Title must be at least 10 characters
                </div>
              </div>

              <!-- Description -->
              <div class="col-12">
                <label class="form-label">
                  <i class="material-icons">description</i>
                  Description
                </label>
                <textarea class="form-control" 
                          rows="4" 
                          formControlName="description"
                          [class.is-invalid]="propertyForm.get('description')?.invalid && propertyForm.get('description')?.touched">
                </textarea>
                <div class="invalid-feedback" *ngIf="propertyForm.get('description')?.errors?.['required']">
                  Description is required
                </div>
                <div class="invalid-feedback" *ngIf="propertyForm.get('description')?.errors?.['minlength']">
                  Description must be at least 50 characters
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Location Section -->
        <div class="card mb-4" *ngIf="activeSection === 'location'">
          <div class="card-body">
            <h5 class="card-title">
              <i class="material-icons">location_on</i>
              Location
            </h5>
            
            <div class="row g-3">
              <!-- Address -->
              <div class="col-12">
                <label class="form-label">
                  <i class="material-icons">home</i>
                  Address
                </label>
                <input type="text" class="form-control" formControlName="address">
              </div>

              <!-- City -->
              <div class="col-md-6">
                <label class="form-label">
                  <i class="material-icons">location_city</i>
                  City
                </label>
                <input type="text" class="form-control" formControlName="city">
              </div>

              <!-- Country -->
              <div class="col-md-6">
                <label class="form-label">
                  <i class="material-icons">public</i>
                  Country
                </label>
                <input type="text" class="form-control" formControlName="country">
              </div>

              <!-- Postal Code -->
              <div class="col-md-6">
                <label class="form-label">
                  <i class="material-icons">markunread_mailbox</i>
                  Postal Code
                </label>
                <input type="text" class="form-control" formControlName="postalCode">
              </div>
            </div>
          </div>
        </div>

        <!-- Pricing Section -->
        <div class="card mb-4" *ngIf="activeSection === 'pricing'">
          <div class="card-body">
            <h5 class="card-title">
              <i class="material-icons">attach_money</i>
              Pricing
            </h5>
            
            <div class="row g-3">
              <!-- Price per night -->
              <div class="col-md-4">
                <label class="form-label">
                  <i class="material-icons">payments</i>
                  Price per night
                </label>
                <div class="input-group">
                  <span class="input-group-text">$</span>
                  <input type="number" class="form-control" formControlName="pricePerNight">
                </div>
              </div>

              <!-- Cleaning fee -->
              <div class="col-md-4">
                <label class="form-label">
                  <i class="material-icons">cleaning_services</i>
                  Cleaning fee
                </label>
                <div class="input-group">
                  <span class="input-group-text">$</span>
                  <input type="number" class="form-control" formControlName="cleaningFee">
                </div>
              </div>

              <!-- Service fee -->
              <div class="col-md-4">
                <label class="form-label">
                  <i class="material-icons">support_agent</i>
                  Service fee
                </label>
                <div class="input-group">
                  <span class="input-group-text">$</span>
                  <input type="number" class="form-control" formControlName="serviceFee">
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Property Details Section -->
        <div class="card mb-4" *ngIf="activeSection === 'details'">
          <div class="card-body">
            <h5 class="card-title">
              <i class="material-icons">apartment</i>
              Property Details
            </h5>
            
            <div class="row g-3">
              <!-- Bedrooms -->
              <div class="col-md-3">
                <label class="form-label">
                  <i class="material-icons">bed</i>
                  Bedrooms
                </label>
                <input type="number" class="form-control" formControlName="bedrooms">
              </div>

              <!-- Bathrooms -->
              <div class="col-md-3">
                <label class="form-label">
                  <i class="material-icons">bathroom</i>
                  Bathrooms
                </label>
                <input type="number" class="form-control" formControlName="bathrooms">
              </div>

              <!-- Max Guests -->
              <div class="col-md-3">
                <label class="form-label">
                  <i class="material-icons">group</i>
                  Max Guests
                </label>
                <input type="number" class="form-control" formControlName="maxGuests">
              </div>

              <!-- Min Nights -->
              <div class="col-md-3">
                <label class="form-label">
                  <i class="material-icons">calendar_today</i>
                  Minimum Nights
                </label>
                <input type="number" class="form-control" formControlName="minNights">
              </div>

              <!-- Max Nights -->
              <div class="col-md-3">
                <label class="form-label">
                  <i class="material-icons">event</i>
                  Maximum Nights
                </label>
                <input type="number" class="form-control" formControlName="maxNights">
              </div>

              <!-- Instant Book -->
              <div class="mb-3">
                <div class="form-check">
                  <input  type="checkbox" id="instantBook">
                  <label class="form-check-label" for="instantBook">
                    <i class="material-icons">bolt</i>
                    Allow instant booking
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Amenities Section -->
        <div class="card mb-4" *ngIf="activeSection === 'amenities'">
          <div class="card-body">
            <h5 class="card-title">
              <i class="material-icons">spa</i>
              Amenities
            </h5>

            <!-- Property Amenities -->
            <div class="mb-4">
              <h6 class="mb-3">
                Selected Amenities
              </h6>
              <div class="amenities-grid" *ngIf="propertyAmenities.length > 0">
                <div *ngFor="let amenity of propertyAmenities" 
                     class="amenity-item selected"
                     (click)="toggleAmenity(amenity.id, $event)">
                  <div class="form-check w-100">
                    <input class="form-check-input" 
                           type="checkbox" 
                           [id]="'selected-amenity-' + amenity.id"
                           [checked]="true" 
                           (click)="$event.stopPropagation()">
                    <label class="form-check-label" [for]="'selected-amenity-' + amenity.id">
                      {{ amenity.name }}
                    </label>
                  </div>
                </div>
              </div>
              <div *ngIf="propertyAmenities.length === 0" class="text-muted p-3 bg-light rounded">
                No amenities selected for this property
              </div>
            </div>

            <!-- Available Amenities -->
            <div>
              <h6 class="mb-3">
                Available Amenities
              </h6>
              <div class="amenities-grid">
                <div *ngFor="let amenity of allAmenities" 
                     class="amenity-item"
                     [class.selected]="isAmenitySelected(amenity.id)"
                     [class.d-none]="isAmenitySelected(amenity.id)"
                     (click)="toggleAmenity(amenity.id, $event)">
                  <div class="form-check w-100">
                    <input class="form-check-input" 
                           type="checkbox" 
                           [id]="'amenity-' + amenity.id"
                           [checked]="isAmenitySelected(amenity.id)" 
                           (click)="$event.stopPropagation()">
                    <label class="form-check-label" [for]="'amenity-' + amenity.id">
                      {{ amenity.name }}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Images Section -->
        <div class="card mb-4" *ngIf="activeSection === 'images'">
          <div class="card-body">
            <h5 class="card-title">
              <i class="material-icons">photo_library</i>
              Photos
            </h5>
            
            <!-- Image Upload Section -->
            <div class="image-upload-section">
              <h3>Property Images</h3>
              
              <!-- Error Display -->
              <div class="error-message" *ngIf="error">
                <i class="material-icons">error_outline</i>
                {{ error }}
              </div>

              <!-- Existing Images -->
              <div class="existing-images" *ngIf="propertyImages.length > 0">
                <h4>Current Images</h4>
                <div class="image-grid">
                  <div class="image-container" *ngFor="let image of propertyImages; let i = index">
                    <img [src]="image.imageUrl" [alt]="'Property image ' + (i + 1)" class="property-image">
                    <div class="image-overlay">
                      <button type="button" class="remove-button" (click)="removeImage(i)">
                        <i class="material-icons">delete</i>
                        Remove
                      </button>
                      <span class="primary-badge" *ngIf="image.isPrimary">
                        <i class="material-icons">star</i>
                        Primary
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Upload New Images -->
              <div class="upload-section">
                <h4>Upload New Images</h4>
                <div class="file-input-container">
                  <input 
                    type="file" 
                    multiple 
                    accept="image/*" 
                    (change)="onFileUpload($event)"
                    #fileInput
                    class="file-input">
                  <div class="upload-placeholder">
                    <i class="material-icons">cloud_upload</i>
                    <span>Click to upload images</span>
                  </div>
                </div>
                
                <!-- Image Preview -->
                <div class="preview-images" *ngIf="previewUrls.length > 0">
                  <h4>New Images Preview</h4>
                  <div class="image-grid">
                    <div class="image-container" *ngFor="let url of previewUrls; let i = index">
                      <img [src]="url" [alt]="'Preview image ' + (i + 1)" class="preview-image">
                      <div class="image-overlay">
                        <button type="button" class="remove-button" (click)="removeUploadedFile(i)">
                          <i class="material-icons">delete</i>
                          Remove
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<!-- Delete confirmation modal -->
<ng-template #deleteModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Confirm Delete</h5>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body">
    <p>Are you sure you want to delete this property? This action cannot be undone.</p>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Cancel</button>
    <button type="button" class="btn btn-danger" (click)="confirmDelete()">Delete Property</button>
  </div>
</ng-template>

