<div class="notification-container" *ngIf="loggedIn">
    <div class="notification-bell" (click)="toggleNotifications($event)">
        <i class="fa fa-bell"></i>
        <span class="notification-badge" *ngIf="notificationCount > 0">{{notificationCount}}</span>
    </div>
    <!-- Notification Dropdown -->
    <div class="notification-dropdown" *ngIf="isNotificationOpen">
        <div class="notification-header">
            <h3>Notifications</h3>
            <span *ngIf="notifications.length > 0 && notificationCount > 0" (click)="markAllAsRead()"
                class="mark-read">Mark all as read</span>
        </div>
        <div class="notification-list" *ngIf="notifications.length > 0">
            <div class="notification-item" *ngFor="let notification of notifications"
                [class.unread]="!notification.isRead" (click)="markAsRead(notification.id)">
                <div class="notification-content">
                    <p>{{notification.message}}</p>
                    <span class="notification-time">{{formatRelativeTime(notification.createdAt)}}</span>
                    <span class="notification-sender" *ngIf="notification.senderName">From:
                        {{notification.senderName}}</span>
                </div>
            </div>
        </div>
        <div class="empty-notifications" *ngIf="notifications.length === 0">
            <p>No notifications</p>
        </div>
    </div>
</div>