using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using API.Data;
using API.Models;

namespace API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ExperienceController : ControllerBase
    {
        private readonly AppDbContext _context;

        public ExperienceController(AppDbContext context)
        {
            _context = context;
        }

        // GET: api/Experience
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Experience>>> GetExperiences(
            ExperienceCategory? category = null,
            string location = null,
            decimal? minPrice = null,
            decimal? maxPrice = null,
            DateTime? date = null,
            int page = 1,
            int pageSize = 20)
        {
            var query = _context.Experiences.AsQueryable();

            if (category.HasValue)
                query = query.Where(e => e.Category == category.Value);

            if (!string.IsNullOrEmpty(location))
                query = query.Where(e => e.MeetingPoint.Contains(location));

            if (minPrice.HasValue)
                query = query.Where(e => e.Price >= minPrice.Value);

            if (maxPrice.HasValue)
                query = query.Where(e => e.Price <= maxPrice.Value);

            if (date.HasValue)
            {
                query = query.Where(e => e.Availabilities.Any(a => a.Date.Date == date.Value.Date && a.IsActive));
            }

            var experiences = await query
                .Where(e => e.IsActive)
                .Include(e => e.Provider)
                .Include(e => e.Images.Where(i => i.IsPrimary))
                .OrderByDescending(e => e.Rating)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return Ok(experiences);
        }

        // GET: api/Experience/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<Experience>> GetExperience(int id)
        {
            var experience = await _context.Experiences
                .Include(e => e.Provider)
                .Include(e => e.Images)
                .Include(e => e.Availabilities.Where(a => a.Date >= DateTime.Today && a.IsActive))
                .Include(e => e.Reviews.Take(10))
                    .ThenInclude(r => r.User)
                .Include(e => e.Tags)
                .FirstOrDefaultAsync(e => e.Id == id);

            if (experience == null)
            {
                return NotFound();
            }

            return Ok(experience);
        }

        // POST: api/Experience
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<Experience>> CreateExperience(Experience experience)
        {
            // Verify provider exists
            var provider = await _context.ExperienceProviders.FindAsync(experience.ProviderId);
            if (provider == null)
            {
                return BadRequest("Provider not found");
            }

            experience.CreatedAt = DateTime.UtcNow;
            experience.UpdatedAt = DateTime.UtcNow;

            _context.Experiences.Add(experience);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetExperience), new { id = experience.Id }, experience);
        }

        // PUT: api/Experience/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> UpdateExperience(int id, Experience experience)
        {
            if (id != experience.Id)
            {
                return BadRequest();
            }

            var existingExperience = await _context.Experiences.FindAsync(id);
            if (existingExperience == null)
            {
                return NotFound();
            }

            existingExperience.Title = experience.Title;
            existingExperience.Description = experience.Description;
            existingExperience.ShortDescription = experience.ShortDescription;
            existingExperience.Category = experience.Category;
            existingExperience.Price = experience.Price;
            existingExperience.Duration = experience.Duration;
            existingExperience.MaxParticipants = experience.MaxParticipants;
            existingExperience.MinParticipants = experience.MinParticipants;
            existingExperience.MinAge = experience.MinAge;
            existingExperience.MaxAge = experience.MaxAge;
            existingExperience.MeetingPoint = experience.MeetingPoint;
            existingExperience.Latitude = experience.Latitude;
            existingExperience.Longitude = experience.Longitude;
            existingExperience.Inclusions = experience.Inclusions;
            existingExperience.Exclusions = experience.Exclusions;
            existingExperience.Requirements = experience.Requirements;
            existingExperience.CancellationPolicy = experience.CancellationPolicy;
            existingExperience.IsActive = experience.IsActive;
            existingExperience.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return NoContent();
        }

        // GET: api/Experience/{id}/availability
        [HttpGet("{id}/availability")]
        public async Task<ActionResult<IEnumerable<ExperienceAvailability>>> GetExperienceAvailability(int id, DateTime? startDate = null, DateTime? endDate = null)
        {
            startDate ??= DateTime.Today;
            endDate ??= DateTime.Today.AddDays(30);

            var availability = await _context.ExperienceAvailabilities
                .Where(ea => ea.ExperienceId == id && ea.Date >= startDate && ea.Date <= endDate && ea.IsActive)
                .OrderBy(ea => ea.Date)
                .ThenBy(ea => ea.StartTime)
                .ToListAsync();

            return Ok(availability);
        }

        // POST: api/Experience/{id}/availability
        [HttpPost("{id}/availability")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ExperienceAvailability>> AddExperienceAvailability(int id, ExperienceAvailability availability)
        {
            var experience = await _context.Experiences.FindAsync(id);
            if (experience == null)
            {
                return NotFound("Experience not found");
            }

            availability.ExperienceId = id;
            availability.CreatedAt = DateTime.UtcNow;

            _context.ExperienceAvailabilities.Add(availability);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetExperienceAvailability), new { id = availability.Id }, availability);
        }

        // POST: api/Experience/book
        [HttpPost("book")]
        [Authorize]
        public async Task<ActionResult<ExperienceBooking>> BookExperience(ExperienceBooking booking)
        {
            // Verify availability
            var availability = await _context.ExperienceAvailabilities
                .Include(ea => ea.Experience)
                .FirstOrDefaultAsync(ea => ea.Id == booking.ExperienceAvailabilityId);

            if (availability == null)
            {
                return BadRequest("Availability not found");
            }

            if (availability.AvailableSpots < booking.Participants)
            {
                return BadRequest("Not enough spots available");
            }

            // Calculate total price
            booking.TotalPrice = availability.Price * booking.Participants;
            booking.BookingDate = DateTime.UtcNow;

            // Update available spots
            availability.BookedSpots += booking.Participants;

            _context.ExperienceBookings.Add(booking);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetExperienceBooking), new { id = booking.Id }, booking);
        }

        // GET: api/Experience/bookings/{id}
        [HttpGet("bookings/{id}")]
        [Authorize]
        public async Task<ActionResult<ExperienceBooking>> GetExperienceBooking(int id)
        {
            var booking = await _context.ExperienceBookings
                .Include(eb => eb.Experience)
                    .ThenInclude(e => e.Provider)
                .Include(eb => eb.ExperienceAvailability)
                .Include(eb => eb.User)
                .Include(eb => eb.Participants_Navigation)
                .FirstOrDefaultAsync(eb => eb.Id == id);

            if (booking == null)
            {
                return NotFound();
            }

            return Ok(booking);
        }

        // GET: api/Experience/my-bookings
        [HttpGet("my-bookings")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<ExperienceBooking>>> GetMyExperienceBookings()
        {
            var userId = int.Parse(User.FindFirst("userId")?.Value ?? "0");

            var bookings = await _context.ExperienceBookings
                .Where(eb => eb.UserId == userId)
                .Include(eb => eb.Experience)
                    .ThenInclude(e => e.Provider)
                .Include(eb => eb.ExperienceAvailability)
                .OrderByDescending(eb => eb.BookingDate)
                .ToListAsync();

            return Ok(bookings);
        }

        // PUT: api/Experience/bookings/{id}/cancel
        [HttpPut("bookings/{id}/cancel")]
        [Authorize]
        public async Task<IActionResult> CancelExperienceBooking(int id, [FromBody] string cancellationReason = "")
        {
            var booking = await _context.ExperienceBookings
                .Include(eb => eb.ExperienceAvailability)
                .FirstOrDefaultAsync(eb => eb.Id == id);

            if (booking == null)
            {
                return NotFound();
            }

            var userId = int.Parse(User.FindFirst("userId")?.Value ?? "0");
            if (booking.UserId != userId && !User.IsInRole("Admin"))
            {
                return Forbid();
            }

            booking.Status = BookingStatus_Experience.Cancelled;
            booking.CancelledAt = DateTime.UtcNow;
            booking.CancellationReason = cancellationReason;

            // Return spots to availability
            booking.ExperienceAvailability.BookedSpots -= booking.Participants;

            await _context.SaveChangesAsync();
            return NoContent();
        }

        // POST: api/Experience/{id}/review
        [HttpPost("{id}/review")]
        [Authorize]
        public async Task<ActionResult<ExperienceReview>> AddExperienceReview(int id, ExperienceReview review)
        {
            var userId = int.Parse(User.FindFirst("userId")?.Value ?? "0");

            // Verify user has booked this experience
            var hasBooking = await _context.ExperienceBookings
                .AnyAsync(eb => eb.ExperienceId == id && eb.UserId == userId && eb.Status == BookingStatus_Experience.Completed);

            if (!hasBooking)
            {
                return BadRequest("You can only review experiences you have completed");
            }

            // Check if user already reviewed this experience
            var existingReview = await _context.ExperienceReviews
                .FirstOrDefaultAsync(er => er.ExperienceId == id && er.UserId == userId);

            if (existingReview != null)
            {
                return BadRequest("You have already reviewed this experience");
            }

            review.ExperienceId = id;
            review.UserId = userId;
            review.CreatedAt = DateTime.UtcNow;

            _context.ExperienceReviews.Add(review);

            // Update experience rating
            var experience = await _context.Experiences.FindAsync(id);
            if (experience != null)
            {
                var allReviews = await _context.ExperienceReviews
                    .Where(er => er.ExperienceId == id)
                    .ToListAsync();

                allReviews.Add(review);
                experience.Rating = (decimal)allReviews.Average(r => r.Rating);
                experience.TotalReviews = allReviews.Count;
            }

            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetExperience), new { id = review.Id }, review);
        }

        // GET: api/Experience/providers
        [HttpGet("providers")]
        public async Task<ActionResult<IEnumerable<ExperienceProvider>>> GetExperienceProviders()
        {
            var providers = await _context.ExperienceProviders
                .Where(ep => ep.Status == ExperienceStatus.Active)
                .Include(ep => ep.Images.Where(i => i.IsPrimary))
                .OrderByDescending(ep => ep.Rating)
                .ToListAsync();

            return Ok(providers);
        }

        // GET: api/Experience/providers/{id}
        [HttpGet("providers/{id}")]
        public async Task<ActionResult<ExperienceProvider>> GetExperienceProvider(int id)
        {
            var provider = await _context.ExperienceProviders
                .Include(ep => ep.Images)
                .Include(ep => ep.Experiences.Where(e => e.IsActive))
                    .ThenInclude(e => e.Images.Where(i => i.IsPrimary))
                .FirstOrDefaultAsync(ep => ep.Id == id);

            if (provider == null)
            {
                return NotFound();
            }

            return Ok(provider);
        }

        // POST: api/Experience/providers
        [HttpPost("providers")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ExperienceProvider>> CreateExperienceProvider(ExperienceProvider provider)
        {
            provider.CreatedAt = DateTime.UtcNow;
            provider.UpdatedAt = DateTime.UtcNow;

            _context.ExperienceProviders.Add(provider);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetExperienceProvider), new { id = provider.Id }, provider);
        }

        // GET: api/Experience/categories
        [HttpGet("categories")]
        public ActionResult<IEnumerable<string>> GetExperienceCategories()
        {
            var categories = Enum.GetNames(typeof(ExperienceCategory));
            return Ok(categories);
        }

        // GET: api/Experience/search
        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<Experience>>> SearchExperiences(
            string query,
            ExperienceCategory? category = null,
            decimal? maxPrice = null,
            int? maxDuration = null)
        {
            var experiences = await _context.Experiences
                .Where(e => e.IsActive)
                .Where(e => e.Title.Contains(query) || e.Description.Contains(query) || e.ShortDescription.Contains(query))
                .Where(e => !category.HasValue || e.Category == category.Value)
                .Where(e => !maxPrice.HasValue || e.Price <= maxPrice.Value)
                .Where(e => !maxDuration.HasValue || e.Duration <= maxDuration.Value)
                .Include(e => e.Provider)
                .Include(e => e.Images.Where(i => i.IsPrimary))
                .OrderByDescending(e => e.Rating)
                .Take(50)
                .ToListAsync();

            return Ok(experiences);
        }

        // GET: api/Experience/nearby
        [HttpGet("nearby")]
        public async Task<ActionResult<IEnumerable<Experience>>> GetNearbyExperiences(
            decimal latitude,
            decimal longitude,
            double radiusKm = 10.0)
        {
            // Simple distance calculation (for more accurate results, use spatial queries)
            var experiences = await _context.Experiences
                .Where(e => e.IsActive)
                .Include(e => e.Provider)
                .Include(e => e.Images.Where(i => i.IsPrimary))
                .ToListAsync();

            var nearbyExperiences = experiences
                .Where(e => CalculateDistance(latitude, longitude, e.Latitude, e.Longitude) <= radiusKm)
                .OrderBy(e => CalculateDistance(latitude, longitude, e.Latitude, e.Longitude))
                .Take(20)
                .ToList();

            return Ok(nearbyExperiences);
        }

        private static double CalculateDistance(decimal lat1, decimal lon1, decimal lat2, decimal lon2)
        {
            var R = 6371; // Earth's radius in kilometers
            var dLat = ToRadians((double)(lat2 - lat1));
            var dLon = ToRadians((double)(lon2 - lon1));
            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(ToRadians((double)lat1)) * Math.Cos(ToRadians((double)lat2)) *
                    Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            return R * c;
        }

        private static double ToRadians(double degrees)
        {
            return degrees * Math.PI / 180;
        }
    }
}
