-- Hotel Marketplace Sample Data Population Script
USE HotelMarketplace;
GO

-- Insert sample rooms for existing properties
INSERT INTO [Rooms] ([PropertyId], [RoomNumber], [RoomType], [Status], [Floor], [BasePrice], [MaxOccupancy], [Bedrooms], [Bathrooms], [SquareFootage], [HasBalcony], [Has<PERSON>itchen], [HasLivingRoom], [Description], [ViewType])
VALUES 
    (1, '101', 'Standard', 'Available', 1, 150.00, 2, 1, 1, 350.00, 0, 0, 0, 'Comfortable standard room with city view', 'City'),
    (1, '102', 'Deluxe', 'Available', 1, 200.00, 2, 1, 1, 450.00, 1, 0, 1, 'Deluxe room with balcony and living area', 'Garden'),
    (1, '201', 'Suite', 'Available', 2, 350.00, 4, 2, 2, 800.00, 1, 1, 1, 'Luxury suite with full kitchen and balcony', 'Ocean'),
    (2, 'A1', 'Studio', 'Available', 1, 120.00, 2, 0, 1, 300.00, 0, 1, 0, 'Modern studio apartment with kitchenette', 'City'),
    (2, 'B2', 'OneBedroom', 'Available', 2, 180.00, 3, 1, 1, 500.00, 1, 1, 1, 'One bedroom apartment with balcony', 'Garden');

-- Insert room images
INSERT INTO [RoomImages] ([RoomId], [ImageUrl], [IsPrimary], [Category])
VALUES 
    (1, '/images/rooms/room101_main.jpg', 1, 'Bedroom'),
    (1, '/images/rooms/room101_bathroom.jpg', 0, 'Bathroom'),
    (2, '/images/rooms/room102_main.jpg', 1, 'Bedroom'),
    (2, '/images/rooms/room102_balcony.jpg', 0, 'Balcony'),
    (3, '/images/rooms/suite201_main.jpg', 1, 'Living Room'),
    (3, '/images/rooms/suite201_kitchen.jpg', 0, 'Kitchen');

-- Insert room availability for next 30 days
DECLARE @StartDate DATE = GETDATE();
DECLARE @EndDate DATE = DATEADD(DAY, 30, @StartDate);
DECLARE @CurrentDate DATE = @StartDate;

WHILE @CurrentDate <= @EndDate
BEGIN
    INSERT INTO [RoomAvailabilities] ([RoomId], [Date], [IsAvailable], [Price], [MinNights], [MaxNights])
    VALUES 
        (1, @CurrentDate, 1, 150.00, 1, 7),
        (2, @CurrentDate, 1, 200.00, 1, 7),
        (3, @CurrentDate, 1, 350.00, 2, 14),
        (4, @CurrentDate, 1, 120.00, 1, 7),
        (5, @CurrentDate, 1, 180.00, 1, 7);
    
    SET @CurrentDate = DATEADD(DAY, 1, @CurrentDate);
END;

-- Insert sample companies
INSERT INTO [Companies] ([Name], [TaxId], [Address], [City], [Country], [Email], [CreditLimit], [AvailableCredit], [Status])
VALUES 
    ('TechCorp Solutions', 'TC123456789', '123 Business Ave', 'New York', 'USA', '<EMAIL>', 50000.00, 50000.00, 'Active'),
    ('Global Consulting Ltd', 'GC987654321', '456 Corporate Blvd', 'London', 'UK', '<EMAIL>', 75000.00, 75000.00, 'Active'),
    ('StartupHub Inc', 'SH555666777', '789 Innovation St', 'San Francisco', 'USA', '<EMAIL>', 25000.00, 25000.00, 'Pending');

-- Insert corporate users (assuming user IDs 1, 2, 3 exist)
INSERT INTO [CorporateUsers] ([UserId], [CompanyId], [EmployeeId], [Department], [JobTitle], [CanApproveBookings], [BookingLimit])
VALUES 
    (1, 1, 'EMP001', 'Sales', 'Sales Manager', 1, 5000.00),
    (2, 1, 'EMP002', 'Marketing', 'Marketing Specialist', 0, 2000.00),
    (3, 2, 'CON001', 'Consulting', 'Senior Consultant', 1, 10000.00);

-- Insert corporate rates
INSERT INTO [CorporateRates] ([CompanyId], [PropertyId], [DiscountPercentage], [ValidFrom], [ValidTo])
VALUES 
    (1, 1, 15.00, GETDATE(), DATEADD(YEAR, 1, GETDATE())),
    (1, 2, 10.00, GETDATE(), DATEADD(YEAR, 1, GETDATE())),
    (2, 1, 20.00, GETDATE(), DATEADD(YEAR, 1, GETDATE()));

-- Insert pricing rules
INSERT INTO [PricingRules] ([PropertyId], [Name], [RuleType], [Priority], [ValidFrom], [ValidTo], [AdjustmentPercentage])
VALUES 
    (1, 'Weekend Premium', 'WeekendPremium', 1, GETDATE(), DATEADD(YEAR, 1, GETDATE()), 15.00),
    (1, 'Early Bird Discount', 'EarlyBird', 2, GETDATE(), DATEADD(YEAR, 1, GETDATE()), -10.00),
    (2, 'Last Minute Deal', 'LastMinute', 1, GETDATE(), DATEADD(YEAR, 1, GETDATE()), -15.00);

-- Insert seasonal pricing
INSERT INTO [SeasonalPricings] ([PropertyId], [SeasonName], [StartDate], [EndDate], [PriceMultiplier])
VALUES 
    (1, 'Summer Peak', '2024-06-01', '2024-08-31', 1.25),
    (1, 'Winter Holiday', '2024-12-15', '2025-01-15', 1.40),
    (2, 'Spring Season', '2024-03-01', '2024-05-31', 1.10);

-- Insert staff
INSERT INTO [Staff] ([FirstName], [LastName], [Email], [PhoneNumber], [Department], [Position], [HourlyRate], [HireDate])
VALUES 
    ('Maria', 'Rodriguez', '<EMAIL>', '******-0101', 'Housekeeping', 'Housekeeper', 18.50, '2023-01-15'),
    ('James', 'Wilson', '<EMAIL>', '******-0102', 'Maintenance', 'Maintenance Technician', 25.00, '2022-08-20'),
    ('Sarah', 'Johnson', '<EMAIL>', '******-0103', 'Housekeeping', 'Housekeeping Supervisor', 22.00, '2021-11-10'),
    ('David', 'Brown', '<EMAIL>', '******-0104', 'Front Desk', 'Concierge', 20.00, '2023-03-05');

-- Insert housekeeping tasks
INSERT INTO [HousekeepingTasks] ([PropertyId], [RoomId], [AssignedStaffId], [Title], [Description], [TaskType], [Priority], [ScheduledDate], [EstimatedDuration])
VALUES 
    (1, 1, 1, 'Daily Room Cleaning', 'Standard room cleaning and maintenance', 'Cleaning', 'Medium', GETDATE(), '01:30:00'),
    (1, 2, 1, 'Deep Clean Suite', 'Thorough cleaning of deluxe suite', 'Cleaning', 'High', DATEADD(DAY, 1, GETDATE()), '02:30:00'),
    (1, 3, 3, 'Room Inspection', 'Quality control inspection', 'Inspection', 'Medium', GETDATE(), '00:30:00'),
    (2, 4, 1, 'Apartment Turnover', 'Clean apartment between guests', 'Cleaning', 'High', GETDATE(), '02:00:00');

-- Insert inventory items
INSERT INTO [InventoryItems] ([PropertyId], [Name], [Category], [Unit], [CurrentStock], [MinimumStock], [MaximumStock], [UnitCost], [Supplier], [LastRestocked])
VALUES 
    (1, 'Toilet Paper', 'Bathroom Supplies', 'Rolls', 150, 50, 300, 1.25, 'Hotel Supply Co', GETDATE()),
    (1, 'Towels - Bath', 'Linens', 'Pieces', 80, 30, 150, 15.00, 'Linen Express', DATEADD(DAY, -5, GETDATE())),
    (1, 'Shampoo Bottles', 'Bathroom Amenities', 'Bottles', 45, 20, 100, 3.50, 'Amenity Plus', GETDATE()),
    (2, 'Bed Sheets - Queen', 'Linens', 'Sets', 25, 10, 50, 35.00, 'Linen Express', DATEADD(DAY, -3, GETDATE())),
    (2, 'Coffee Pods', 'Kitchen Supplies', 'Boxes', 12, 5, 30, 8.99, 'Coffee Direct', DATEADD(DAY, -1, GETDATE()));

-- Insert channels
INSERT INTO [Channels] ([Name], [ChannelType], [CommissionRate], [ApiEndpoint])
VALUES 
    ('Booking.com', 'OTA', 15.00, 'https://api.booking.com/v1/'),
    ('Expedia', 'OTA', 18.00, 'https://api.expedia.com/v3/'),
    ('Airbnb', 'OTA', 12.00, 'https://api.airbnb.com/v2/'),
    ('Direct Website', 'Direct', 0.00, NULL),
    ('Corporate Portal', 'Corporate', 5.00, 'https://corporate.hotelmarketplace.com/api/');

-- Insert property channels
INSERT INTO [PropertyChannels] ([PropertyId], [ChannelId], [ChannelPropertyId], [CommissionOverride])
VALUES 
    (1, 1, 'BDC_PROP_001', 15.00),
    (1, 2, 'EXP_PROP_001', 18.00),
    (1, 4, 'DIRECT_001', 0.00),
    (2, 1, 'BDC_PROP_002', 15.00),
    (2, 3, 'ABB_PROP_002', 12.00);

-- Insert experience providers
INSERT INTO [ExperienceProviders] ([Name], [Description], [Email], [PhoneNumber], [City], [Country], [Rating], [TotalReviews], [IsVerified], [Status])
VALUES 
    ('City Tours Pro', 'Professional city tour guides with 10+ years experience', '<EMAIL>', '******-0201', 'New York', 'USA', 4.8, 245, 1, 'Active'),
    ('Adventure Seekers', 'Outdoor adventure and extreme sports experiences', '<EMAIL>', '******-0202', 'San Francisco', 'USA', 4.6, 189, 1, 'Active'),
    ('Culinary Delights', 'Food tours and cooking classes with local chefs', '<EMAIL>', '******-0203', 'New York', 'USA', 4.9, 156, 1, 'Active');

-- Insert experiences
INSERT INTO [Experiences] ([ProviderId], [Title], [Description], [Category], [Price], [Duration], [MaxParticipants], [MeetingPoint], [Latitude], [Longitude])
VALUES 
    (1, 'Manhattan Walking Tour', 'Explore the best of Manhattan with our expert guides. Visit Times Square, Central Park, and more!', 'Tours', 45.00, 180, 15, 'Times Square Visitor Center', 40.7580, -73.9855),
    (1, 'Brooklyn Bridge Experience', 'Walk across the iconic Brooklyn Bridge and learn about its fascinating history', 'Cultural', 35.00, 120, 12, 'Brooklyn Bridge Entrance', 40.7061, -73.9969),
    (2, 'Golden Gate Bike Tour', 'Cycle across the Golden Gate Bridge and explore Sausalito', 'Adventure', 75.00, 240, 10, 'Fishermans Wharf', 37.8080, -122.4177),
    (3, 'Little Italy Food Tour', 'Taste authentic Italian cuisine in the heart of Little Italy', 'Dining', 85.00, 150, 8, 'Little Italy Welcome Center', 40.7195, -73.9967);

-- Insert experience images
INSERT INTO [ExperienceImages] ([ExperienceId], [ImageUrl], [IsPrimary], [Caption], [SortOrder])
VALUES 
    (1, '/images/experiences/manhattan_tour_1.jpg', 1, 'Times Square during the tour', 1),
    (1, '/images/experiences/manhattan_tour_2.jpg', 0, 'Central Park visit', 2),
    (2, '/images/experiences/brooklyn_bridge_1.jpg', 1, 'Walking on Brooklyn Bridge', 1),
    (3, '/images/experiences/golden_gate_bike_1.jpg', 1, 'Biking across Golden Gate', 1),
    (4, '/images/experiences/food_tour_1.jpg', 1, 'Delicious Italian food', 1);

-- Insert experience availability for next 14 days
DECLARE @ExpStartDate DATE = GETDATE();
DECLARE @ExpEndDate DATE = DATEADD(DAY, 14, @ExpStartDate);
DECLARE @ExpCurrentDate DATE = @ExpStartDate;

WHILE @ExpCurrentDate <= @ExpEndDate
BEGIN
    -- Morning tours
    INSERT INTO [ExperienceAvailabilities] ([ExperienceId], [Date], [StartTime], [EndTime], [AvailableSpots], [Price])
    VALUES 
        (1, @ExpCurrentDate, '09:00:00', '12:00:00', 15, 45.00),
        (2, @ExpCurrentDate, '10:00:00', '12:00:00', 12, 35.00);
    
    -- Afternoon tours
    INSERT INTO [ExperienceAvailabilities] ([ExperienceId], [Date], [StartTime], [EndTime], [AvailableSpots], [Price])
    VALUES 
        (1, @ExpCurrentDate, '14:00:00', '17:00:00', 15, 45.00),
        (3, @ExpCurrentDate, '13:00:00', '17:00:00', 10, 75.00),
        (4, @ExpCurrentDate, '15:00:00', '17:30:00', 8, 85.00);
    
    SET @ExpCurrentDate = DATEADD(DAY, 1, @ExpCurrentDate);
END;

-- Insert experience tags
INSERT INTO [ExperienceTags] ([ExperienceId], [Tag])
VALUES 
    (1, 'Walking'),
    (1, 'Sightseeing'),
    (1, 'History'),
    (2, 'Architecture'),
    (2, 'Photography'),
    (3, 'Cycling'),
    (3, 'Outdoor'),
    (3, 'Scenic'),
    (4, 'Food'),
    (4, 'Culture'),
    (4, 'Local');

-- Insert service providers
INSERT INTO [ServiceProviders] ([Name], [ServiceType], [Description], [Email], [PhoneNumber], [Rating], [TotalReviews])
VALUES 
    ('Elite Transportation', 'Transportation', 'Luxury car service and airport transfers', '<EMAIL>', '******-0301', 4.7, 89),
    ('Spa Wellness Center', 'Wellness', 'In-room spa services and wellness treatments', '<EMAIL>', '******-0302', 4.9, 156),
    ('Business Services Pro', 'Business', 'Meeting room setup and business support services', '<EMAIL>', '******-0303', 4.5, 67);

PRINT 'Sample data inserted successfully!';
PRINT 'Hotel Marketplace is ready for testing with sample data.';
GO
