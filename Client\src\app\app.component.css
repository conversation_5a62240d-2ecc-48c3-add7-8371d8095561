* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Circular', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }
  
  body {
    background-color: #f7f7f7;
    color: #222222;
    line-height: 1.4;
  }
  
  .btn-primary {
    background-color: #ff385c;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .btn-primary:hover {
    background-color: #e31c5f;
  }
  
  .btn-primary:disabled {
    background-color: #f2f2f2;
    color: #b0b0b0;
    cursor: not-allowed;
  }
  
  .btn-secondary {
    background-color: white;
    color: #222222;
    border: 1px solid #222222;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    text-decoration: none;
    display: inline-block;
  }
  
  .btn-secondary:hover {
    background-color: #f7f7f7;
  }
  
  .btn-secondary:disabled {
    border-color: #dddddd;
    color: #b0b0b0;
    cursor: not-allowed;
  }
  