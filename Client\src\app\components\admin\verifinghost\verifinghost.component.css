.verification-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.verification-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.verification-header h1 {
  margin-left: 20px;
  color: #333;
  font-size: 24px;
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 16px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.back-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.back-button mat-icon {
  margin-right: 8px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px;
}

.loading-container p {
  margin-top: 20px;
  color: #666;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  color: #f44336;
  margin-bottom: 20px;
}

.error-container p {
  margin-bottom: 20px;
  color: #666;
}

.verification-card {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 30px;
}

.verification-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.info-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.info-item mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  margin-right: 15px;
  color: #2196F3;
}

.info-text {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.value {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.status-pending {
  background-color: #FFC107;
  color: #000;
}

.status-verified {
  background-color: #4CAF50;
  color: white;
}

.status-rejected {
  background-color: #F44336;
  color: white;
}

.verification-document {
  margin-top: 30px;
}

.verification-document h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.document-preview {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.document-image {
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
}

mat-card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  gap: 10px;
}

mat-card-actions button {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .verification-info {
    grid-template-columns: 1fr;
  }
  
  .verification-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .verification-header h1 {
    margin-left: 0;
    margin-top: 15px;
  }
  
  mat-card-actions {
    flex-direction: column;
  }
  
  mat-card-actions button {
    width: 100%;
    margin-bottom: 10px;
  }
}
