<nav class="header" [class.scrolled]="isScrolled">
    <div class="header-container">
      <!-- <PERSON><PERSON> on the left -->
      <div class="header-left">
      <a href="/home" class="header-logo">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" class="airbnb-icon">
          <path fill="#FF5A5F" d="M224 373.1c-25.2-31.7-40.1-59.4-45-83.2-22.6-88 112.6-88 90.1 0-5.5 24.3-20.3 52-45 83.2zm138.2 73.2c-42.1 18.3-83.7-10.9-119.3-50.5 103.9-130.1 46.1-200-18.9-200-54.9 0-85.2 46.5-73.3 100.5 6.9 29.2 25.2 62.4 54.4 99.5-32.5 36.1-60.6 52.7-85.2 54.9-50 7.4-89.1-41.1-71.3-91.1 15.1-39.2 111.7-231.2 115.9-241.6 15.8-30.1 25.6-57.4 59.4-57.4 32.3 0 43.4 25.9 60.4 59.9 36 70.6 89.4 177.5 114.8 239.1 13.2 33.1-1.4 71.3-37 86.6z"/>
        </svg>
        <span class="logo-text">Airbnb</span>
      </a>
      </div>
      <!-- Search bar in the center -->
      <div class="header-center">
        <app-search-bar 
          [isHeaderScrolled]="isScrolled"
          (searchPerformed)="onSearch($event)">
        </app-search-bar>
      </div>



      <!-- "Airbnb your home" button on the right -->
      <div class="header-right">
        <a class="host-button" *ngIf="isGuest" (click)="switchToHosting()" >Switch to hosting</a>

        <!-- <a class="host-button" href="/login">Airbnb your home
        </a> -->
        <!-- User profile icon with dropdown menu -->
         <app-navbar [isHeaderScrolled]="isScrolled"></app-navbar>
      </div>
    </div>
  </nav>