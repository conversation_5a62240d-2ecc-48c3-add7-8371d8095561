.booking-details-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.error-message {
  color: #f44336;
  text-align: center;
  margin: 2rem 0;
  padding: 1rem;
  background-color: #ffebee;
  border-radius: 4px;
}

.booking-content h2 {
  margin-bottom: 2rem;
  color: #333;
  text-align: center;
}

.booking-card {
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  background-color: white;
  border-radius: 4px;
  transition: transform 0.3s ease;
}

.booking-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.booking-card mat-card-header {
  margin-bottom: 1rem;
}

.booking-card mat-card-title {
  color: #3f51b5;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.booking-card mat-card-content p {
  margin: 0.5rem 0;
  line-height: 1.5;
}

.action-buttons {
  width: 100%;
  text-align: center;
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.action-buttons button {
  background-color: #3f51b5;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  min-width: 150px;
  transition: background-color 0.3s ease;
}

.action-buttons button:hover {
  background-color: #303f9f;
}

.status-message {
  text-align: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 500;
}

.pending-actions {
  text-align: center;
  width: 100%;
}

.pending-actions .status-message {
  color: #ff9800;
  margin-bottom: 1rem;
}

.pending-actions button {
  background-color: #ff9800;
  color: white;
  padding: 12px 24px;
  font-size: 1.1rem;
  font-weight: 500;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  min-width: 200px;
  transition: background-color 0.3s ease;
}

.pending-actions button:hover {
  background-color: #f57c00;
}

.confirmed-actions {
  text-align: center;
  width: 100%;
}

.confirmed-actions .status-message {
  color: #4caf50;
}

.cancelled-actions {
  text-align: center;
  width: 100%;
}

.cancelled-actions .status-message {
  color: #f44336;
}

.status-pending {
  color: #ff9800;
  font-weight: 500;
}

.status-confirmed {
  color: #4caf50;
  font-weight: 500;
}

.status-cancelled {
  color: #f44336;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .booking-card {
    margin-bottom: 15px;
  }
  
  .booking-card mat-card-title {
    font-size: 1.1rem;
  }
  
  .action-buttons button {
    min-width: 120px;
    padding: 8px 16px;
  }
} 