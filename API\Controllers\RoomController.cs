using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using API.Data;
using API.Models;

namespace API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class RoomController : ControllerBase
    {
        private readonly AppDbContext _context;

        public RoomController(AppDbContext context)
        {
            _context = context;
        }

        // GET: api/Room/property/{propertyId}
        [HttpGet("property/{propertyId}")]
        public async Task<ActionResult<IEnumerable<Room>>> GetRoomsByProperty(int propertyId)
        {
            var rooms = await _context.Rooms
                .Where(r => r.PropertyId == propertyId)
                .Include(r => r.RoomImages)
                .Include(r => r.RoomAmenities)
                    .ThenInclude(ra => ra.Amenity)
                .OrderBy(r => r.RoomNumber)
                .ToListAsync();

            return Ok(rooms);
        }

        // GET: api/Room/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<Room>> GetRoom(int id)
        {
            var room = await _context.Rooms
                .Include(r => r.Property)
                .Include(r => r.RoomImages)
                .Include(r => r.RoomAmenities)
                    .ThenInclude(ra => ra.Amenity)
                .Include(r => r.RoomAvailabilities.Where(ra => ra.Date >= DateTime.Today))
                .FirstOrDefaultAsync(r => r.Id == id);

            if (room == null)
            {
                return NotFound();
            }

            return Ok(room);
        }

        // POST: api/Room
        [HttpPost]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<Room>> CreateRoom(Room room)
        {
            // Verify property ownership
            var property = await _context.Properties.FindAsync(room.PropertyId);
            if (property == null)
            {
                return BadRequest("Property not found");
            }

            // Check if room number already exists for this property
            var existingRoom = await _context.Rooms
                .FirstOrDefaultAsync(r => r.PropertyId == room.PropertyId && r.RoomNumber == room.RoomNumber);
            
            if (existingRoom != null)
            {
                return BadRequest("Room number already exists for this property");
            }

            room.CreatedAt = DateTime.UtcNow;
            room.UpdatedAt = DateTime.UtcNow;

            _context.Rooms.Add(room);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetRoom), new { id = room.Id }, room);
        }

        // PUT: api/Room/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<IActionResult> UpdateRoom(int id, Room room)
        {
            if (id != room.Id)
            {
                return BadRequest();
            }

            var existingRoom = await _context.Rooms.FindAsync(id);
            if (existingRoom == null)
            {
                return NotFound();
            }

            // Update properties
            existingRoom.RoomNumber = room.RoomNumber;
            existingRoom.RoomType = room.RoomType;
            existingRoom.Status = room.Status;
            existingRoom.Floor = room.Floor;
            existingRoom.BasePrice = room.BasePrice;
            existingRoom.MaxOccupancy = room.MaxOccupancy;
            existingRoom.Bedrooms = room.Bedrooms;
            existingRoom.Bathrooms = room.Bathrooms;
            existingRoom.SquareFootage = room.SquareFootage;
            existingRoom.HasBalcony = room.HasBalcony;
            existingRoom.HasKitchen = room.HasKitchen;
            existingRoom.HasLivingRoom = room.HasLivingRoom;
            existingRoom.Description = room.Description;
            existingRoom.ViewType = room.ViewType;
            existingRoom.IsAccessible = room.IsAccessible;
            existingRoom.AllowsSmoking = room.AllowsSmoking;
            existingRoom.AllowsPets = room.AllowsPets;
            existingRoom.UpdatedAt = DateTime.UtcNow;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!RoomExists(id))
                {
                    return NotFound();
                }
                throw;
            }

            return NoContent();
        }

        // DELETE: api/Room/{id}
        [HttpDelete("{id}")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<IActionResult> DeleteRoom(int id)
        {
            var room = await _context.Rooms.FindAsync(id);
            if (room == null)
            {
                return NotFound();
            }

            // Check if room has any bookings
            var hasBookings = await _context.RoomBookings
                .AnyAsync(rb => rb.RoomId == id);

            if (hasBookings)
            {
                return BadRequest("Cannot delete room with existing bookings");
            }

            _context.Rooms.Remove(room);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        // GET: api/Room/{id}/availability
        [HttpGet("{id}/availability")]
        public async Task<ActionResult<IEnumerable<RoomAvailability>>> GetRoomAvailability(int id, DateTime? startDate = null, DateTime? endDate = null)
        {
            startDate ??= DateTime.Today;
            endDate ??= DateTime.Today.AddDays(30);

            var availability = await _context.RoomAvailabilities
                .Where(ra => ra.RoomId == id && ra.Date >= startDate && ra.Date <= endDate)
                .OrderBy(ra => ra.Date)
                .ToListAsync();

            return Ok(availability);
        }

        // POST: api/Room/{id}/availability
        [HttpPost("{id}/availability")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult> UpdateRoomAvailability(int id, List<RoomAvailability> availabilities)
        {
            var room = await _context.Rooms.FindAsync(id);
            if (room == null)
            {
                return NotFound("Room not found");
            }

            foreach (var availability in availabilities)
            {
                availability.RoomId = id;
                availability.CreatedAt = DateTime.UtcNow;

                var existing = await _context.RoomAvailabilities
                    .FirstOrDefaultAsync(ra => ra.RoomId == id && ra.Date == availability.Date);

                if (existing != null)
                {
                    existing.IsAvailable = availability.IsAvailable;
                    existing.Price = availability.Price;
                    existing.MinNights = availability.MinNights;
                    existing.MaxNights = availability.MaxNights;
                }
                else
                {
                    _context.RoomAvailabilities.Add(availability);
                }
            }

            await _context.SaveChangesAsync();
            return Ok();
        }

        // GET: api/Room/{id}/bookings
        [HttpGet("{id}/bookings")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<IEnumerable<RoomBooking>>> GetRoomBookings(int id)
        {
            var bookings = await _context.RoomBookings
                .Where(rb => rb.RoomId == id)
                .Include(rb => rb.Booking)
                    .ThenInclude(b => b.Guest)
                .OrderByDescending(rb => rb.CheckInDate)
                .ToListAsync();

            return Ok(bookings);
        }

        // POST: api/Room/{id}/images
        [HttpPost("{id}/images")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<ActionResult<RoomImage>> AddRoomImage(int id, RoomImage roomImage)
        {
            var room = await _context.Rooms.FindAsync(id);
            if (room == null)
            {
                return NotFound("Room not found");
            }

            roomImage.RoomId = id;
            roomImage.CreatedAt = DateTime.UtcNow;

            _context.RoomImages.Add(roomImage);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetRoom), new { id = roomImage.Id }, roomImage);
        }

        // DELETE: api/Room/images/{imageId}
        [HttpDelete("images/{imageId}")]
        [Authorize(Roles = "Host,Admin")]
        public async Task<IActionResult> DeleteRoomImage(int imageId)
        {
            var image = await _context.RoomImages.FindAsync(imageId);
            if (image == null)
            {
                return NotFound();
            }

            _context.RoomImages.Remove(image);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        // GET: api/Room/search
        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<Room>>> SearchRooms(
            int? propertyId = null,
            RoomType? roomType = null,
            RoomStatus? status = null,
            decimal? minPrice = null,
            decimal? maxPrice = null,
            int? minOccupancy = null,
            bool? hasBalcony = null,
            bool? hasKitchen = null)
        {
            var query = _context.Rooms.AsQueryable();

            if (propertyId.HasValue)
                query = query.Where(r => r.PropertyId == propertyId.Value);

            if (roomType.HasValue)
                query = query.Where(r => r.RoomType == roomType.Value);

            if (status.HasValue)
                query = query.Where(r => r.Status == status.Value);

            if (minPrice.HasValue)
                query = query.Where(r => r.BasePrice >= minPrice.Value);

            if (maxPrice.HasValue)
                query = query.Where(r => r.BasePrice <= maxPrice.Value);

            if (minOccupancy.HasValue)
                query = query.Where(r => r.MaxOccupancy >= minOccupancy.Value);

            if (hasBalcony.HasValue)
                query = query.Where(r => r.HasBalcony == hasBalcony.Value);

            if (hasKitchen.HasValue)
                query = query.Where(r => r.HasKitchen == hasKitchen.Value);

            var rooms = await query
                .Include(r => r.Property)
                .Include(r => r.RoomImages)
                .OrderBy(r => r.PropertyId)
                .ThenBy(r => r.RoomNumber)
                .ToListAsync();

            return Ok(rooms);
        }

        private bool RoomExists(int id)
        {
            return _context.Rooms.Any(e => e.Id == id);
        }
    }
}
