// src/app/components/auth/auth.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, Subject, catchError, map, of, tap, throwError, retry } from 'rxjs';
import { Router } from '@angular/router';
import { User, TokenResponse } from '../models/user.model';
import { SocialAuthService, SocialUser } from "@abacritt/angularx-social-login";
import { GoogleLoginProvider } from "@abacritt/angularx-social-login";

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private baseUrl = 'https://localhost:7228/api/Auth/';
  // Backup URL in case the primary one fails
  private fallbackUrl = 'http://localhost:5161/api/Auth/';
  private currentUserSubject: BehaviorSubject<User | null>;
  public currentUser: Observable<User | null>;
  public isLoggedIn: boolean = false;

  public userDetails: User | null = null;




  constructor(private http: HttpClient, private router: Router,
    private socialAuthService: SocialAuthService

  ) {

    const storedUser = localStorage.getItem('currentUser');
    this.currentUserSubject = new BehaviorSubject<User | null>(
      storedUser ? JSON.parse(storedUser) : null
    );
    this.currentUser = this.currentUserSubject.asObservable();
    this.currentUser.subscribe(user => {
      this.isLoggedIn = !!user; // Set isLoggedIn based on user existence
    });
    this.socialAuthService.authState.subscribe((user: SocialUser) => {
      if (user) {
        this.handleGoogleLogin(user);
      }
    });
  }
  signInWithGoogle(): void {
    this.socialAuthService.signIn(GoogleLoginProvider.PROVIDER_ID);
  }

  getUserEmail(
    userToken: string
  ): string | null {
    const decoded = this.decodeToken(userToken);
    return decoded.unique_name || null;
  }
  isAdmin(): boolean {

    const user = this.currentUserSubject.value;
    return user ? user.role === 'Admin' : false;
  }

  // Method to handle post-login navigation based on user role
  navigateBasedOnRole() {
    const user = this.currentUserSubject.value;
    if (!user) return;

    if (user.role === 'Admin') {
      this.router.navigate(['/admin']);
    } else if (user.role === 'Host') {
      this.router.navigate(['/host']);
    } else {
      this.router.navigate(['/home']);
    }
  }
  // auth.service.ts
  switchToHosting(): Observable<TokenResponse> {
    return this.http.post<TokenResponse>(`${this.baseUrl}switch-to-host`, {}).pipe(
      tap((response) => {
        // Decode the new token and update user state
        const decoded = this.decodeToken(response.accessToken);
        const updatedUser: User = {
          email: decoded.unique_name,
          role: decoded.role,
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
          firstName: this.currentUserValue?.firstName, // Preserve existing fields
          lastName: this.currentUserValue?.lastName,
          imageUrl: this.currentUserValue?.imageUrl,
        };

        // Update localStorage and BehaviorSubject
        localStorage.setItem('currentUser', JSON.stringify(updatedUser));
        this.currentUserSubject.next(updatedUser);
      }),
      catchError((err) => {
        console.error('Role switch failed', err);
        return throwError(() => err);
      })
    );
  }


  isUserAGuest(): boolean {
    const user = this.currentUserSubject.value;
    return user ? user.role === 'Guest' : false;
  }
  private handleGoogleLogin(googleUser: SocialUser): void {
    this.http.post(`${this.baseUrl}google-auth`, {
      email: googleUser.email,
      firstName: googleUser.firstName,
      lastName: googleUser.lastName,
      idToken: googleUser.idToken
    }).subscribe({
      next: (response: any) => {
        const decoded = this.decodeToken(response.accessToken);
        const user: User = {
          email: decoded.unique_name,
          role: decoded.role,
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
          firstName: googleUser.firstName,
          lastName: googleUser.lastName,
          imageUrl: googleUser.photoUrl
        };

        localStorage.setItem('currentUser', JSON.stringify(user));
        this.currentUserSubject.next(user);

        // Check if there's a saved redirect URL
        const redirectUrl = this.getRedirectUrl();
        if (redirectUrl) {
          // Navigate to the saved URL
          this.router.navigateByUrl(redirectUrl);
        } else {
          // Default navigation based on role
          this.navigateBasedOnRole();
        }
      },
      error: (err) => {
        console.error('Google login error:', err);
        this.logout();
      }
    });
  }


  public get userId(): string | null {
    const user = this.currentUserSubject.value;
    return user ? this.getUserIdFromToken(user.accessToken) : null;
  }
  public get userEmail(): string | null {
    const user = this.currentUserSubject.value;
    return user ? this.getUserEmail(user.accessToken) : null;
  }

  public get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  login(email: string, password: string): Observable<User> {
    return this.http.post<any>(`${this.baseUrl}login`, { email, password }).pipe(
      map((response: TokenResponse) => {
        const decoded = this.decodeToken(response.accessToken);
        const user: User = {
          email: decoded.unique_name,
          role: decoded.role || decoded['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'],
          accessToken: response.accessToken,
          refreshToken: response.refreshToken
        };
        localStorage.setItem('currentUser', JSON.stringify(user));
        this.currentUserSubject.next(user);
        return user;
      })
    );
  }



  checkEmailVerificationStatus():
    Observable<boolean> {
    return this.http.get<{ isEmailVerified: boolean }>(`https://localhost:7228/api/Profile/user/email-verification-status/`).pipe(
      map(response => response.isEmailVerified),
      catchError(error => {
        console.error('Error checking email verification status:', error);
        return of(false); // Return false in case of error
      })
    );
  }



  register(
    email: string, firstName: string, lastName: string, password: string
  ) {
    console.log('AuthService: Sending registration request to:', `${this.baseUrl}register`);

    // Create the request payload
    const payload = {
      email,
      firstName,
      lastName,
      password
    };

    // Try the primary URL first with retry logic
    return this.http.post(`${this.baseUrl}register`, payload).pipe(
      retry(2), // Retry the request up to 2 times
      catchError((error: HttpErrorResponse) => {
        // Log detailed error information
        console.warn('AuthService: Registration error details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message,
          error: error.error
        });

        // If the primary URL fails, try the fallback URL
        console.warn('AuthService: Primary URL failed, trying fallback URL');
        return this.http.post(`${this.fallbackUrl}register`, payload).pipe(
          retry(2), // Retry the fallback request up to 2 times
          catchError((fallbackError: HttpErrorResponse) => {
            // Log detailed fallback error information
            console.error('AuthService: Fallback registration error details:', {
              status: fallbackError.status,
              statusText: fallbackError.statusText,
              url: fallbackError.url,
              message: fallbackError.message,
              error: fallbackError.error
            });

            console.error('AuthService: Both URLs failed for registration');
            return throwError(() => fallbackError);
          })
        );
      })
    );
  }
  logout() {
    // First get the token to ensure it's available for the interceptor
    const token = this.currentUserValue?.accessToken;

    if (token) {
      console.log('Logging out with token:', token);
      this.http.post(`${this.baseUrl}logout`, {}).subscribe({
        next: () => {
          localStorage.removeItem('currentUser');
          this.currentUserSubject.next(null);
          this.router.navigate(['/login']);
        },
        error: (error) => {
          console.error('Logout failed', error);
          // Even if the server call fails, clear local data
          localStorage.removeItem('currentUser');
          this.currentUserSubject.next(null);
          this.router.navigate(['/login']);
        }
      });
    } else {
      // No token, just clear local data
      console.log('No token available, clearing local data only');
      localStorage.removeItem('currentUser');
      this.currentUserSubject.next(null);
      this.router.navigate(['/login']);
    }
  }
  refreshToken(): Observable<TokenResponse | null> {
    const user = this.currentUserValue;
    if (!user?.refreshToken) return of(null);

    return this.http.post<TokenResponse>(`${this.baseUrl}refresh-token`, {
      userId: this.getUserIdFromToken(user.accessToken),
      refreshToken: user.refreshToken
    }).pipe(
      map((response: TokenResponse) => {
        const decoded = this.decodeToken(response.accessToken);
        const updatedUser: User = {
          email: decoded.unique_name,
          role: decoded.role,
          accessToken: response.accessToken,
          refreshToken: response.refreshToken
        };
        localStorage.setItem('currentUser', JSON.stringify(updatedUser));
        this.currentUserSubject.next(updatedUser);
        return response;
      }),
      catchError(err => {
        this.logout();
        return throwError(() => err);
      })
    );
  }
  getCurrentUserId(): number {
    if (!this.currentUser) {
      throw new Error('No user is currently logged in');
    }
    return Number(this.userId) || 0;
  }

  public decodeToken(token: string): any {
    const decoded = JSON.parse(atob(token.split('.')[1]));
    return {
      ...decoded,
      nameid: decoded['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'],
      unique_name: decoded['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'],
      role: decoded['http://schemas.microsoft.com/ws/2008/06/identity/claims/role']
    };
  }

  public getUserIdFromToken(token: string): string {
    const decoded = this.decodeToken(token);
    return decoded.nameid;
  }

  handleError(error: any) {
    if (error.status === 401 || error.status === 403) {
      this.logout();
      this.router.navigate(['/forbidden']);
    }
    return throwError(() => error);
  }
  getUserProfile(): Observable<any> {
    return this.http.get(`${this.baseUrl}profile`).pipe(

      catchError(error => {
        if (error.status === 401) {
          //this.logout();
          // this.router.navigate(['/login']);
        }
        return throwError(() => error);
      })
    );
  }

  // Add these methods to store and retrieve redirect state
  storeRedirectState(url: string, data: any): void {
    sessionStorage.setItem('redirectUrl', url);
    sessionStorage.setItem('redirectData', JSON.stringify(data));
  }

  getRedirectUrl(): string | null {
    return sessionStorage.getItem('redirectUrl');
  }

  getRedirectData(): any {
    const data = sessionStorage.getItem('redirectData');
    return data ? JSON.parse(data) : null;
  }

  clearRedirectState(): void {
    sessionStorage.removeItem('redirectUrl');
    sessionStorage.removeItem('redirectData');
  }
}
