{"version": 3, "sources": ["../../../../../../node_modules/@abacritt/angularx-social-login/fesm2022/abacritt-angularx-social-login.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Inject, Injectable, Optional, SkipSelf, NgModule, Input, Directive } from '@angular/core';\nimport { BehaviorSubject, ReplaySubject, AsyncSubject, isObservable } from 'rxjs';\nimport { skip, filter, take } from 'rxjs/operators';\nimport { CommonModule } from '@angular/common';\nclass BaseLoginProvider {\n  constructor() {}\n  loadScript(id, src, onload, parentElement = null) {\n    // get document if platform is only browser\n    if (typeof document !== 'undefined' && !document.getElementById(id)) {\n      let signInJS = document.createElement('script');\n      signInJS.async = true;\n      signInJS.src = src;\n      signInJS.onload = onload;\n      if (!parentElement) {\n        parentElement = document.head;\n      }\n      parentElement.appendChild(signInJS);\n    }\n  }\n}\nclass SocialUser {}\nconst isGoogleAccountsDefined = () => {\n  return typeof window.google?.accounts !== 'undefined';\n};\nconst assertGoogleAccountsDefined = () => {\n  if (!isGoogleAccountsDefined()) {\n    throw new Error('Google Accounts API is undefined');\n  }\n};\nconst getGoogleAccountsOrThrow = () => {\n  assertGoogleAccountsDefined();\n  return window.google.accounts;\n};\nconst defaultInitOptions = {\n  oneTapEnabled: true\n};\nclass GoogleLoginProvider extends BaseLoginProvider {\n  static {\n    this.PROVIDER_ID = 'GOOGLE';\n  }\n  constructor(clientId, initOptions) {\n    super();\n    this.clientId = clientId;\n    this.initOptions = initOptions;\n    this.changeUser = new EventEmitter();\n    this._socialUser = new BehaviorSubject(null);\n    this._accessToken = new BehaviorSubject(null);\n    this._receivedAccessToken = new EventEmitter();\n    this.initOptions = {\n      ...defaultInitOptions,\n      ...this.initOptions\n    };\n    // emit changeUser events but skip initial value from behaviorSubject\n    this._socialUser.pipe(skip(1)).subscribe(this.changeUser);\n    // emit receivedAccessToken but skip initial value from behaviorSubject\n    this._accessToken.pipe(skip(1)).subscribe(this._receivedAccessToken);\n  }\n  initialize(autoLogin, lang) {\n    return new Promise((resolve, reject) => {\n      try {\n        this.loadScript(GoogleLoginProvider.PROVIDER_ID, this.getGoogleLoginScriptSrc(lang), () => {\n          if (!isGoogleAccountsDefined()) return;\n          google.accounts.id.initialize({\n            client_id: this.clientId,\n            auto_select: autoLogin,\n            callback: ({\n              credential\n            }) => {\n              const socialUser = this.createSocialUser(credential);\n              this._socialUser.next(socialUser);\n            },\n            prompt_parent_id: this.initOptions?.prompt_parent_id,\n            itp_support: this.initOptions.oneTapEnabled,\n            use_fedcm_for_prompt: this.initOptions.oneTapEnabled\n          });\n          if (this.initOptions.oneTapEnabled) {\n            this._socialUser.pipe(filter(user => user === null)).subscribe(() => google.accounts.id.prompt(console.debug));\n          }\n          if (this.initOptions.scopes) {\n            const scope = this.initOptions.scopes instanceof Array ? this.initOptions.scopes.filter(s => s).join(' ') : this.initOptions.scopes;\n            this._tokenClient = google.accounts.oauth2.initTokenClient({\n              client_id: this.clientId,\n              scope,\n              prompt: this.initOptions.prompt,\n              callback: tokenResponse => {\n                if (tokenResponse.error) {\n                  this._accessToken.error({\n                    code: tokenResponse.error,\n                    description: tokenResponse.error_description,\n                    uri: tokenResponse.error_uri\n                  });\n                } else {\n                  this._accessToken.next(tokenResponse.access_token);\n                }\n              }\n            });\n          }\n          resolve();\n        });\n      } catch (err) {\n        reject(err);\n      }\n    });\n  }\n  getLoginStatus() {\n    return new Promise((resolve, reject) => {\n      if (this._socialUser.value) {\n        resolve(this._socialUser.value);\n      } else {\n        reject(`No user is currently logged in with ${GoogleLoginProvider.PROVIDER_ID}`);\n      }\n    });\n  }\n  refreshToken() {\n    return new Promise((resolve, reject) => {\n      getGoogleAccountsOrThrow().id.revoke(this._socialUser.value.id, response => {\n        if (response.error) reject(response.error);else resolve(this._socialUser.value);\n      });\n    });\n  }\n  getAccessToken() {\n    return new Promise((resolve, reject) => {\n      if (!this._tokenClient) {\n        if (this._socialUser.value) {\n          reject('No token client was instantiated, you should specify some scopes.');\n        } else {\n          reject('You should be logged-in first.');\n        }\n      } else {\n        this._tokenClient.requestAccessToken({\n          hint: this._socialUser.value?.email\n        });\n        this._receivedAccessToken.pipe(take(1)).subscribe(resolve);\n      }\n    });\n  }\n  revokeAccessToken() {\n    return new Promise((resolve, reject) => {\n      if (!this._tokenClient) {\n        reject('No token client was instantiated, you should specify some scopes.');\n      } else if (!this._accessToken.value) {\n        reject('No access token to revoke');\n      } else {\n        getGoogleAccountsOrThrow().oauth2.revoke(this._accessToken.value, () => {\n          this._accessToken.next(null);\n          resolve();\n        });\n      }\n    });\n  }\n  signIn() {\n    return Promise.reject('You should not call this method directly for Google, use \"<asl-google-signin-button>\" wrapper ' + 'or generate the button yourself with \"google.accounts.id.renderButton()\" ' + '(https://developers.google.com/identity/gsi/web/guides/display-button#javascript)');\n  }\n  async signOut() {\n    getGoogleAccountsOrThrow().id.disableAutoSelect();\n    this._socialUser.next(null);\n  }\n  createSocialUser(idToken) {\n    const user = new SocialUser();\n    user.idToken = idToken;\n    const payload = this.decodeJwt(idToken);\n    user.id = payload.sub;\n    user.name = payload.name;\n    user.email = payload.email;\n    user.photoUrl = payload.picture;\n    user.firstName = payload['given_name'];\n    user.lastName = payload['family_name'];\n    return user;\n  }\n  decodeJwt(idToken) {\n    const base64Url = idToken.split(\".\")[1];\n    const base64 = base64Url.replace(/-/g, \"+\").replace(/_/g, \"/\");\n    const jsonPayload = decodeURIComponent(window.atob(base64).split(\"\").map(function (c) {\n      return \"%\" + (\"00\" + c.charCodeAt(0).toString(16)).slice(-2);\n    }).join(\"\"));\n    return JSON.parse(jsonPayload);\n  }\n  getGoogleLoginScriptSrc(lang) {\n    return lang ? `https://accounts.google.com/gsi/client?hl=${lang}` : 'https://accounts.google.com/gsi/client';\n  }\n}\n\n/**\n * The service encapsulating the social login functionality. Exposes methods like\n * `signIn`, `signOut`. Also, exposes an `authState` `Observable` that one can\n * subscribe to get the current logged in user information.\n *\n * @dynamic\n */\nclass SocialAuthService {\n  static {\n    this.ERR_LOGIN_PROVIDER_NOT_FOUND = 'Login provider not found';\n  }\n  static {\n    this.ERR_NOT_LOGGED_IN = 'Not logged in';\n  }\n  static {\n    this.ERR_NOT_INITIALIZED = 'Login providers not ready yet. Are there errors on your console?';\n  }\n  static {\n    this.ERR_NOT_SUPPORTED_FOR_REFRESH_TOKEN = 'Chosen login provider is not supported for refreshing a token';\n  }\n  static {\n    this.ERR_NOT_SUPPORTED_FOR_ACCESS_TOKEN = 'Chosen login provider is not supported for getting an access token';\n  }\n  /** An `Observable` that one can subscribe to get the current logged in user information */\n  get authState() {\n    return this._authState.asObservable();\n  }\n  /** An `Observable` to communicate the readiness of the service and associated login providers */\n  get initState() {\n    return this._initState.asObservable();\n  }\n  /**\n   * @param config A `SocialAuthServiceConfig` object or a `Promise` that resolves to a `SocialAuthServiceConfig` object\n   */\n  constructor(config, _ngZone, _injector) {\n    this._ngZone = _ngZone;\n    this._injector = _injector;\n    this.providers = new Map();\n    this.autoLogin = false;\n    this.lang = '';\n    this._user = null;\n    this._authState = new ReplaySubject(1);\n    /* Consider making this an enum comprising LOADING, LOADED, FAILED etc. */\n    this.initialized = false;\n    this._initState = new AsyncSubject();\n    if (config instanceof Promise) {\n      config.then(config => {\n        this.initialize(config);\n      });\n    } else {\n      this.initialize(config);\n    }\n  }\n  initialize(config) {\n    this.autoLogin = config.autoLogin !== undefined ? config.autoLogin : false;\n    this.lang = config.lang !== undefined ? config.lang : '';\n    const {\n      onError = console.error\n    } = config;\n    config.providers.forEach(item => {\n      this.providers.set(item.id, 'prototype' in item.provider ? this._injector.get(item.provider) : item.provider);\n    });\n    Promise.all(Array.from(this.providers.values()).map(provider => provider.initialize(this.autoLogin, this.lang))).then(() => {\n      if (this.autoLogin) {\n        const loginStatusPromises = [];\n        let loggedIn = false;\n        this.providers.forEach((provider, key) => {\n          const promise = provider.getLoginStatus();\n          loginStatusPromises.push(promise);\n          promise.then(user => {\n            this.setUser(user, key);\n            loggedIn = true;\n          }).catch(console.debug);\n        });\n        Promise.all(loginStatusPromises).catch(() => {\n          if (!loggedIn) {\n            this._user = null;\n            this._authState.next(null);\n          }\n        });\n      }\n      this.providers.forEach((provider, key) => {\n        if (isObservable(provider.changeUser)) {\n          provider.changeUser.subscribe(user => {\n            this._ngZone.run(() => {\n              this.setUser(user, key);\n            });\n          });\n        }\n      });\n    }).catch(error => {\n      onError(error);\n    }).finally(() => {\n      this.initialized = true;\n      this._initState.next(this.initialized);\n      this._initState.complete();\n    });\n  }\n  async getAccessToken(providerId) {\n    const providerObject = this.providers.get(providerId);\n    if (!this.initialized) {\n      throw SocialAuthService.ERR_NOT_INITIALIZED;\n    } else if (!providerObject) {\n      throw SocialAuthService.ERR_LOGIN_PROVIDER_NOT_FOUND;\n    } else if (!(providerObject instanceof GoogleLoginProvider)) {\n      throw SocialAuthService.ERR_NOT_SUPPORTED_FOR_ACCESS_TOKEN;\n    }\n    return await providerObject.getAccessToken();\n  }\n  refreshAuthToken(providerId) {\n    return new Promise((resolve, reject) => {\n      if (!this.initialized) {\n        reject(SocialAuthService.ERR_NOT_INITIALIZED);\n      } else {\n        const providerObject = this.providers.get(providerId);\n        if (providerObject) {\n          if (typeof providerObject.refreshToken !== 'function') {\n            reject(SocialAuthService.ERR_NOT_SUPPORTED_FOR_REFRESH_TOKEN);\n          } else {\n            providerObject.refreshToken().then(user => {\n              this.setUser(user, providerId);\n              resolve();\n            }).catch(err => {\n              reject(err);\n            });\n          }\n        } else {\n          reject(SocialAuthService.ERR_LOGIN_PROVIDER_NOT_FOUND);\n        }\n      }\n    });\n  }\n  refreshAccessToken(providerId) {\n    return new Promise((resolve, reject) => {\n      if (!this.initialized) {\n        reject(SocialAuthService.ERR_NOT_INITIALIZED);\n      } else if (providerId !== GoogleLoginProvider.PROVIDER_ID) {\n        reject(SocialAuthService.ERR_NOT_SUPPORTED_FOR_REFRESH_TOKEN);\n      } else {\n        const providerObject = this.providers.get(providerId);\n        if (providerObject instanceof GoogleLoginProvider) {\n          providerObject.revokeAccessToken().then(resolve).catch(reject);\n        } else {\n          reject(SocialAuthService.ERR_LOGIN_PROVIDER_NOT_FOUND);\n        }\n      }\n    });\n  }\n  /**\n   * A method used to sign in a user with a specific `LoginProvider`.\n   *\n   * @param providerId Id with which the `LoginProvider` has been registered with the service\n   * @param signInOptions Optional `LoginProvider` specific arguments\n   * @returns A `Promise` that resolves to the authenticated user information\n   */\n  signIn(providerId, signInOptions) {\n    return new Promise((resolve, reject) => {\n      if (!this.initialized) {\n        reject(SocialAuthService.ERR_NOT_INITIALIZED);\n      } else {\n        let providerObject = this.providers.get(providerId);\n        if (providerObject) {\n          providerObject.signIn(signInOptions).then(user => {\n            this.setUser(user, providerId);\n            resolve(user);\n          }).catch(err => {\n            reject(err);\n          });\n        } else {\n          reject(SocialAuthService.ERR_LOGIN_PROVIDER_NOT_FOUND);\n        }\n      }\n    });\n  }\n  /**\n   * A method used to sign out the currently loggen in user.\n   *\n   * @param revoke Optional parameter to specify whether a hard sign out is to be performed\n   * @returns A `Promise` that resolves if the operation is successful, rejects otherwise\n   */\n  signOut(revoke = false) {\n    return new Promise((resolve, reject) => {\n      if (!this.initialized) {\n        reject(SocialAuthService.ERR_NOT_INITIALIZED);\n      } else if (!this._user) {\n        reject(SocialAuthService.ERR_NOT_LOGGED_IN);\n      } else {\n        let providerId = this._user.provider;\n        let providerObject = this.providers.get(providerId);\n        if (providerObject) {\n          providerObject.signOut(revoke).then(() => {\n            resolve();\n            this.setUser(null);\n          }).catch(err => {\n            reject(err);\n          });\n        } else {\n          reject(SocialAuthService.ERR_LOGIN_PROVIDER_NOT_FOUND);\n        }\n      }\n    });\n  }\n  setUser(user, id) {\n    if (user && id) user.provider = id;\n    this._user = user;\n    this._authState.next(user);\n  }\n  static {\n    this.ɵfac = function SocialAuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SocialAuthService)(i0.ɵɵinject('SocialAuthServiceConfig'), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SocialAuthService,\n      factory: SocialAuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SocialAuthService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: ['SocialAuthServiceConfig']\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.Injector\n  }], null);\n})();\n\n/**\n * The main module of angularx-social-login library.\n */\nclass SocialLoginModule {\n  static initialize(config) {\n    return {\n      ngModule: SocialLoginModule,\n      providers: [SocialAuthService, {\n        provide: 'SocialAuthServiceConfig',\n        useValue: config\n      }]\n    };\n  }\n  constructor(parentModule) {\n    if (parentModule) {\n      throw new Error('SocialLoginModule is already loaded. Import it in the AppModule only');\n    }\n  }\n  static {\n    this.ɵfac = function SocialLoginModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SocialLoginModule)(i0.ɵɵinject(SocialLoginModule, 12));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: SocialLoginModule,\n      imports: [CommonModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [SocialAuthService],\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SocialLoginModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      providers: [SocialAuthService]\n    }]\n  }], () => [{\n    type: SocialLoginModule,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }], null);\n})();\n\n// Simulates login / logout without actually requiring an Internet connection.\n//\n// Useful for certain development situations.\n//\n// For example, if you want to simulate the greatest football referee England has ever produced:\n//\n//  const dummyUser: SocialUser = {\n//     id: '**********',\n//     name: 'Howard Webb',\n//     email: '<EMAIL>',\n//     firstName: 'Howard',\n//     lastName: 'Webb',\n//     authToken: 'dummyAuthToken',\n//     photoUrl: 'https://en.wikipedia.org/wiki/Howard_Webb#/media/File:Howard_Webb_march11.jpg',\n//     provider: 'DUMMY',\n//     idToken: 'dummyIdToken',\n//     authorizationCode: 'dummyAuthCode'\n// };\n//\n//  let config = new AuthServiceConfig([\n//  { ... },\n//  {\n//       id: DummyLoginProvider.PROVIDER_ID,\n//       provider: new DummyLoginProvider(dummyUser)  // Pass your user into the constructor\n//   },\n//  { ... }\n//  ]);\nclass DummyLoginProvider extends BaseLoginProvider {\n  static {\n    this.PROVIDER_ID = 'DUMMY';\n  }\n  static {\n    this.DEFAULT_USER = {\n      id: '**********',\n      name: 'Mickey Mouse',\n      email: '<EMAIL>',\n      firstName: 'Mickey',\n      lastName: 'Mouse',\n      authToken: 'dummyAuthToken',\n      photoUrl: 'https://en.wikipedia.org/wiki/File:Mickey_Mouse.png',\n      provider: 'DUMMY',\n      idToken: 'dummyIdToken',\n      authorizationCode: 'dummyAuthCode',\n      response: {}\n    };\n  }\n  constructor(dummy) {\n    super();\n    if (dummy) {\n      this.dummy = dummy;\n    } else {\n      this.dummy = DummyLoginProvider.DEFAULT_USER;\n    }\n    // Start not logged in\n    this.loggedIn = false;\n  }\n  getLoginStatus() {\n    return new Promise((resolve, reject) => {\n      if (this.loggedIn) {\n        resolve(this.dummy);\n      } else {\n        reject('No user is currently logged in.');\n      }\n    });\n  }\n  initialize() {\n    return new Promise((resolve, reject) => {\n      resolve();\n    });\n  }\n  signIn() {\n    return new Promise((resolve, reject) => {\n      this.loggedIn = true;\n      resolve(this.dummy);\n    });\n  }\n  signOut(revoke) {\n    return new Promise((resolve, reject) => {\n      this.loggedIn = false;\n      resolve();\n    });\n  }\n}\nclass FacebookLoginProvider extends BaseLoginProvider {\n  static {\n    this.PROVIDER_ID = 'FACEBOOK';\n  }\n  constructor(clientId, initOptions = {}) {\n    super();\n    this.clientId = clientId;\n    this.requestOptions = {\n      scope: 'email,public_profile',\n      locale: 'en_US',\n      fields: 'name,email,picture,first_name,last_name',\n      version: 'v10.0'\n    };\n    this.requestOptions = {\n      ...this.requestOptions,\n      ...initOptions\n    };\n  }\n  initialize() {\n    return new Promise((resolve, reject) => {\n      try {\n        this.loadScript(FacebookLoginProvider.PROVIDER_ID, `//connect.facebook.net/${this.requestOptions.locale}/sdk.js`, () => {\n          FB.init({\n            appId: this.clientId,\n            autoLogAppEvents: true,\n            cookie: true,\n            xfbml: true,\n            version: this.requestOptions.version\n          });\n          resolve();\n        });\n      } catch (err) {\n        reject(err);\n      }\n    });\n  }\n  getLoginStatus() {\n    return new Promise((resolve, reject) => {\n      FB.getLoginStatus(response => {\n        if (response.status === 'connected') {\n          let authResponse = response.authResponse;\n          FB.api(`/me?fields=${this.requestOptions.fields}`, fbUser => {\n            let user = new SocialUser();\n            user.id = fbUser.id;\n            user.name = fbUser.name;\n            user.email = fbUser.email;\n            user.photoUrl = 'https://graph.facebook.com/' + fbUser.id + '/picture?type=normal&access_token=' + authResponse.accessToken;\n            user.firstName = fbUser.first_name;\n            user.lastName = fbUser.last_name;\n            user.authToken = authResponse.accessToken;\n            user.response = fbUser;\n            resolve(user);\n          });\n        } else {\n          reject(`No user is currently logged in with ${FacebookLoginProvider.PROVIDER_ID}`);\n        }\n      });\n    });\n  }\n  signIn(signInOptions) {\n    const options = {\n      ...this.requestOptions,\n      ...signInOptions\n    };\n    return new Promise((resolve, reject) => {\n      FB.login(response => {\n        if (response.authResponse) {\n          let authResponse = response.authResponse;\n          FB.api(`/me?fields=${options.fields}`, fbUser => {\n            let user = new SocialUser();\n            user.id = fbUser.id;\n            user.name = fbUser.name;\n            user.email = fbUser.email;\n            user.photoUrl = 'https://graph.facebook.com/' + fbUser.id + '/picture?type=normal';\n            user.firstName = fbUser.first_name;\n            user.lastName = fbUser.last_name;\n            user.authToken = authResponse.accessToken;\n            user.response = fbUser;\n            resolve(user);\n          });\n        } else {\n          reject('User cancelled login or did not fully authorize.');\n        }\n      }, options);\n    });\n  }\n  signOut() {\n    return new Promise((resolve, reject) => {\n      FB.logout(response => {\n        resolve();\n      });\n    });\n  }\n}\nclass AmazonLoginProvider extends BaseLoginProvider {\n  static {\n    this.PROVIDER_ID = 'AMAZON';\n  }\n  constructor(clientId, initOptions = {\n    scope: 'profile',\n    scope_data: {\n      profile: {\n        essential: false\n      }\n    },\n    redirect_uri: location.origin\n  }) {\n    super();\n    this.clientId = clientId;\n    this.initOptions = initOptions;\n  }\n  initialize() {\n    let amazonRoot = null;\n    if (document) {\n      amazonRoot = document.createElement('div');\n      amazonRoot.id = 'amazon-root';\n      document.body.appendChild(amazonRoot);\n    }\n    window.onAmazonLoginReady = () => {\n      amazon.Login.setClientId(this.clientId);\n    };\n    return new Promise((resolve, reject) => {\n      try {\n        this.loadScript('amazon-login-sdk', 'https://assets.loginwithamazon.com/sdk/na/login1.js', () => {\n          resolve();\n        }, amazonRoot);\n      } catch (err) {\n        reject(err);\n      }\n    });\n  }\n  getLoginStatus() {\n    return new Promise((resolve, reject) => {\n      let token = this.retrieveToken();\n      if (token) {\n        amazon.Login.retrieveProfile(token, response => {\n          if (response.success) {\n            let user = new SocialUser();\n            user.id = response.profile.CustomerId;\n            user.name = response.profile.Name;\n            user.email = response.profile.PrimaryEmail;\n            user.response = response.profile;\n            resolve(user);\n          } else {\n            reject(response.error);\n          }\n        });\n      } else {\n        reject(`No user is currently logged in with ${AmazonLoginProvider.PROVIDER_ID}`);\n      }\n    });\n  }\n  signIn(signInOptions) {\n    const options = {\n      ...this.initOptions,\n      ...signInOptions\n    };\n    return new Promise((resolve, reject) => {\n      amazon.Login.authorize(options, authResponse => {\n        if (authResponse.error) {\n          reject(authResponse.error);\n        } else {\n          amazon.Login.retrieveProfile(authResponse.access_token, response => {\n            let user = new SocialUser();\n            user.id = response.profile.CustomerId;\n            user.name = response.profile.Name;\n            user.email = response.profile.PrimaryEmail;\n            user.authToken = authResponse.access_token;\n            user.response = response.profile;\n            this.persistToken(authResponse.access_token);\n            resolve(user);\n          });\n        }\n      });\n    });\n  }\n  signOut() {\n    return new Promise((resolve, reject) => {\n      try {\n        amazon.Login.logout();\n        this.clearToken();\n        resolve();\n      } catch (err) {\n        reject(err.message);\n      }\n    });\n  }\n  persistToken(token) {\n    localStorage.setItem(`${AmazonLoginProvider.PROVIDER_ID}_token`, token);\n  }\n  retrieveToken() {\n    return localStorage.getItem(`${AmazonLoginProvider.PROVIDER_ID}_token`);\n  }\n  clearToken() {\n    localStorage.removeItem(`${AmazonLoginProvider.PROVIDER_ID}_token`);\n  }\n}\nconst permissionTypes = {\n  notify: 1,\n  friends: 2,\n  photos: 4,\n  audio: 8,\n  video: 16,\n  offers: 32,\n  questions: 64,\n  pages: 128,\n  links: 256,\n  status: 1024,\n  notes: 2048,\n  messages: 4096,\n  wall: 8192,\n  ads: 32768,\n  offline: 65536,\n  docs: 131072,\n  groups: 262144,\n  notifications: 524288,\n  stats: 1048576,\n  email: 4194304,\n  market: 134217728\n};\nclass VKLoginProvider extends BaseLoginProvider {\n  constructor(clientId, initOptions = {\n    fields: 'photo_max,contacts',\n    version: '5.131'\n  }) {\n    super();\n    this.clientId = clientId;\n    this.initOptions = initOptions;\n    this.VK_API_URL = '//vk.com/js/api/openapi.js';\n    this.VK_API_GET_USER = 'users.get';\n  }\n  static {\n    this.PROVIDER_ID = 'VK';\n  }\n  initialize() {\n    return new Promise((resolve, reject) => {\n      try {\n        this.loadScript(VKLoginProvider.PROVIDER_ID, this.VK_API_URL, () => {\n          VK.init({\n            apiId: this.clientId\n          });\n          resolve();\n        });\n      } catch (err) {\n        reject(err);\n      }\n    });\n  }\n  getLoginStatus() {\n    return new Promise(resolve => this.getLoginStatusInternal(resolve));\n  }\n  signIn(permissions) {\n    if (permissions?.includes('offers')) {\n      console.warn('The \"offers\" permission is outdated.');\n    }\n    if (permissions?.includes('questions')) {\n      console.warn('The \"questions\" permission is outdated.');\n    }\n    if (permissions?.includes('messages')) {\n      console.warn('The \"messages\" permission is unavailable for non-standalone applications.');\n    }\n    const scope = permissions?.reduce((accumulator, current) => {\n      const index = Object.keys(permissionTypes).findIndex(pt => pt === current);\n      return index > -1 ? accumulator + permissionTypes[current] : 0;\n    }, 0);\n    return new Promise(resolve => this.signInInternal(resolve, scope));\n  }\n  signOut() {\n    return new Promise(resolve => {\n      VK.Auth.logout(() => {\n        resolve();\n      });\n    });\n  }\n  signInInternal(resolve, scope) {\n    VK.Auth.login(loginResponse => {\n      if (loginResponse.status === 'connected') {\n        this.getUser(loginResponse.session.mid, loginResponse.session.sid, resolve);\n      }\n    }, scope);\n  }\n  getUser(userId, token, resolve) {\n    VK.Api.call(this.VK_API_GET_USER, {\n      user_id: userId,\n      fields: this.initOptions.fields,\n      v: this.initOptions.version\n    }, userResponse => {\n      resolve(this.createUser(Object.assign({}, {\n        token\n      }, userResponse.response[0])));\n    });\n  }\n  getLoginStatusInternal(resolve) {\n    VK.Auth.getLoginStatus(loginResponse => {\n      if (loginResponse.status === 'connected') {\n        this.getUser(loginResponse.session.mid, loginResponse.session.sid, resolve);\n      }\n    });\n  }\n  createUser(response) {\n    const user = new SocialUser();\n    user.id = response.id;\n    user.name = `${response.first_name} ${response.last_name}`;\n    user.photoUrl = response.photo_max;\n    user.authToken = response.token;\n    return user;\n  }\n}\n\n/**\n * Protocol modes supported by MSAL.\n */\nvar ProtocolMode;\n(function (ProtocolMode) {\n  ProtocolMode[\"AAD\"] = \"AAD\";\n  ProtocolMode[\"OIDC\"] = \"OIDC\";\n})(ProtocolMode || (ProtocolMode = {}));\nconst COMMON_AUTHORITY = 'https://login.microsoftonline.com/common/';\n/**\n * Microsoft Authentication using MSAL v2: https://github.com/AzureAD/microsoft-authentication-library-for-js/tree/dev/lib/msal-browser\n */\nclass MicrosoftLoginProvider extends BaseLoginProvider {\n  static {\n    this.PROVIDER_ID = 'MICROSOFT';\n  }\n  constructor(clientId, initOptions) {\n    super();\n    this.clientId = clientId;\n    this.initOptions = {\n      authority: COMMON_AUTHORITY,\n      scopes: ['openid', 'email', 'profile', 'User.Read'],\n      knownAuthorities: [],\n      protocolMode: ProtocolMode.AAD,\n      clientCapabilities: [],\n      cacheLocation: 'sessionStorage'\n    };\n    this.initOptions = {\n      ...this.initOptions,\n      ...initOptions\n    };\n  }\n  initialize() {\n    return new Promise((resolve, reject) => {\n      this.loadScript(MicrosoftLoginProvider.PROVIDER_ID, 'https://alcdn.msauth.net/browser/2.13.1/js/msal-browser.min.js', () => {\n        try {\n          const config = {\n            auth: {\n              clientId: this.clientId,\n              redirectUri: this.initOptions.redirect_uri ?? location.origin,\n              authority: this.initOptions.authority,\n              knownAuthorities: this.initOptions.knownAuthorities,\n              protocolMode: this.initOptions.protocolMode,\n              clientCapabilities: this.initOptions.clientCapabilities\n            },\n            cache: !this.initOptions.cacheLocation ? null : {\n              cacheLocation: this.initOptions.cacheLocation\n            }\n          };\n          this._instance = new msal.PublicClientApplication(config);\n          resolve();\n        } catch (e) {\n          reject(e);\n        }\n      });\n    });\n  }\n  getSocialUser(loginResponse) {\n    return new Promise((resolve, reject) => {\n      //After login, use Microsoft Graph API to get user info\n      let meRequest = new XMLHttpRequest();\n      meRequest.onreadystatechange = () => {\n        if (meRequest.readyState == 4) {\n          try {\n            if (meRequest.status == 200) {\n              let userInfo = JSON.parse(meRequest.responseText);\n              let user = new SocialUser();\n              user.provider = MicrosoftLoginProvider.PROVIDER_ID;\n              user.id = loginResponse.idToken;\n              user.authToken = loginResponse.accessToken;\n              user.name = loginResponse.idTokenClaims.name;\n              user.email = loginResponse.account.username;\n              user.idToken = loginResponse.idToken;\n              user.response = loginResponse;\n              user.firstName = userInfo.givenName;\n              user.lastName = userInfo.surname;\n              resolve(user);\n            } else {\n              reject(`Error retrieving user info: ${meRequest.status}`);\n            }\n          } catch (err) {\n            reject(err);\n          }\n        }\n      };\n      //Microsoft Graph ME Endpoint: https://docs.microsoft.com/en-us/graph/api/user-get?view=graph-rest-1.0&tabs=http\n      meRequest.open('GET', 'https://graph.microsoft.com/v1.0/me');\n      meRequest.setRequestHeader('Authorization', `Bearer ${loginResponse.accessToken}`);\n      try {\n        meRequest.send();\n      } catch (err) {\n        reject(err);\n      }\n    });\n  }\n  async getLoginStatus() {\n    const accounts = this._instance.getAllAccounts();\n    if (accounts?.length > 0) {\n      const loginResponse = await this._instance.ssoSilent({\n        scopes: this.initOptions.scopes,\n        loginHint: accounts[0].username\n      });\n      return await this.getSocialUser(loginResponse);\n    } else {\n      throw `No user is currently logged in with ${MicrosoftLoginProvider.PROVIDER_ID}`;\n    }\n  }\n  async signIn() {\n    const loginResponse = await this._instance.loginPopup({\n      scopes: this.initOptions.scopes,\n      prompt: this.initOptions.prompt\n    });\n    return await this.getSocialUser(loginResponse);\n  }\n  async signOut(revoke) {\n    const accounts = this._instance.getAllAccounts();\n    if (accounts?.length > 0) {\n      await this._instance.logoutPopup({\n        account: accounts[0],\n        postLogoutRedirectUri: this.initOptions.logout_redirect_uri ?? this.initOptions.redirect_uri ?? location.href\n      });\n    }\n  }\n}\nclass GoogleSigninButtonDirective {\n  constructor(el, socialAuthService) {\n    this.type = 'icon';\n    this.size = 'medium';\n    this.text = 'signin_with';\n    this.shape = 'rectangular';\n    this.theme = 'outline';\n    this.logo_alignment = 'left';\n    this.width = 0;\n    this.locale = '';\n    socialAuthService.initState.pipe(take(1)).subscribe(() => {\n      Promise.resolve(this.width).then(value => {\n        if (value > 400 || value < 200 && value != 0) {\n          Promise.reject('Please note .. max-width 400 , min-width 200 ' + '(https://developers.google.com/identity/gsi/web/tools/configurator)');\n        } else if (isGoogleAccountsDefined()) {\n          google.accounts.id.renderButton(el.nativeElement, {\n            type: this.type,\n            size: this.size,\n            text: this.text,\n            width: this.width,\n            shape: this.shape,\n            theme: this.theme,\n            logo_alignment: this.logo_alignment,\n            locale: this.locale\n          });\n        }\n      });\n    });\n  }\n  static {\n    this.ɵfac = function GoogleSigninButtonDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GoogleSigninButtonDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(SocialAuthService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: GoogleSigninButtonDirective,\n      selectors: [[\"asl-google-signin-button\"]],\n      inputs: {\n        type: \"type\",\n        size: \"size\",\n        text: \"text\",\n        shape: \"shape\",\n        theme: \"theme\",\n        logo_alignment: \"logo_alignment\",\n        width: \"width\",\n        locale: \"locale\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GoogleSigninButtonDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: 'asl-google-signin-button',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: SocialAuthService\n  }], {\n    type: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    text: [{\n      type: Input\n    }],\n    shape: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    logo_alignment: [{\n      type: Input\n    }],\n    width: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }]\n  });\n})();\nclass GoogleSigninButtonModule {\n  static {\n    this.ɵfac = function GoogleSigninButtonModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GoogleSigninButtonModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: GoogleSigninButtonModule,\n      imports: [GoogleSigninButtonDirective],\n      exports: [GoogleSigninButtonDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GoogleSigninButtonModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [],\n      exports: [GoogleSigninButtonDirective],\n      imports: [GoogleSigninButtonDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AmazonLoginProvider, BaseLoginProvider, DummyLoginProvider, FacebookLoginProvider, GoogleLoginProvider, GoogleSigninButtonDirective, GoogleSigninButtonModule, MicrosoftLoginProvider, SocialAuthService, SocialLoginModule, SocialUser, VKLoginProvider };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,oBAAN,MAAwB;AAAA,EACtB,cAAc;AAAA,EAAC;AAAA,EACf,WAAW,IAAI,KAAK,QAAQ,gBAAgB,MAAM;AAEhD,QAAI,OAAO,aAAa,eAAe,CAAC,SAAS,eAAe,EAAE,GAAG;AACnE,UAAI,WAAW,SAAS,cAAc,QAAQ;AAC9C,eAAS,QAAQ;AACjB,eAAS,MAAM;AACf,eAAS,SAAS;AAClB,UAAI,CAAC,eAAe;AAClB,wBAAgB,SAAS;AAAA,MAC3B;AACA,oBAAc,YAAY,QAAQ;AAAA,IACpC;AAAA,EACF;AACF;AACA,IAAM,aAAN,MAAiB;AAAC;AAClB,IAAM,0BAA0B,MAAM;AACpC,SAAO,OAAO,OAAO,QAAQ,aAAa;AAC5C;AACA,IAAM,8BAA8B,MAAM;AACxC,MAAI,CAAC,wBAAwB,GAAG;AAC9B,UAAM,IAAI,MAAM,kCAAkC;AAAA,EACpD;AACF;AACA,IAAM,2BAA2B,MAAM;AACrC,8BAA4B;AAC5B,SAAO,OAAO,OAAO;AACvB;AACA,IAAM,qBAAqB;AAAA,EACzB,eAAe;AACjB;AACA,IAAM,sBAAN,MAAM,6BAA4B,kBAAkB;AAAA,EAClD,OAAO;AACL,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,UAAU,aAAa;AACjC,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,cAAc,IAAI,gBAAgB,IAAI;AAC3C,SAAK,eAAe,IAAI,gBAAgB,IAAI;AAC5C,SAAK,uBAAuB,IAAI,aAAa;AAC7C,SAAK,cAAc,kCACd,qBACA,KAAK;AAGV,SAAK,YAAY,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,KAAK,UAAU;AAExD,SAAK,aAAa,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,KAAK,oBAAoB;AAAA,EACrE;AAAA,EACA,WAAW,WAAW,MAAM;AAC1B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI;AACF,aAAK,WAAW,qBAAoB,aAAa,KAAK,wBAAwB,IAAI,GAAG,MAAM;AACzF,cAAI,CAAC,wBAAwB,EAAG;AAChC,iBAAO,SAAS,GAAG,WAAW;AAAA,YAC5B,WAAW,KAAK;AAAA,YAChB,aAAa;AAAA,YACb,UAAU,CAAC;AAAA,cACT;AAAA,YACF,MAAM;AACJ,oBAAM,aAAa,KAAK,iBAAiB,UAAU;AACnD,mBAAK,YAAY,KAAK,UAAU;AAAA,YAClC;AAAA,YACA,kBAAkB,KAAK,aAAa;AAAA,YACpC,aAAa,KAAK,YAAY;AAAA,YAC9B,sBAAsB,KAAK,YAAY;AAAA,UACzC,CAAC;AACD,cAAI,KAAK,YAAY,eAAe;AAClC,iBAAK,YAAY,KAAK,OAAO,UAAQ,SAAS,IAAI,CAAC,EAAE,UAAU,MAAM,OAAO,SAAS,GAAG,OAAO,QAAQ,KAAK,CAAC;AAAA,UAC/G;AACA,cAAI,KAAK,YAAY,QAAQ;AAC3B,kBAAM,QAAQ,KAAK,YAAY,kBAAkB,QAAQ,KAAK,YAAY,OAAO,OAAO,OAAK,CAAC,EAAE,KAAK,GAAG,IAAI,KAAK,YAAY;AAC7H,iBAAK,eAAe,OAAO,SAAS,OAAO,gBAAgB;AAAA,cACzD,WAAW,KAAK;AAAA,cAChB;AAAA,cACA,QAAQ,KAAK,YAAY;AAAA,cACzB,UAAU,mBAAiB;AACzB,oBAAI,cAAc,OAAO;AACvB,uBAAK,aAAa,MAAM;AAAA,oBACtB,MAAM,cAAc;AAAA,oBACpB,aAAa,cAAc;AAAA,oBAC3B,KAAK,cAAc;AAAA,kBACrB,CAAC;AAAA,gBACH,OAAO;AACL,uBAAK,aAAa,KAAK,cAAc,YAAY;AAAA,gBACnD;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AACA,kBAAQ;AAAA,QACV,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,eAAO,GAAG;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,KAAK,YAAY,OAAO;AAC1B,gBAAQ,KAAK,YAAY,KAAK;AAAA,MAChC,OAAO;AACL,eAAO,uCAAuC,qBAAoB,WAAW,EAAE;AAAA,MACjF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AACb,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,+BAAyB,EAAE,GAAG,OAAO,KAAK,YAAY,MAAM,IAAI,cAAY;AAC1E,YAAI,SAAS,MAAO,QAAO,SAAS,KAAK;AAAA,YAAO,SAAQ,KAAK,YAAY,KAAK;AAAA,MAChF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,CAAC,KAAK,cAAc;AACtB,YAAI,KAAK,YAAY,OAAO;AAC1B,iBAAO,mEAAmE;AAAA,QAC5E,OAAO;AACL,iBAAO,gCAAgC;AAAA,QACzC;AAAA,MACF,OAAO;AACL,aAAK,aAAa,mBAAmB;AAAA,UACnC,MAAM,KAAK,YAAY,OAAO;AAAA,QAChC,CAAC;AACD,aAAK,qBAAqB,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,OAAO;AAAA,MAC3D;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,CAAC,KAAK,cAAc;AACtB,eAAO,mEAAmE;AAAA,MAC5E,WAAW,CAAC,KAAK,aAAa,OAAO;AACnC,eAAO,2BAA2B;AAAA,MACpC,OAAO;AACL,iCAAyB,EAAE,OAAO,OAAO,KAAK,aAAa,OAAO,MAAM;AACtE,eAAK,aAAa,KAAK,IAAI;AAC3B,kBAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AACP,WAAO,QAAQ,OAAO,0PAAoQ;AAAA,EAC5R;AAAA,EACM,UAAU;AAAA;AACd,+BAAyB,EAAE,GAAG,kBAAkB;AAChD,WAAK,YAAY,KAAK,IAAI;AAAA,IAC5B;AAAA;AAAA,EACA,iBAAiB,SAAS;AACxB,UAAM,OAAO,IAAI,WAAW;AAC5B,SAAK,UAAU;AACf,UAAM,UAAU,KAAK,UAAU,OAAO;AACtC,SAAK,KAAK,QAAQ;AAClB,SAAK,OAAO,QAAQ;AACpB,SAAK,QAAQ,QAAQ;AACrB,SAAK,WAAW,QAAQ;AACxB,SAAK,YAAY,QAAQ,YAAY;AACrC,SAAK,WAAW,QAAQ,aAAa;AACrC,WAAO;AAAA,EACT;AAAA,EACA,UAAU,SAAS;AACjB,UAAM,YAAY,QAAQ,MAAM,GAAG,EAAE,CAAC;AACtC,UAAM,SAAS,UAAU,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AAC7D,UAAM,cAAc,mBAAmB,OAAO,KAAK,MAAM,EAAE,MAAM,EAAE,EAAE,IAAI,SAAU,GAAG;AACpF,aAAO,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE;AAAA,IAC7D,CAAC,EAAE,KAAK,EAAE,CAAC;AACX,WAAO,KAAK,MAAM,WAAW;AAAA,EAC/B;AAAA,EACA,wBAAwB,MAAM;AAC5B,WAAO,OAAO,6CAA6C,IAAI,KAAK;AAAA,EACtE;AACF;AASA,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO;AACL,SAAK,+BAA+B;AAAA,EACtC;AAAA,EACA,OAAO;AACL,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,OAAO;AACL,SAAK,sCAAsC;AAAA,EAC7C;AAAA,EACA,OAAO;AACL,SAAK,qCAAqC;AAAA,EAC5C;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK,WAAW,aAAa;AAAA,EACtC;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK,WAAW,aAAa;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,QAAQ,SAAS,WAAW;AACtC,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,YAAY,oBAAI,IAAI;AACzB,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,aAAa,IAAI,cAAc,CAAC;AAErC,SAAK,cAAc;AACnB,SAAK,aAAa,IAAI,aAAa;AACnC,QAAI,kBAAkB,SAAS;AAC7B,aAAO,KAAK,CAAAA,YAAU;AACpB,aAAK,WAAWA,OAAM;AAAA,MACxB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,WAAW,MAAM;AAAA,IACxB;AAAA,EACF;AAAA,EACA,WAAW,QAAQ;AACjB,SAAK,YAAY,OAAO,cAAc,SAAY,OAAO,YAAY;AACrE,SAAK,OAAO,OAAO,SAAS,SAAY,OAAO,OAAO;AACtD,UAAM;AAAA,MACJ,UAAU,QAAQ;AAAA,IACpB,IAAI;AACJ,WAAO,UAAU,QAAQ,UAAQ;AAC/B,WAAK,UAAU,IAAI,KAAK,IAAI,eAAe,KAAK,WAAW,KAAK,UAAU,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,IAC9G,CAAC;AACD,YAAQ,IAAI,MAAM,KAAK,KAAK,UAAU,OAAO,CAAC,EAAE,IAAI,cAAY,SAAS,WAAW,KAAK,WAAW,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM;AAC1H,UAAI,KAAK,WAAW;AAClB,cAAM,sBAAsB,CAAC;AAC7B,YAAI,WAAW;AACf,aAAK,UAAU,QAAQ,CAAC,UAAU,QAAQ;AACxC,gBAAM,UAAU,SAAS,eAAe;AACxC,8BAAoB,KAAK,OAAO;AAChC,kBAAQ,KAAK,UAAQ;AACnB,iBAAK,QAAQ,MAAM,GAAG;AACtB,uBAAW;AAAA,UACb,CAAC,EAAE,MAAM,QAAQ,KAAK;AAAA,QACxB,CAAC;AACD,gBAAQ,IAAI,mBAAmB,EAAE,MAAM,MAAM;AAC3C,cAAI,CAAC,UAAU;AACb,iBAAK,QAAQ;AACb,iBAAK,WAAW,KAAK,IAAI;AAAA,UAC3B;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,UAAU,QAAQ,CAAC,UAAU,QAAQ;AACxC,YAAI,aAAa,SAAS,UAAU,GAAG;AACrC,mBAAS,WAAW,UAAU,UAAQ;AACpC,iBAAK,QAAQ,IAAI,MAAM;AACrB,mBAAK,QAAQ,MAAM,GAAG;AAAA,YACxB,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,WAAS;AAChB,cAAQ,KAAK;AAAA,IACf,CAAC,EAAE,QAAQ,MAAM;AACf,WAAK,cAAc;AACnB,WAAK,WAAW,KAAK,KAAK,WAAW;AACrC,WAAK,WAAW,SAAS;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA,EACM,eAAe,YAAY;AAAA;AAC/B,YAAM,iBAAiB,KAAK,UAAU,IAAI,UAAU;AACpD,UAAI,CAAC,KAAK,aAAa;AACrB,cAAM,mBAAkB;AAAA,MAC1B,WAAW,CAAC,gBAAgB;AAC1B,cAAM,mBAAkB;AAAA,MAC1B,WAAW,EAAE,0BAA0B,sBAAsB;AAC3D,cAAM,mBAAkB;AAAA,MAC1B;AACA,aAAO,MAAM,eAAe,eAAe;AAAA,IAC7C;AAAA;AAAA,EACA,iBAAiB,YAAY;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,CAAC,KAAK,aAAa;AACrB,eAAO,mBAAkB,mBAAmB;AAAA,MAC9C,OAAO;AACL,cAAM,iBAAiB,KAAK,UAAU,IAAI,UAAU;AACpD,YAAI,gBAAgB;AAClB,cAAI,OAAO,eAAe,iBAAiB,YAAY;AACrD,mBAAO,mBAAkB,mCAAmC;AAAA,UAC9D,OAAO;AACL,2BAAe,aAAa,EAAE,KAAK,UAAQ;AACzC,mBAAK,QAAQ,MAAM,UAAU;AAC7B,sBAAQ;AAAA,YACV,CAAC,EAAE,MAAM,SAAO;AACd,qBAAO,GAAG;AAAA,YACZ,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,iBAAO,mBAAkB,4BAA4B;AAAA,QACvD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,YAAY;AAC7B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,CAAC,KAAK,aAAa;AACrB,eAAO,mBAAkB,mBAAmB;AAAA,MAC9C,WAAW,eAAe,oBAAoB,aAAa;AACzD,eAAO,mBAAkB,mCAAmC;AAAA,MAC9D,OAAO;AACL,cAAM,iBAAiB,KAAK,UAAU,IAAI,UAAU;AACpD,YAAI,0BAA0B,qBAAqB;AACjD,yBAAe,kBAAkB,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,QAC/D,OAAO;AACL,iBAAO,mBAAkB,4BAA4B;AAAA,QACvD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,YAAY,eAAe;AAChC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,CAAC,KAAK,aAAa;AACrB,eAAO,mBAAkB,mBAAmB;AAAA,MAC9C,OAAO;AACL,YAAI,iBAAiB,KAAK,UAAU,IAAI,UAAU;AAClD,YAAI,gBAAgB;AAClB,yBAAe,OAAO,aAAa,EAAE,KAAK,UAAQ;AAChD,iBAAK,QAAQ,MAAM,UAAU;AAC7B,oBAAQ,IAAI;AAAA,UACd,CAAC,EAAE,MAAM,SAAO;AACd,mBAAO,GAAG;AAAA,UACZ,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,mBAAkB,4BAA4B;AAAA,QACvD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,SAAS,OAAO;AACtB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,CAAC,KAAK,aAAa;AACrB,eAAO,mBAAkB,mBAAmB;AAAA,MAC9C,WAAW,CAAC,KAAK,OAAO;AACtB,eAAO,mBAAkB,iBAAiB;AAAA,MAC5C,OAAO;AACL,YAAI,aAAa,KAAK,MAAM;AAC5B,YAAI,iBAAiB,KAAK,UAAU,IAAI,UAAU;AAClD,YAAI,gBAAgB;AAClB,yBAAe,QAAQ,MAAM,EAAE,KAAK,MAAM;AACxC,oBAAQ;AACR,iBAAK,QAAQ,IAAI;AAAA,UACnB,CAAC,EAAE,MAAM,SAAO;AACd,mBAAO,GAAG;AAAA,UACZ,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,mBAAkB,4BAA4B;AAAA,QACvD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,MAAM,IAAI;AAChB,QAAI,QAAQ,GAAI,MAAK,WAAW;AAChC,SAAK,QAAQ;AACb,SAAK,WAAW,KAAK,IAAI;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,SAAS,yBAAyB,GAAM,SAAY,MAAM,GAAM,SAAY,QAAQ,CAAC;AAAA,IAC9I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,WAAW,QAAQ;AACxB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,mBAAmB;AAAA,QAC7B,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,cAAc;AACxB,QAAI,cAAc;AAChB,YAAM,IAAI,MAAM,sEAAsE;AAAA,IACxF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,SAAS,oBAAmB,EAAE,CAAC;AAAA,IACxF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,iBAAiB;AAAA,MAC7B,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,WAAW,CAAC,iBAAiB;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AA6BH,IAAM,qBAAN,MAAM,4BAA2B,kBAAkB;AAAA,EACjD,OAAO;AACL,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,eAAe;AAAA,MAClB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,mBAAmB;AAAA,MACnB,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EACA,YAAY,OAAO;AACjB,UAAM;AACN,QAAI,OAAO;AACT,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,WAAK,QAAQ,oBAAmB;AAAA,IAClC;AAEA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,iBAAiB;AACf,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,KAAK,UAAU;AACjB,gBAAQ,KAAK,KAAK;AAAA,MACpB,OAAO;AACL,eAAO,iCAAiC;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,aAAa;AACX,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,IACV,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AACP,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,WAAK,WAAW;AAChB,cAAQ,KAAK,KAAK;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,QAAQ;AACd,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,WAAK,WAAW;AAChB,cAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACF;AACA,IAAM,wBAAN,MAAM,+BAA8B,kBAAkB;AAAA,EACpD,OAAO;AACL,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,UAAU,cAAc,CAAC,GAAG;AACtC,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAAA,MACpB,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AACA,SAAK,iBAAiB,kCACjB,KAAK,iBACL;AAAA,EAEP;AAAA,EACA,aAAa;AACX,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI;AACF,aAAK,WAAW,uBAAsB,aAAa,0BAA0B,KAAK,eAAe,MAAM,WAAW,MAAM;AACtH,aAAG,KAAK;AAAA,YACN,OAAO,KAAK;AAAA,YACZ,kBAAkB;AAAA,YAClB,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,SAAS,KAAK,eAAe;AAAA,UAC/B,CAAC;AACD,kBAAQ;AAAA,QACV,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,eAAO,GAAG;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,SAAG,eAAe,cAAY;AAC5B,YAAI,SAAS,WAAW,aAAa;AACnC,cAAI,eAAe,SAAS;AAC5B,aAAG,IAAI,cAAc,KAAK,eAAe,MAAM,IAAI,YAAU;AAC3D,gBAAI,OAAO,IAAI,WAAW;AAC1B,iBAAK,KAAK,OAAO;AACjB,iBAAK,OAAO,OAAO;AACnB,iBAAK,QAAQ,OAAO;AACpB,iBAAK,WAAW,gCAAgC,OAAO,KAAK,uCAAuC,aAAa;AAChH,iBAAK,YAAY,OAAO;AACxB,iBAAK,WAAW,OAAO;AACvB,iBAAK,YAAY,aAAa;AAC9B,iBAAK,WAAW;AAChB,oBAAQ,IAAI;AAAA,UACd,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,uCAAuC,uBAAsB,WAAW,EAAE;AAAA,QACnF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO,eAAe;AACpB,UAAM,UAAU,kCACX,KAAK,iBACL;AAEL,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,SAAG,MAAM,cAAY;AACnB,YAAI,SAAS,cAAc;AACzB,cAAI,eAAe,SAAS;AAC5B,aAAG,IAAI,cAAc,QAAQ,MAAM,IAAI,YAAU;AAC/C,gBAAI,OAAO,IAAI,WAAW;AAC1B,iBAAK,KAAK,OAAO;AACjB,iBAAK,OAAO,OAAO;AACnB,iBAAK,QAAQ,OAAO;AACpB,iBAAK,WAAW,gCAAgC,OAAO,KAAK;AAC5D,iBAAK,YAAY,OAAO;AACxB,iBAAK,WAAW,OAAO;AACvB,iBAAK,YAAY,aAAa;AAC9B,iBAAK,WAAW;AAChB,oBAAQ,IAAI;AAAA,UACd,CAAC;AAAA,QACH,OAAO;AACL,iBAAO,kDAAkD;AAAA,QAC3D;AAAA,MACF,GAAG,OAAO;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,UAAU;AACR,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,SAAG,OAAO,cAAY;AACpB,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,IAAM,sBAAN,MAAM,6BAA4B,kBAAkB;AAAA,EAClD,OAAO;AACL,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,UAAU,cAAc;AAAA,IAClC,OAAO;AAAA,IACP,YAAY;AAAA,MACV,SAAS;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,cAAc,SAAS;AAAA,EACzB,GAAG;AACD,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,aAAa;AACX,QAAI,aAAa;AACjB,QAAI,UAAU;AACZ,mBAAa,SAAS,cAAc,KAAK;AACzC,iBAAW,KAAK;AAChB,eAAS,KAAK,YAAY,UAAU;AAAA,IACtC;AACA,WAAO,qBAAqB,MAAM;AAChC,aAAO,MAAM,YAAY,KAAK,QAAQ;AAAA,IACxC;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI;AACF,aAAK,WAAW,oBAAoB,uDAAuD,MAAM;AAC/F,kBAAQ;AAAA,QACV,GAAG,UAAU;AAAA,MACf,SAAS,KAAK;AACZ,eAAO,GAAG;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,QAAQ,KAAK,cAAc;AAC/B,UAAI,OAAO;AACT,eAAO,MAAM,gBAAgB,OAAO,cAAY;AAC9C,cAAI,SAAS,SAAS;AACpB,gBAAI,OAAO,IAAI,WAAW;AAC1B,iBAAK,KAAK,SAAS,QAAQ;AAC3B,iBAAK,OAAO,SAAS,QAAQ;AAC7B,iBAAK,QAAQ,SAAS,QAAQ;AAC9B,iBAAK,WAAW,SAAS;AACzB,oBAAQ,IAAI;AAAA,UACd,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,eAAO,uCAAuC,qBAAoB,WAAW,EAAE;AAAA,MACjF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,eAAe;AACpB,UAAM,UAAU,kCACX,KAAK,cACL;AAEL,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAO,MAAM,UAAU,SAAS,kBAAgB;AAC9C,YAAI,aAAa,OAAO;AACtB,iBAAO,aAAa,KAAK;AAAA,QAC3B,OAAO;AACL,iBAAO,MAAM,gBAAgB,aAAa,cAAc,cAAY;AAClE,gBAAI,OAAO,IAAI,WAAW;AAC1B,iBAAK,KAAK,SAAS,QAAQ;AAC3B,iBAAK,OAAO,SAAS,QAAQ;AAC7B,iBAAK,QAAQ,SAAS,QAAQ;AAC9B,iBAAK,YAAY,aAAa;AAC9B,iBAAK,WAAW,SAAS;AACzB,iBAAK,aAAa,aAAa,YAAY;AAC3C,oBAAQ,IAAI;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,UAAU;AACR,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI;AACF,eAAO,MAAM,OAAO;AACpB,aAAK,WAAW;AAChB,gBAAQ;AAAA,MACV,SAAS,KAAK;AACZ,eAAO,IAAI,OAAO;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,aAAa,OAAO;AAClB,iBAAa,QAAQ,GAAG,qBAAoB,WAAW,UAAU,KAAK;AAAA,EACxE;AAAA,EACA,gBAAgB;AACd,WAAO,aAAa,QAAQ,GAAG,qBAAoB,WAAW,QAAQ;AAAA,EACxE;AAAA,EACA,aAAa;AACX,iBAAa,WAAW,GAAG,qBAAoB,WAAW,QAAQ;AAAA,EACpE;AACF;AACA,IAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AAAA,EACN,KAAK;AAAA,EACL,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,kBAAN,MAAM,yBAAwB,kBAAkB;AAAA,EAC9C,YAAY,UAAU,cAAc;AAAA,IAClC,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,GAAG;AACD,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,aAAa;AACX,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI;AACF,aAAK,WAAW,iBAAgB,aAAa,KAAK,YAAY,MAAM;AAClE,aAAG,KAAK;AAAA,YACN,OAAO,KAAK;AAAA,UACd,CAAC;AACD,kBAAQ;AAAA,QACV,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,eAAO,GAAG;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,WAAO,IAAI,QAAQ,aAAW,KAAK,uBAAuB,OAAO,CAAC;AAAA,EACpE;AAAA,EACA,OAAO,aAAa;AAClB,QAAI,aAAa,SAAS,QAAQ,GAAG;AACnC,cAAQ,KAAK,sCAAsC;AAAA,IACrD;AACA,QAAI,aAAa,SAAS,WAAW,GAAG;AACtC,cAAQ,KAAK,yCAAyC;AAAA,IACxD;AACA,QAAI,aAAa,SAAS,UAAU,GAAG;AACrC,cAAQ,KAAK,2EAA2E;AAAA,IAC1F;AACA,UAAM,QAAQ,aAAa,OAAO,CAAC,aAAa,YAAY;AAC1D,YAAM,QAAQ,OAAO,KAAK,eAAe,EAAE,UAAU,QAAM,OAAO,OAAO;AACzE,aAAO,QAAQ,KAAK,cAAc,gBAAgB,OAAO,IAAI;AAAA,IAC/D,GAAG,CAAC;AACJ,WAAO,IAAI,QAAQ,aAAW,KAAK,eAAe,SAAS,KAAK,CAAC;AAAA,EACnE;AAAA,EACA,UAAU;AACR,WAAO,IAAI,QAAQ,aAAW;AAC5B,SAAG,KAAK,OAAO,MAAM;AACnB,gBAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,eAAe,SAAS,OAAO;AAC7B,OAAG,KAAK,MAAM,mBAAiB;AAC7B,UAAI,cAAc,WAAW,aAAa;AACxC,aAAK,QAAQ,cAAc,QAAQ,KAAK,cAAc,QAAQ,KAAK,OAAO;AAAA,MAC5E;AAAA,IACF,GAAG,KAAK;AAAA,EACV;AAAA,EACA,QAAQ,QAAQ,OAAO,SAAS;AAC9B,OAAG,IAAI,KAAK,KAAK,iBAAiB;AAAA,MAChC,SAAS;AAAA,MACT,QAAQ,KAAK,YAAY;AAAA,MACzB,GAAG,KAAK,YAAY;AAAA,IACtB,GAAG,kBAAgB;AACjB,cAAQ,KAAK,WAAW,OAAO,OAAO,CAAC,GAAG;AAAA,QACxC;AAAA,MACF,GAAG,aAAa,SAAS,CAAC,CAAC,CAAC,CAAC;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB,SAAS;AAC9B,OAAG,KAAK,eAAe,mBAAiB;AACtC,UAAI,cAAc,WAAW,aAAa;AACxC,aAAK,QAAQ,cAAc,QAAQ,KAAK,cAAc,QAAQ,KAAK,OAAO;AAAA,MAC5E;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW,UAAU;AACnB,UAAM,OAAO,IAAI,WAAW;AAC5B,SAAK,KAAK,SAAS;AACnB,SAAK,OAAO,GAAG,SAAS,UAAU,IAAI,SAAS,SAAS;AACxD,SAAK,WAAW,SAAS;AACzB,SAAK,YAAY,SAAS;AAC1B,WAAO;AAAA,EACT;AACF;AAKA,IAAI;AAAA,CACH,SAAUC,eAAc;AACvB,EAAAA,cAAa,KAAK,IAAI;AACtB,EAAAA,cAAa,MAAM,IAAI;AACzB,GAAG,iBAAiB,eAAe,CAAC,EAAE;AACtC,IAAM,mBAAmB;AAIzB,IAAM,yBAAN,MAAM,gCAA+B,kBAAkB;AAAA,EACrD,OAAO;AACL,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,UAAU,aAAa;AACjC,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,cAAc;AAAA,MACjB,WAAW;AAAA,MACX,QAAQ,CAAC,UAAU,SAAS,WAAW,WAAW;AAAA,MAClD,kBAAkB,CAAC;AAAA,MACnB,cAAc,aAAa;AAAA,MAC3B,oBAAoB,CAAC;AAAA,MACrB,eAAe;AAAA,IACjB;AACA,SAAK,cAAc,kCACd,KAAK,cACL;AAAA,EAEP;AAAA,EACA,aAAa;AACX,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,WAAK,WAAW,wBAAuB,aAAa,kEAAkE,MAAM;AAC1H,YAAI;AACF,gBAAM,SAAS;AAAA,YACb,MAAM;AAAA,cACJ,UAAU,KAAK;AAAA,cACf,aAAa,KAAK,YAAY,gBAAgB,SAAS;AAAA,cACvD,WAAW,KAAK,YAAY;AAAA,cAC5B,kBAAkB,KAAK,YAAY;AAAA,cACnC,cAAc,KAAK,YAAY;AAAA,cAC/B,oBAAoB,KAAK,YAAY;AAAA,YACvC;AAAA,YACA,OAAO,CAAC,KAAK,YAAY,gBAAgB,OAAO;AAAA,cAC9C,eAAe,KAAK,YAAY;AAAA,YAClC;AAAA,UACF;AACA,eAAK,YAAY,IAAI,KAAK,wBAAwB,MAAM;AACxD,kBAAQ;AAAA,QACV,SAAS,GAAG;AACV,iBAAO,CAAC;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,cAAc,eAAe;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,UAAI,YAAY,IAAI,eAAe;AACnC,gBAAU,qBAAqB,MAAM;AACnC,YAAI,UAAU,cAAc,GAAG;AAC7B,cAAI;AACF,gBAAI,UAAU,UAAU,KAAK;AAC3B,kBAAI,WAAW,KAAK,MAAM,UAAU,YAAY;AAChD,kBAAI,OAAO,IAAI,WAAW;AAC1B,mBAAK,WAAW,wBAAuB;AACvC,mBAAK,KAAK,cAAc;AACxB,mBAAK,YAAY,cAAc;AAC/B,mBAAK,OAAO,cAAc,cAAc;AACxC,mBAAK,QAAQ,cAAc,QAAQ;AACnC,mBAAK,UAAU,cAAc;AAC7B,mBAAK,WAAW;AAChB,mBAAK,YAAY,SAAS;AAC1B,mBAAK,WAAW,SAAS;AACzB,sBAAQ,IAAI;AAAA,YACd,OAAO;AACL,qBAAO,+BAA+B,UAAU,MAAM,EAAE;AAAA,YAC1D;AAAA,UACF,SAAS,KAAK;AACZ,mBAAO,GAAG;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAEA,gBAAU,KAAK,OAAO,qCAAqC;AAC3D,gBAAU,iBAAiB,iBAAiB,UAAU,cAAc,WAAW,EAAE;AACjF,UAAI;AACF,kBAAU,KAAK;AAAA,MACjB,SAAS,KAAK;AACZ,eAAO,GAAG;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACM,iBAAiB;AAAA;AACrB,YAAM,WAAW,KAAK,UAAU,eAAe;AAC/C,UAAI,UAAU,SAAS,GAAG;AACxB,cAAM,gBAAgB,MAAM,KAAK,UAAU,UAAU;AAAA,UACnD,QAAQ,KAAK,YAAY;AAAA,UACzB,WAAW,SAAS,CAAC,EAAE;AAAA,QACzB,CAAC;AACD,eAAO,MAAM,KAAK,cAAc,aAAa;AAAA,MAC/C,OAAO;AACL,cAAM,uCAAuC,wBAAuB,WAAW;AAAA,MACjF;AAAA,IACF;AAAA;AAAA,EACM,SAAS;AAAA;AACb,YAAM,gBAAgB,MAAM,KAAK,UAAU,WAAW;AAAA,QACpD,QAAQ,KAAK,YAAY;AAAA,QACzB,QAAQ,KAAK,YAAY;AAAA,MAC3B,CAAC;AACD,aAAO,MAAM,KAAK,cAAc,aAAa;AAAA,IAC/C;AAAA;AAAA,EACM,QAAQ,QAAQ;AAAA;AACpB,YAAM,WAAW,KAAK,UAAU,eAAe;AAC/C,UAAI,UAAU,SAAS,GAAG;AACxB,cAAM,KAAK,UAAU,YAAY;AAAA,UAC/B,SAAS,SAAS,CAAC;AAAA,UACnB,uBAAuB,KAAK,YAAY,uBAAuB,KAAK,YAAY,gBAAgB,SAAS;AAAA,QAC3G,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AACF;AACA,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,YAAY,IAAI,mBAAmB;AACjC,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,iBAAiB;AACtB,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,sBAAkB,UAAU,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACxD,cAAQ,QAAQ,KAAK,KAAK,EAAE,KAAK,WAAS;AACxC,YAAI,QAAQ,OAAO,QAAQ,OAAO,SAAS,GAAG;AAC5C,kBAAQ,OAAO,kHAAuH;AAAA,QACxI,WAAW,wBAAwB,GAAG;AACpC,iBAAO,SAAS,GAAG,aAAa,GAAG,eAAe;AAAA,YAChD,MAAM,KAAK;AAAA,YACX,MAAM,KAAK;AAAA,YACX,MAAM,KAAK;AAAA,YACX,OAAO,KAAK;AAAA,YACZ,OAAO,KAAK;AAAA,YACZ,OAAO,KAAK;AAAA,YACZ,gBAAgB,KAAK;AAAA,YACrB,QAAQ,KAAK;AAAA,UACf,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAAgC,kBAAqB,UAAU,GAAM,kBAAkB,iBAAiB,CAAC;AAAA,IAC5I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,0BAA0B,CAAC;AAAA,MACxC,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA0B;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,2BAA2B;AAAA,MACrC,SAAS,CAAC,2BAA2B;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC;AAAA,MACf,SAAS,CAAC,2BAA2B;AAAA,MACrC,SAAS,CAAC,2BAA2B;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["config", "ProtocolMode"]}