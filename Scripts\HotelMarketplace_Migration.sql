-- Hotel Marketplace Migration Script
-- This script creates all the new tables for the marketplace features

USE HotelMarketplace;
GO

-- Create Room Management Tables
CREATE TABLE [Rooms] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [RoomNumber] NVARCHAR(50) NOT NULL,
    [RoomType] NVARCHAR(50) NOT NULL,
    [Status] NVARCHAR(50) NOT NULL DEFAULT 'Available',
    [Floor] INT NOT NULL,
    [BasePrice] DECIMAL(18,2) NOT NULL,
    [MaxOccupancy] INT NOT NULL,
    [Bedrooms] INT NOT NULL,
    [Bathrooms] INT NOT NULL,
    [SquareFootage] DECIMAL(10,2) NOT NULL,
    [HasBalcony] BIT NOT NULL DEFAULT 0,
    [<PERSON><PERSON><PERSON><PERSON>] BIT NOT NULL DEFAULT 0,
    [HasLivingRoom] BIT NOT NULL DEFAULT 0,
    [Description] NVARCHAR(1000),
    [ViewType] NVARCHAR(500),
    [IsAccessible] BIT NOT NULL DEFAULT 0,
    [AllowsSmoking] BIT NOT NULL DEFAULT 0,
    [AllowsPets] BIT NOT NULL DEFAULT 0,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE
);

CREATE TABLE [RoomAmenities] (
    [RoomId] INT NOT NULL,
    [AmenityId] INT NOT NULL,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    PRIMARY KEY ([RoomId], [AmenityId]),
    FOREIGN KEY ([RoomId]) REFERENCES [Rooms]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([AmenityId]) REFERENCES [Amenities]([Id]) ON DELETE CASCADE
);

CREATE TABLE [RoomImages] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [RoomId] INT NOT NULL,
    [ImageUrl] NVARCHAR(500) NOT NULL,
    [IsPrimary] BIT NOT NULL DEFAULT 0,
    [Category] NVARCHAR(100),
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([RoomId]) REFERENCES [Rooms]([Id]) ON DELETE CASCADE
);

CREATE TABLE [RoomAvailabilities] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [RoomId] INT NOT NULL,
    [Date] DATE NOT NULL,
    [IsAvailable] BIT NOT NULL DEFAULT 1,
    [Price] DECIMAL(18,2) NOT NULL,
    [MinNights] INT NOT NULL DEFAULT 1,
    [MaxNights] INT NOT NULL DEFAULT 30,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([RoomId]) REFERENCES [Rooms]([Id]) ON DELETE CASCADE
);

CREATE TABLE [RoomBookings] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [BookingId] INT NOT NULL,
    [RoomId] INT NOT NULL,
    [CheckInDate] DATETIME2 NOT NULL,
    [CheckOutDate] DATETIME2 NOT NULL,
    [RoomRate] DECIMAL(18,2) NOT NULL,
    [Guests] INT NOT NULL,
    [SpecialRequests] NVARCHAR(MAX),
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([BookingId]) REFERENCES [Bookings]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([RoomId]) REFERENCES [Rooms]([Id]) ON DELETE CASCADE
);

-- Create Corporate Booking Tables
CREATE TABLE [Companies] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [Name] NVARCHAR(200) NOT NULL,
    [TaxId] NVARCHAR(100),
    [Address] NVARCHAR(500),
    [City] NVARCHAR(100),
    [Country] NVARCHAR(100),
    [PostalCode] NVARCHAR(20),
    [PhoneNumber] NVARCHAR(20),
    [Email] NVARCHAR(255),
    [Website] NVARCHAR(255),
    [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    [CreditLimit] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [AvailableCredit] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [PaymentTerms] INT NOT NULL DEFAULT 30,
    [RequiresApproval] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

CREATE TABLE [CorporateUsers] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [UserId] INT NOT NULL,
    [CompanyId] INT NOT NULL,
    [EmployeeId] NVARCHAR(100),
    [Department] NVARCHAR(100),
    [JobTitle] NVARCHAR(100),
    [CanApproveBookings] BIT NOT NULL DEFAULT 0,
    [BookingLimit] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([UserId]) REFERENCES [Users]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([CompanyId]) REFERENCES [Companies]([Id]) ON DELETE CASCADE
);

CREATE TABLE [CorporateRates] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [CompanyId] INT NOT NULL,
    [PropertyId] INT NOT NULL,
    [DiscountPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
    [FixedRate] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [ValidFrom] DATETIME2 NOT NULL,
    [ValidTo] DATETIME2 NOT NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([CompanyId]) REFERENCES [Companies]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE
);

CREATE TABLE [CorporateBookings] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [BookingId] INT NOT NULL,
    [CompanyId] INT NOT NULL,
    [CorporateUserId] INT NOT NULL,
    [ProjectCode] NVARCHAR(100),
    [CostCenter] NVARCHAR(100),
    [BusinessPurpose] NVARCHAR(500),
    [ApprovalStatus] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    [ApprovedBy] INT,
    [ApprovedAt] DATETIME2,
    [ApprovalNotes] NVARCHAR(500),
    [RequiresReceipt] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([BookingId]) REFERENCES [Bookings]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([CompanyId]) REFERENCES [Companies]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([CorporateUserId]) REFERENCES [CorporateUsers]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([ApprovedBy]) REFERENCES [CorporateUsers]([Id])
);

CREATE TABLE [BookingApprovals] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [CorporateBookingId] INT NOT NULL,
    [ApproverId] INT NOT NULL,
    [Status] NVARCHAR(50) NOT NULL,
    [Comments] NVARCHAR(500),
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([CorporateBookingId]) REFERENCES [CorporateBookings]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([ApproverId]) REFERENCES [CorporateUsers]([Id]) ON DELETE CASCADE
);

CREATE TABLE [ExpenseReports] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [CompanyId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [ReportNumber] NVARCHAR(100) NOT NULL,
    [ReportDate] DATETIME2 NOT NULL,
    [TotalAmount] DECIMAL(18,2) NOT NULL,
    [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    [Description] NVARCHAR(500),
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([CompanyId]) REFERENCES [Companies]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([UserId]) REFERENCES [Users]([Id]) ON DELETE CASCADE
);

CREATE TABLE [ExpenseReportItems] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ExpenseReportId] INT NOT NULL,
    [BookingId] INT NOT NULL,
    [Amount] DECIMAL(18,2) NOT NULL,
    [Description] NVARCHAR(200),
    [ExpenseDate] DATETIME2 NOT NULL,
    FOREIGN KEY ([ExpenseReportId]) REFERENCES [ExpenseReports]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([BookingId]) REFERENCES [Bookings]([Id]) ON DELETE CASCADE
);

-- Create Dynamic Pricing Tables
CREATE TABLE [PricingRules] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [Name] NVARCHAR(100) NOT NULL,
    [RuleType] NVARCHAR(50) NOT NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [Priority] INT NOT NULL DEFAULT 1,
    [MinPriceMultiplier] DECIMAL(5,2) NOT NULL DEFAULT 0.5,
    [MaxPriceMultiplier] DECIMAL(5,2) NOT NULL DEFAULT 3.0,
    [ValidFrom] DATETIME2 NOT NULL,
    [ValidTo] DATETIME2 NOT NULL,
    [Conditions] NVARCHAR(1000),
    [AdjustmentPercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
    [FixedAdjustment] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE
);

CREATE TABLE [PricingRuleConditions] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PricingRuleId] INT NOT NULL,
    [ConditionType] NVARCHAR(100) NOT NULL,
    [Operator] NVARCHAR(50) NOT NULL,
    [Value] NVARCHAR(200) NOT NULL,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PricingRuleId]) REFERENCES [PricingRules]([Id]) ON DELETE CASCADE
);

CREATE TABLE [SeasonalPricings] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [SeasonName] NVARCHAR(100) NOT NULL,
    [StartDate] DATETIME2 NOT NULL,
    [EndDate] DATETIME2 NOT NULL,
    [PriceMultiplier] DECIMAL(5,2) NOT NULL DEFAULT 1.0,
    [IsRecurring] BIT NOT NULL DEFAULT 1,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE
);

CREATE TABLE [EventPricings] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [EventName] NVARCHAR(200) NOT NULL,
    [EventStartDate] DATETIME2 NOT NULL,
    [EventEndDate] DATETIME2 NOT NULL,
    [PriceMultiplier] DECIMAL(5,2) NOT NULL DEFAULT 1.0,
    [RadiusKm] DECIMAL(5,2) NOT NULL DEFAULT 10.0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [Description] NVARCHAR(500),
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE
);

CREATE TABLE [CompetitorPricings] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [CompetitorName] NVARCHAR(200) NOT NULL,
    [CompetitorUrl] NVARCHAR(500),
    [CompetitorPrice] DECIMAL(18,2) NOT NULL,
    [PriceDate] DATETIME2 NOT NULL,
    [OurPrice] DECIMAL(18,2) NOT NULL,
    [PriceDifference] DECIMAL(18,2) NOT NULL,
    [DistanceKm] DECIMAL(5,2) NOT NULL,
    [SimilarityScore] INT NOT NULL,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE
);

CREATE TABLE [DemandForecasts] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [ForecastDate] DATETIME2 NOT NULL,
    [PredictedOccupancy] DECIMAL(5,2) NOT NULL,
    [PredictedADR] DECIMAL(18,2) NOT NULL,
    [PredictedRevPAR] DECIMAL(18,2) NOT NULL,
    [ConfidenceScore] DECIMAL(5,2) NOT NULL,
    [Factors] NVARCHAR(1000),
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE
);

CREATE TABLE [PricingStrategies] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [Strategy] NVARCHAR(50) NOT NULL DEFAULT 'Moderate',
    [AutoAdjustPricing] BIT NOT NULL DEFAULT 0,
    [MinOccupancyThreshold] DECIMAL(5,2) NOT NULL DEFAULT 0.3,
    [MaxOccupancyThreshold] DECIMAL(5,2) NOT NULL DEFAULT 0.9,
    [PriceIncreaseStep] DECIMAL(5,2) NOT NULL DEFAULT 0.05,
    [PriceDecreaseStep] DECIMAL(5,2) NOT NULL DEFAULT 0.05,
    [DaysInAdvanceThreshold] INT NOT NULL DEFAULT 7,
    [ConsiderCompetitors] BIT NOT NULL DEFAULT 1,
    [ConsiderEvents] BIT NOT NULL DEFAULT 1,
    [ConsiderSeasons] BIT NOT NULL DEFAULT 1,
    [LastUpdated] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE
);

CREATE TABLE [PriceHistories] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [RoomId] INT,
    [Date] DATETIME2 NOT NULL,
    [Price] DECIMAL(18,2) NOT NULL,
    [BasePrice] DECIMAL(18,2) NOT NULL,
    [AdjustmentAmount] DECIMAL(18,2) NOT NULL,
    [AdjustmentReason] NVARCHAR(200),
    [OccupancyRate] DECIMAL(5,2) NOT NULL DEFAULT 0,
    [BookingsCount] INT NOT NULL DEFAULT 0,
    [Revenue] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([RoomId]) REFERENCES [Rooms]([Id])
);

-- Create indexes for performance
CREATE INDEX IX_Rooms_PropertyId ON [Rooms]([PropertyId]);
CREATE INDEX IX_Rooms_Status ON [Rooms]([Status]);
CREATE INDEX IX_RoomAvailabilities_RoomId_Date ON [RoomAvailabilities]([RoomId], [Date]);
CREATE INDEX IX_CorporateBookings_CompanyId ON [CorporateBookings]([CompanyId]);
CREATE INDEX IX_CorporateBookings_ApprovalStatus ON [CorporateBookings]([ApprovalStatus]);
CREATE INDEX IX_PricingRules_PropertyId ON [PricingRules]([PropertyId]);
CREATE INDEX IX_PriceHistories_PropertyId_Date ON [PriceHistories]([PropertyId], [Date]);

-- Create Housekeeping & Maintenance Tables
CREATE TABLE [Staff] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [FirstName] NVARCHAR(100) NOT NULL,
    [LastName] NVARCHAR(100) NOT NULL,
    [Email] NVARCHAR(255),
    [PhoneNumber] NVARCHAR(20),
    [Department] NVARCHAR(100),
    [Position] NVARCHAR(100),
    [HourlyRate] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [HireDate] DATETIME2 NOT NULL,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

CREATE TABLE [HousekeepingTasks] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [RoomId] INT,
    [AssignedStaffId] INT,
    [Title] NVARCHAR(200) NOT NULL,
    [Description] NVARCHAR(1000),
    [TaskType] NVARCHAR(50) NOT NULL,
    [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    [Priority] NVARCHAR(50) NOT NULL DEFAULT 'Medium',
    [ScheduledDate] DATETIME2 NOT NULL,
    [EstimatedDuration] TIME NOT NULL,
    [StartedAt] DATETIME2,
    [CompletedAt] DATETIME2,
    [Notes] NVARCHAR(1000),
    [CompletionNotes] NVARCHAR(500),
    [ActualDuration] DECIMAL(5,2),
    [RequiresInspection] BIT NOT NULL DEFAULT 0,
    [IsRecurring] BIT NOT NULL DEFAULT 0,
    [RecurrenceIntervalDays] INT,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([RoomId]) REFERENCES [Rooms]([Id]),
    FOREIGN KEY ([AssignedStaffId]) REFERENCES [Staff]([Id])
);

CREATE TABLE [TaskChecklistItems] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [HousekeepingTaskId] INT NOT NULL,
    [Description] NVARCHAR(200) NOT NULL,
    [IsCompleted] BIT NOT NULL DEFAULT 0,
    [IsRequired] BIT NOT NULL DEFAULT 1,
    [SortOrder] INT NOT NULL,
    [CompletedAt] DATETIME2,
    [Notes] NVARCHAR(500),
    FOREIGN KEY ([HousekeepingTaskId]) REFERENCES [HousekeepingTasks]([Id]) ON DELETE CASCADE
);

CREATE TABLE [TaskImages] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [HousekeepingTaskId] INT NOT NULL,
    [ImageUrl] NVARCHAR(500) NOT NULL,
    [Description] NVARCHAR(200),
    [IsBeforeImage] BIT NOT NULL DEFAULT 0,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([HousekeepingTaskId]) REFERENCES [HousekeepingTasks]([Id]) ON DELETE CASCADE
);

CREATE TABLE [RoomMaintenances] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [RoomId] INT NOT NULL,
    [AssignedStaffId] INT,
    [IssueTitle] NVARCHAR(200) NOT NULL,
    [IssueDescription] NVARCHAR(1000),
    [Priority] NVARCHAR(50) NOT NULL DEFAULT 'Medium',
    [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    [ReportedDate] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [ScheduledDate] DATETIME2,
    [CompletedDate] DATETIME2,
    [EstimatedCost] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [ActualCost] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [ResolutionNotes] NVARCHAR(1000),
    [RequiresExternalVendor] BIT NOT NULL DEFAULT 0,
    [VendorName] NVARCHAR(200),
    [AffectsAvailability] BIT NOT NULL DEFAULT 0,
    FOREIGN KEY ([RoomId]) REFERENCES [Rooms]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([AssignedStaffId]) REFERENCES [Staff]([Id])
);

CREATE TABLE [MaintenanceImages] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [RoomMaintenanceId] INT NOT NULL,
    [ImageUrl] NVARCHAR(500) NOT NULL,
    [Description] NVARCHAR(200),
    [IsBeforeImage] BIT NOT NULL DEFAULT 0,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([RoomMaintenanceId]) REFERENCES [RoomMaintenances]([Id]) ON DELETE CASCADE
);

CREATE TABLE [StaffSchedules] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [StaffId] INT NOT NULL,
    [Date] DATE NOT NULL,
    [StartTime] TIME NOT NULL,
    [EndTime] TIME NOT NULL,
    [IsAvailable] BIT NOT NULL DEFAULT 1,
    [Notes] NVARCHAR(200),
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([StaffId]) REFERENCES [Staff]([Id]) ON DELETE CASCADE
);

CREATE TABLE [InventoryItems] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [Name] NVARCHAR(200) NOT NULL,
    [Category] NVARCHAR(100),
    [Unit] NVARCHAR(50),
    [CurrentStock] INT NOT NULL DEFAULT 0,
    [MinimumStock] INT NOT NULL DEFAULT 0,
    [MaximumStock] INT NOT NULL DEFAULT 0,
    [UnitCost] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [Supplier] NVARCHAR(100),
    [LastRestocked] DATETIME2 NOT NULL,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE
);

CREATE TABLE [InventoryTransactions] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [InventoryItemId] INT NOT NULL,
    [StaffId] INT,
    [Quantity] INT NOT NULL,
    [TransactionType] NVARCHAR(50) NOT NULL,
    [Reason] NVARCHAR(500),
    [TransactionDate] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([InventoryItemId]) REFERENCES [InventoryItems]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([StaffId]) REFERENCES [Staff]([Id])
);

-- Create Channel Management Tables
CREATE TABLE [Channels] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [Name] NVARCHAR(100) NOT NULL,
    [ChannelType] NVARCHAR(50) NOT NULL,
    [Status] NVARCHAR(50) NOT NULL DEFAULT 'Active',
    [ApiEndpoint] NVARCHAR(500),
    [ApiKey] NVARCHAR(200),
    [Username] NVARCHAR(200),
    [Password] NVARCHAR(200),
    [CommissionRate] DECIMAL(5,2) NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [AutoSync] BIT NOT NULL DEFAULT 1,
    [SyncIntervalMinutes] INT NOT NULL DEFAULT 60,
    [LastSyncAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Configuration] NVARCHAR(1000),
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

CREATE TABLE [PropertyChannels] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [ChannelId] INT NOT NULL,
    [ChannelPropertyId] NVARCHAR(100),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CommissionOverride] DECIMAL(5,2) NOT NULL DEFAULT 0,
    [SyncRates] BIT NOT NULL DEFAULT 1,
    [SyncInventory] BIT NOT NULL DEFAULT 1,
    [SyncRestrictions] BIT NOT NULL DEFAULT 1,
    [ConnectedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [LastSyncAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [LastSyncStatus] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([ChannelId]) REFERENCES [Channels]([Id]) ON DELETE CASCADE
);

CREATE TABLE [RoomChannelMappings] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyChannelId] INT NOT NULL,
    [RoomId] INT NOT NULL,
    [ChannelRoomId] NVARCHAR(100),
    [ChannelRoomName] NVARCHAR(200),
    [IsActive] BIT NOT NULL DEFAULT 1,
    FOREIGN KEY ([PropertyChannelId]) REFERENCES [PropertyChannels]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([RoomId]) REFERENCES [Rooms]([Id]) ON DELETE CASCADE
);

CREATE TABLE [ChannelRates] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ChannelId] INT NOT NULL,
    [PropertyId] INT NOT NULL,
    [RoomId] INT,
    [Date] DATE NOT NULL,
    [Rate] DECIMAL(18,2) NOT NULL,
    [ChannelRate_Value] DECIMAL(18,2) NOT NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [LastUpdated] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [SyncStatus] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    [SyncedAt] DATETIME2,
    FOREIGN KEY ([ChannelId]) REFERENCES [Channels]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([RoomId]) REFERENCES [Rooms]([Id])
);

CREATE TABLE [ChannelInventories] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ChannelId] INT NOT NULL,
    [PropertyId] INT NOT NULL,
    [RoomId] INT,
    [Date] DATE NOT NULL,
    [AvailableRooms] INT NOT NULL,
    [ChannelAllotment] INT NOT NULL,
    [MinStay] INT NOT NULL DEFAULT 1,
    [MaxStay] INT NOT NULL DEFAULT 30,
    [ClosedToArrival] BIT NOT NULL DEFAULT 0,
    [ClosedToDeparture] BIT NOT NULL DEFAULT 0,
    [IsClosed] BIT NOT NULL DEFAULT 0,
    [LastUpdated] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [SyncStatus] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    [SyncedAt] DATETIME2,
    FOREIGN KEY ([ChannelId]) REFERENCES [Channels]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([RoomId]) REFERENCES [Rooms]([Id])
);

CREATE TABLE [ChannelBookings] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ChannelId] INT NOT NULL,
    [BookingId] INT NOT NULL,
    [ChannelBookingId] NVARCHAR(100),
    [ChannelConfirmationNumber] NVARCHAR(100),
    [ChannelCommission] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [NetRate] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [BookingDate] DATETIME2 NOT NULL,
    [ChannelBookingData] NVARCHAR(1000),
    [SyncStatus] NVARCHAR(50) NOT NULL DEFAULT 'Synced',
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([ChannelId]) REFERENCES [Channels]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([BookingId]) REFERENCES [Bookings]([Id]) ON DELETE CASCADE
);

CREATE TABLE [ChannelSyncLogs] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ChannelId] INT NOT NULL,
    [PropertyId] INT,
    [SyncType] NVARCHAR(100) NOT NULL,
    [Status] NVARCHAR(50) NOT NULL,
    [StartTime] DATETIME2 NOT NULL,
    [EndTime] DATETIME2,
    [RecordsProcessed] INT NOT NULL DEFAULT 0,
    [RecordsSuccessful] INT NOT NULL DEFAULT 0,
    [RecordsFailed] INT NOT NULL DEFAULT 0,
    [ErrorMessage] NVARCHAR(2000),
    [Details] NVARCHAR(4000),
    FOREIGN KEY ([ChannelId]) REFERENCES [Channels]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id])
);

CREATE TABLE [RateParities] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [PropertyId] INT NOT NULL,
    [RoomId] INT,
    [Date] DATE NOT NULL,
    [BaseRate] DECIMAL(18,2) NOT NULL,
    [ChannelRates] NVARCHAR(1000),
    [HasParity] BIT NOT NULL DEFAULT 1,
    [MaxDeviation] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [MinDeviation] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [ParityIssues] NVARCHAR(500),
    [CheckedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([RoomId]) REFERENCES [Rooms]([Id])
);

CREATE TABLE [ChannelPerformances] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ChannelId] INT NOT NULL,
    [PropertyId] INT NOT NULL,
    [Date] DATE NOT NULL,
    [BookingsCount] INT NOT NULL DEFAULT 0,
    [Revenue] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [Commission] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [NetRevenue] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [RoomNights] INT NOT NULL DEFAULT 0,
    [ADR] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [ConversionRate] DECIMAL(5,2) NOT NULL DEFAULT 0,
    [CancellationsCount] INT NOT NULL DEFAULT 0,
    [CancellationRate] DECIMAL(5,2) NOT NULL DEFAULT 0,
    FOREIGN KEY ([ChannelId]) REFERENCES [Channels]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([PropertyId]) REFERENCES [Properties]([Id]) ON DELETE CASCADE
);

-- Create more indexes for performance
CREATE INDEX IX_HousekeepingTasks_PropertyId ON [HousekeepingTasks]([PropertyId]);
CREATE INDEX IX_HousekeepingTasks_Status ON [HousekeepingTasks]([Status]);
CREATE INDEX IX_HousekeepingTasks_ScheduledDate ON [HousekeepingTasks]([ScheduledDate]);
CREATE INDEX IX_RoomMaintenances_RoomId ON [RoomMaintenances]([RoomId]);
CREATE INDEX IX_RoomMaintenances_Status ON [RoomMaintenances]([Status]);
CREATE INDEX IX_InventoryItems_PropertyId ON [InventoryItems]([PropertyId]);
CREATE INDEX IX_ChannelRates_ChannelId_PropertyId_Date ON [ChannelRates]([ChannelId], [PropertyId], [Date]);
CREATE INDEX IX_ChannelInventories_ChannelId_PropertyId_Date ON [ChannelInventories]([ChannelId], [PropertyId], [Date]);
CREATE INDEX IX_ChannelSyncLogs_ChannelId_StartTime ON [ChannelSyncLogs]([ChannelId], [StartTime]);

-- Create Experience Marketplace Tables
CREATE TABLE [ExperienceProviders] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [Name] NVARCHAR(200) NOT NULL,
    [Description] NVARCHAR(1000),
    [Email] NVARCHAR(255),
    [PhoneNumber] NVARCHAR(20),
    [Address] NVARCHAR(500),
    [City] NVARCHAR(100),
    [Country] NVARCHAR(100),
    [Website] NVARCHAR(500),
    [Rating] DECIMAL(3,2) NOT NULL DEFAULT 0,
    [TotalReviews] INT NOT NULL DEFAULT 0,
    [IsVerified] BIT NOT NULL DEFAULT 0,
    [Status] NVARCHAR(50) NOT NULL DEFAULT 'PendingApproval',
    [CommissionRate] DECIMAL(5,2) NOT NULL DEFAULT 0.15,
    [LicenseNumber] NVARCHAR(100),
    [InsuranceNumber] NVARCHAR(100),
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

CREATE TABLE [Experiences] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ProviderId] INT NOT NULL,
    [Title] NVARCHAR(200) NOT NULL,
    [Description] NVARCHAR(2000),
    [ShortDescription] NVARCHAR(500),
    [Category] NVARCHAR(50) NOT NULL,
    [Price] DECIMAL(18,2) NOT NULL,
    [Currency] NVARCHAR(3) NOT NULL DEFAULT 'USD',
    [Duration] INT NOT NULL,
    [MaxParticipants] INT NOT NULL,
    [MinParticipants] INT NOT NULL DEFAULT 1,
    [MinAge] INT NOT NULL DEFAULT 0,
    [MaxAge] INT NOT NULL DEFAULT 100,
    [MeetingPoint] NVARCHAR(500),
    [Latitude] DECIMAL(9,6) NOT NULL,
    [Longitude] DECIMAL(9,6) NOT NULL,
    [Inclusions] NVARCHAR(1000),
    [Exclusions] NVARCHAR(1000),
    [Requirements] NVARCHAR(1000),
    [CancellationPolicy] NVARCHAR(1000),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [RequiresApproval] BIT NOT NULL DEFAULT 0,
    [Rating] DECIMAL(3,2) NOT NULL DEFAULT 0,
    [TotalReviews] INT NOT NULL DEFAULT 0,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([ProviderId]) REFERENCES [ExperienceProviders]([Id]) ON DELETE CASCADE
);

CREATE TABLE [ExperienceImages] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ExperienceId] INT NOT NULL,
    [ImageUrl] NVARCHAR(500) NOT NULL,
    [IsPrimary] BIT NOT NULL DEFAULT 0,
    [Caption] NVARCHAR(200),
    [SortOrder] INT NOT NULL,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([ExperienceId]) REFERENCES [Experiences]([Id]) ON DELETE CASCADE
);

CREATE TABLE [ExperienceProviderImages] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ProviderId] INT NOT NULL,
    [ImageUrl] NVARCHAR(500) NOT NULL,
    [IsPrimary] BIT NOT NULL DEFAULT 0,
    [Caption] NVARCHAR(200),
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([ProviderId]) REFERENCES [ExperienceProviders]([Id]) ON DELETE CASCADE
);

CREATE TABLE [ExperienceAvailabilities] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ExperienceId] INT NOT NULL,
    [Date] DATE NOT NULL,
    [StartTime] TIME NOT NULL,
    [EndTime] TIME NOT NULL,
    [AvailableSpots] INT NOT NULL,
    [BookedSpots] INT NOT NULL DEFAULT 0,
    [Price] DECIMAL(18,2) NOT NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([ExperienceId]) REFERENCES [Experiences]([Id]) ON DELETE CASCADE
);

CREATE TABLE [ExperienceBookings] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ExperienceId] INT NOT NULL,
    [ExperienceAvailabilityId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [PropertyBookingId] INT,
    [Participants] INT NOT NULL,
    [TotalPrice] DECIMAL(18,2) NOT NULL,
    [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    [SpecialRequests] NVARCHAR(1000),
    [ContactName] NVARCHAR(200),
    [ContactEmail] NVARCHAR(255),
    [ContactPhone] NVARCHAR(20),
    [BookingDate] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [ConfirmedAt] DATETIME2,
    [CancelledAt] DATETIME2,
    [CancellationReason] NVARCHAR(500),
    FOREIGN KEY ([ExperienceId]) REFERENCES [Experiences]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([ExperienceAvailabilityId]) REFERENCES [ExperienceAvailabilities]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([UserId]) REFERENCES [Users]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([PropertyBookingId]) REFERENCES [Bookings]([Id])
);

CREATE TABLE [ExperienceBookingParticipants] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ExperienceBookingId] INT NOT NULL,
    [FirstName] NVARCHAR(100) NOT NULL,
    [LastName] NVARCHAR(100) NOT NULL,
    [DateOfBirth] DATE NOT NULL,
    [Email] NVARCHAR(255),
    [PhoneNumber] NVARCHAR(20),
    [SpecialRequirements] NVARCHAR(500),
    FOREIGN KEY ([ExperienceBookingId]) REFERENCES [ExperienceBookings]([Id]) ON DELETE CASCADE
);

CREATE TABLE [ExperienceReviews] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ExperienceId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [ExperienceBookingId] INT NOT NULL,
    [Rating] INT NOT NULL,
    [Comment] NVARCHAR(2000),
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [IsVerified] BIT NOT NULL DEFAULT 0,
    FOREIGN KEY ([ExperienceId]) REFERENCES [Experiences]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([UserId]) REFERENCES [Users]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([ExperienceBookingId]) REFERENCES [ExperienceBookings]([Id]) ON DELETE CASCADE
);

CREATE TABLE [ExperienceTags] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ExperienceId] INT NOT NULL,
    [Tag] NVARCHAR(50) NOT NULL,
    FOREIGN KEY ([ExperienceId]) REFERENCES [Experiences]([Id]) ON DELETE CASCADE
);

CREATE TABLE [ServiceProviders] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [Name] NVARCHAR(200) NOT NULL,
    [ServiceType] NVARCHAR(100),
    [Description] NVARCHAR(1000),
    [Email] NVARCHAR(255),
    [PhoneNumber] NVARCHAR(20),
    [Rating] DECIMAL(3,2) NOT NULL DEFAULT 0,
    [TotalReviews] INT NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

CREATE TABLE [ServiceBookings] (
    [Id] INT PRIMARY KEY IDENTITY(1,1),
    [ServiceProviderId] INT NOT NULL,
    [UserId] INT NOT NULL,
    [PropertyBookingId] INT,
    [ServiceType] NVARCHAR(200),
    [ServiceDetails] NVARCHAR(1000),
    [ServiceDate] DATETIME2 NOT NULL,
    [ServiceTime] TIME,
    [Price] DECIMAL(18,2) NOT NULL,
    [Status] NVARCHAR(50) NOT NULL DEFAULT 'Pending',
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY ([ServiceProviderId]) REFERENCES [ServiceProviders]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([UserId]) REFERENCES [Users]([Id]) ON DELETE CASCADE,
    FOREIGN KEY ([PropertyBookingId]) REFERENCES [Bookings]([Id])
);

-- Create final indexes for Experience Marketplace
CREATE INDEX IX_Experiences_ProviderId ON [Experiences]([ProviderId]);
CREATE INDEX IX_Experiences_Category ON [Experiences]([Category]);
CREATE INDEX IX_Experiences_IsActive ON [Experiences]([IsActive]);
CREATE INDEX IX_ExperienceAvailabilities_ExperienceId_Date ON [ExperienceAvailabilities]([ExperienceId], [Date]);
CREATE INDEX IX_ExperienceBookings_UserId ON [ExperienceBookings]([UserId]);
CREATE INDEX IX_ExperienceBookings_Status ON [ExperienceBookings]([Status]);
CREATE INDEX IX_ExperienceReviews_ExperienceId ON [ExperienceReviews]([ExperienceId]);
CREATE INDEX IX_ExperienceTags_ExperienceId_Tag ON [ExperienceTags]([ExperienceId], [Tag]);

-- Add unique constraints
ALTER TABLE [ExperienceTags] ADD CONSTRAINT UQ_ExperienceTags_ExperienceId_Tag UNIQUE ([ExperienceId], [Tag]);
ALTER TABLE [Rooms] ADD CONSTRAINT UQ_Rooms_PropertyId_RoomNumber UNIQUE ([PropertyId], [RoomNumber]);

PRINT 'Hotel Marketplace database migration completed successfully!';
PRINT 'All tables, indexes, and constraints have been created.';
GO
