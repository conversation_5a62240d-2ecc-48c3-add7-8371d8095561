/* navbar.component.css */
.navbar {
    padding: 1rem 2rem;
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
  }
  
  .navbar-container {
   display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .navbar-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
  }
  
  .airbnb-icon {
    width: 30px;
    height: 30px;
    margin-right: 8px;
  }
  
  .logo-text {
    color: #FF5A5F;
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
  }
  
  .navbar-right {
    display: flex;
    align-items: center;
  }
  
  .host-button {
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 22px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .host-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  .host-button span {
    color: #484848;
    font-size: 0.9rem;
  }


  /* navbar.component.css */
.profile-section {
  margin-left: auto;
  position: relative;
}

.profile-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 20px;
  transition: background-color 0.3s;
}

.profile-btn:hover {
  background-color: #f5f5f5;
}

.profile-pic {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  min-width: 160px;
  z-index: 1000;
}

.dropdown-menu a {
  display: block;
  padding: 10px 16px;
  color: #333;
  text-decoration: none;
}

.dropdown-menu a:hover {
  background-color: #f5f5f5;
}

.auth-buttons {
  margin-left: auto;
  display: flex;
  gap: 12px;
}

.login-btn, .signup-btn {
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
}

.login-btn {
  border: 1px solid #ddd;
}

.signup-btn {
  background-color: #ff385c;
  color: white;
}