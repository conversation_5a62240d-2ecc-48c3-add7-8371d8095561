.booking-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

h3 {
  color: #333;
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 12px;
}

.loading {
  text-align: center;
  margin: 40px 0;
  color: #666;
  font-size: 16px;
}

.spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  margin-bottom: 16px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #4285f4;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.booking-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.booking-table th,
.booking-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.booking-table th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 14px;
  letter-spacing: 0.5px;
}

.booking-table tr:last-child td {
  border-bottom: none;
}

.booking-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.booking-table tr:hover {
  background-color: #f5f7fa;
  transition: background-color 0.2s ease;
}

.booking-table td {
  color: #333;
  font-size: 14px;
}

.no-bookings {
  text-align: center;
  margin: 40px 0;
  color: #666;
  font-size: 16px;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.pagination {
  margin-top: 30px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.pagination button {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.pagination button:hover:not(:disabled) {
  background-color: #f0f0f0;
  border-color: #ccc;
}

.pagination button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background-color: #f5f5f5;
}

.pagination span {
  font-size: 14px;
  color: #666;
  margin: 0 10px;
}

button {
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

button:hover {
  background-color: #3367d6;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-confirmed {
  background-color: #d4edda;
  color: #155724;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .booking-table {
    display: block;
    overflow-x: auto;
  }
  
  .booking-container {
    padding: 16px;
  }
  
  h3 {
    font-size: 20px;
  }
}