.notification-container {
    position: relative;
    margin-right: 15px;
}

.notification-bell {
    position: relative;
    cursor: pointer;
    padding: 5px;
}

.notification-bell i {
    font-size: 18px;
    color: #484848;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ff5a5f;
    color: white;
    border-radius: 50%;
    font-size: 0.7rem;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px;
}

.notification-dropdown {
    position: absolute;
    top: 40px;
    right: -50px;
    width: 300px;
    max-height: 400px;
    overflow-y: auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
}

.notification-header h3 {
    margin: 0;
    font-size: 16px;
}

.mark-read {
    color: #ff5a5f;
    cursor: pointer;
    font-size: 12px;
}

.notification-list {
    max-height: 350px;
    overflow-y: auto;
}

.notification-item {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.notification-item:hover {
    background-color: #f8f8f8;
}

.notification-item.unread {
    background-color: #f0f7ff;
}

.notification-content p {
    margin: 0 0 5px 0;
    font-size: 14px;
    line-height: 1.4;
}

.notification-time {
    font-size: 12px;
    color: #777;
    display: block;
    margin-top: 5px;
}

.notification-sender {
    font-size: 11px;
    color: #777;
    display: block;
    font-style: italic;
}

.empty-notifications {
    padding: 20px 15px;
    text-align: center;
    color: #777;
}