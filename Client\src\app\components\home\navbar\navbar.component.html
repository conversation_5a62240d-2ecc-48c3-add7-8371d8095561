<nav class="navbar" [class.scrolled]="isHeaderScrolled">
  <!-- Right side: Notification, Menu and Profile -->
  <div class="navbar-right">

    <app-notification *ngIf="loggedIn "></app-notification>
    <div *ngIf="loggedIn" class="navbar-icon-container" (click)="navigateToMessages()">
      <i class="fas fa-envelope message-icon"></i>
      <span class="message-notification-badge" *ngIf="unreadMessagesCount > 0">
        {{ unreadMessagesCount > 99 ? '99+' : unreadMessagesCount }}
      </span>
    </div>

    <div class="menu-profile" (click)="toggleDropdown($event)">
      <span class="menu-icon" style="color: #484848;">☰</span>
      <span class="profile-icon" style="color: #484848;">
        <img *ngIf="loggedIn && imageUrl" [src]="imageUrl" alt="Profile" class="profile-image" />
        <div *ngIf="loggedIn && !imageUrl" class="profile-avatar">
        <div class="profile-placeholder">{{ userFirstName ? userFirstName[0] : 'G' }}</div>
        </div>
        <span class="profile-placeholder" *ngIf="!loggedIn">👤</span>
      </span>
    </div>
    <!-- Dropdown Menu -->
    <div class="dropdown" *ngIf="isDropdownOpen">
      <ul>
        <li *ngIf="!loggedIn"><a href="/login">Log in</a></li>
        <li *ngIf="!loggedIn"><a href="/register">Sign up</a></li>
        <li *ngIf="loggedIn && !IsUserGuest"><a href="/host">Host Dashboard</a></li>
        <li *ngIf="loggedIn"><a href='/bookings'>Bookings</a></li>
        <li *ngIf="loggedIn"><a href="/wishlist">Wishlist</a></li>
        <li *ngIf="loggedIn && !IsUserGuest"><a (click)='ProfileClicked()'>Profile</a></li>
        <li *ngIf="loggedIn"><a (click)='editProfileClicked()'>Edit Profile</a></li>
        <li *ngIf="!IsUserGuest"><a href="/host/add-property">Host your home</a></li>
        <li *ngIf="loggedIn"><a (click)="logout()">Logout</a></li>
      </ul>
    </div>
  </div>
</nav>