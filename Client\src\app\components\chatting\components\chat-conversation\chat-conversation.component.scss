.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px 24px;
    padding-bottom: 80px;
    /* Space for input */
    height: calc(100vh - 150px);
    /* Adjust based on your header/input height */
    position: relative;
}

.message-list {
    min-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.chat-header {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    background-color: #fff;
    border-bottom: 1px solid #ebebeb;
    z-index: 10;
    position: sticky;
    top: 0;

    .back-button {
        font-size: 18px;
        margin-right: 16px;
        cursor: pointer;
        color: #717171;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;

        &:hover {
            background-color: #f7f7f7;
        }
    }

    .user-info {
        display: flex;
        align-items: center;
        flex: 1;

        .avatar {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 16px;
            border: 1px solid #ebebeb;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        h2 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #222222;
        }
    }
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    color: #717171;

    .spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #FF385C;
        animation: spin 0.8s linear infinite;
        margin-bottom: 16px;
    }

    p {
        font-size: 16px;
        font-weight: 500;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    padding-bottom: 100px;
    scroll-behavior: smooth;
    background-color: #ffffff;

    &.loading {
        display: none;
    }

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 10px;
    }

    .no-messages {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #717171;
        text-align: center;
        padding: 0 20px;
        flex-direction: column;

        p {
            font-size: 16px;
            margin-top: 16px;
            margin-bottom: 0;
        }
    }

    .message-list {


        .message {
            max-width: 70%;
            margin-bottom: 16px;
            display: flex;
            flex-direction: column;
            animation: fadeIn 0.3s ease;

            @keyframes fadeIn {
                from {
                    opacity: 0;
                    transform: translateY(10px);
                }

                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            &.outgoing {
                align-self: flex-end;

                .message-bubble {
                    background-color: #FF385C;
                    color: white;
                    border-radius: 24px 24px 0 24px;
                }

                .message-info {
                    text-align: right;
                    padding-right: 12px;
                }
            }

            &.incoming {
                align-self: flex-start;

                .message-bubble {
                    background-color: #f7f7f7;
                    color: #222222;
                    border-radius: 24px 24px 24px 0;
                }

                .message-info {
                    text-align: left;
                    padding-left: 12px;
                }
            }

            .message-bubble {
                padding: 12px 16px;
                word-wrap: break-word;
                line-height: 1.5;
                font-size: 16px;
                font-weight: 400;
            }

            .message-info {
                font-size: 12px;
                color: #717171;
                margin-top: 6px;
                display: flex;
                align-items: center;

                .time {
                    margin-right: 6px;
                }

                .read-status {
                    color: #FF385C;
                    font-size: 12px;
                }
            }
        }
    }
}

.input-container {
    padding: 16px 24px;
    background-color: white;
    border-top: 1px solid #ebebeb;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    width: 100%;
    box-sizing: border-box;

    form,
    div.input-container {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0;
    }

    input {
        flex: 1;
        padding: 14px 20px;
        border: 1px solid #ebebeb;
        border-radius: 24px;
        font-size: 16px;
        outline: none;
        transition: all 0.2s ease;
        background-color: #f7f7f7;
        font-family: 'Circular', -apple-system, BlinkMacSystemFont, sans-serif;

        &:focus {
            border-color: #FF385C;
            background-color: #ffffff;
        }

        &::placeholder {
            color: #b0b0b0;
        }
    }

    button {
        background-color: #FF385C;
        color: white;
        border: none;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        min-width: 48px;
        margin-left: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        outline: none;
        position: relative;
        transition: all 0.2s ease;

        &:hover {
            background-color: #e61e4d;
        }

        &:disabled {
            background-color: #dddddd;
            cursor: not-allowed;
        }

        i {
            font-size: 18px;
        }

        .sending-spinner {
            position: absolute;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 0.8s linear infinite;
        }
    }
}