.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    font-size: 26px;
    font-weight: 600;
    color: #222222;
    margin: 0;
  }

  .header-actions {
    display: flex;
    gap: 16px;

    button {
      background: none;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      color: #222222;
      display: flex;
      align-items: center;
      gap: 8px;

      &:hover {
        text-decoration: underline;
      }

      i {
        font-size: 18px;
      }
    }
  }
}

.image-gallery {
  display: flex;
  gap: 12px;
  height: 400px;
  margin-bottom: 48px;
  border-radius: 12px;
  overflow: hidden;

  .main-image {
    flex: 1;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 12px 0 0 12px;
    }
  }

  .secondary-images {
    flex: 1;
    position: relative;

    .image-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 12px;
      height: 100%;

      .gallery-img {
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        &:nth-child(2) img {
          border-radius: 0 12px 0 0;
        }

        &:nth-child(4) img {
          border-radius: 0 0 12px 0;
        }
      }
    }

    .btn-show-all {
      position: absolute;
      bottom: 20px;
      right: 20px;
      background-color: white;
      border: 1px solid #DDDDDD;
      border-radius: 8px;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;

      &:hover {
        background-color: #F7F7F7;
      }
    }
  }
}

.content-container {
  display: grid;
  grid-template-columns: 1.8fr 1fr;
  gap: 80px;
}

.property-info {
  .property-header {
    margin-bottom: 24px;

    h2 {
      font-size: 22px;
      font-weight: 600;
      margin: 0 0 8px 0;
    }

    .property-meta {
      font-size: 16px;
      margin-bottom: 8px;
    }

    .property-rating {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;

      .rating {
        display: flex;
        align-items: center;

        i {
          margin-right: 4px;
        }
      }

      .no-reviews {
        font-weight: 500;
      }
    }
  }

  hr {
    border: none;
    border-top: 1px solid #EBEBEB;
    margin: 24px 0;
  }

  .host-info {
    display: flex;
    align-items: center;
    gap: 16px;

    .host-avatar {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .host-details {
      h3 {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 4px 0;
      }

      p {
        font-size: 14px;
        color: #717171;
        margin: 0;
      }
    }
  }

  .location-info {
    display: flex;
    align-items: center;
    gap: 16px;

    .location-icon {
      font-size: 24px;
      color: #222222;
    }

    .location-details {
      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }

  .amenities-section {
    margin-bottom: 32px;

    h3 {
      font-size: 22px;
      font-weight: 600;
      margin: 0 0 24px 0;
    }

    .amenities-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 24px;

      .amenity {
        display: flex;
        align-items: center;
        gap: 16px;

        .amenity-icon {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            max-width: 100%;
            max-height: 100%;
          }
        }

        .amenity-name {
          font-size: 16px;
        }
      }
    }

    .btn-amenities {
      background: none;
      border: 1px solid #222222;
      border-radius: 8px;
      padding: 13px 23px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      margin-top: 8px;

      &:hover {
        background-color: #F7F7F7;
      }
    }
  }

  .description {
    margin-bottom: 32px;

    h3 {
      font-size: 22px;
      font-weight: 600;
      margin: 0 0 16px 0;
    }

    p {
      font-size: 16px;
      line-height: 1.5;
      color: #222222;
      margin: 0;
    }
  }

  // Where you'll sleep section
  .where-youll-sleep {
    margin-bottom: 48px;

    h3 {
      font-size: 22px;
      font-weight: 600;
      margin: 0 0 24px 0;
    }

    .bedrooms-container {
      display: flex;
      gap: 16px;
      overflow-x: auto;
      padding-bottom: 16px;

      .bedroom-card {
        flex: 0 0 auto;
        width: 220px;
        border: 1px solid #DDDDDD;
        border-radius: 12px;
        overflow: hidden;

        img {
          width: 100%;
          height: 160px;
          object-fit: cover;
        }

        h4 {
          font-size: 16px;
          font-weight: 600;
          margin: 16px 16px 4px 16px;
        }

        p {
          font-size: 14px;
          color: #717171;
          margin: 0 16px 16px 16px;
        }
      }
    }
  }

  // Reviews section
  .reviews-section {
    margin-bottom: 48px;

    h3 {
      font-size: 22px;
      font-weight: 600;
      margin: 0 0 24px 0;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        font-size: 16px;
      }
    }

    .reviews-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 32px 24px;
      margin-bottom: 32px;

      .review-card {
        .reviewer-info {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 8px;

          .reviewer-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            background-color: #F7F7F7;
            display: flex;
            align-items: center;
            justify-content: center;

            .avatar-placeholder {
              font-size: 18px;
              font-weight: 600;
              color: #222222;
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .reviewer-details {
            h4 {
              font-size: 16px;
              font-weight: 600;
              margin: 0 0 2px 0;
            }

            p {
              font-size: 14px;
              color: #717171;
              margin: 0;
            }
          }
        }

        .review-rating {
          margin-bottom: 8px;

          i {
            color: #222222;
            font-size: 12px;
          }
        }

        .review-comment {
          font-size: 14px;
          line-height: 1.5;
          margin: 0 0 8px 0;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          line-clamp: 3;
          -webkit-box-orient: vertical;
        }

        .btn-show-more {
          background: none;
          border: none;
          padding: 0;
          font-size: 14px;
          font-weight: 600;
          text-decoration: underline;
          cursor: pointer;
          color: #222222;
        }
      }
    }

    .btn-all-reviews {
      background: none;
      border: 1px solid #222222;
      border-radius: 8px;
      padding: 13px 23px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;

      &:hover {
        background-color: #F7F7F7;
      }
    }
  }
}

.booking-card {
  background-color: #FFFFFF;
  border: 1px solid #DDDDDD;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0px 6px 16px rgba(0, 0, 0, 0.12);
  height: fit-content;

  .booking-price {
    display: flex;
    align-items: baseline;
    margin-bottom: 24px;

    h2 {
      font-size: 22px;
      font-weight: 600;
      margin: 0;
    }

    .per-night {
      font-size: 16px;
      color: #222222;
      margin-left: 5px;
    }
  }

  // Booking form styles
  .booking-form {
    max-width: 370px;
    border-radius: 12px;
    padding: 24px;

    .dates-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      border: 1px solid #B0B0B0;
      border-radius: 8px 8px 0 0;
      overflow: hidden;
      margin-bottom: 0;
      cursor: pointer;

      .check-in,
      .check-out {
        padding: 12px;
        transition: background-color 0.2s;

        &:hover {
          background-color: #f7f7f7;
        }

        label {
          display: block;
          font-size: 10px;
          font-weight: 800;
          text-transform: uppercase;
          margin-bottom: 8px;
        }

        .date-input {
          font-size: 14px;
        }
      }

      .check-in {
        border-right: 1px solid #B0B0B0;
      }
    }

    .guests-dropdown {
      border: 1px solid #B0B0B0;
      border-top: none;
      border-radius: 0 0 8px 8px;
      padding: 12px;
      margin-bottom: 16px;
      cursor: pointer;

      &:hover {
        background-color: #f7f7f7;
      }

      label {
        display: block;
        font-size: 10px;
        font-weight: 800;
        text-transform: uppercase;
        margin-bottom: 8px;
      }

      .guest-selector {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;

        i {
          font-size: 12px;
        }
      }
    }

    .btn-reserve {
      width: 100%;
      background-color: #E61E4D;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 14px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      margin-bottom: 8px;

      &:hover {
        background-color: #D1173B;
      }
    }

    .no-charge-yet {
      text-align: center;
      font-size: 14px;
      color: #222222;
      margin-bottom: 24px;
    }

    .price-details {
      margin-bottom: 16px;

      .price-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        font-size: 16px;
        text-decoration: underline;
      }
    }

    .total-price {
      display: flex;
      justify-content: space-between;
      font-size: 16px;
      font-weight: 600;
      padding-top: 16px;
      border-top: 1px solid #EBEBEB;
    }
  }

  // Calendar Modal
  .calendar-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .calendar-modal {
    width: 100%;
    max-width: 700px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 8px 28px rgba(0, 0, 0, 0.28);
    overflow: hidden;

    @media (max-width: 768px) {
      width: 90%;
      max-height: 90vh;
      overflow-y: auto;
    }
  }

  .calendar-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid #EBEBEB;

    .selected-dates {

      text-align: center;
      margin-bottom: 20px;

      .nights-count {
        font-size: 22px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .date-range {
        font-size: 16px;
        color: #717171;
      }
    }

    .calendar-tabs {
      display: flex;
      border: 1px solid #B0B0B0;
      border-radius: 8px;
      overflow: hidden;

      .tab {
        flex: 1;
        padding: 12px;
        cursor: pointer;
        transition: background-color 0.2s;

        &:first-child {
          border-right: 1px solid #B0B0B0;
        }

        &.active {
          background-color: #F8F8F8;
        }

        &:hover:not(.active) {
          background-color: #F5F5F5;
        }

        .tab-label {
          font-size: 10px;
          font-weight: 800;
          text-transform: uppercase;
          margin-bottom: 4px;
        }

        .tab-value {
          font-size: 14px;
        }
      }
    }
  }

  .calendar-container {
    padding: 20px 24px;
    position: relative;

    .calendar-navigation {
      position: absolute;
      top: 20px;
      width: 100%;
      left: 0;
      display: flex;
      justify-content: space-between;
      padding: 0 12px;

      .nav-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: white;
        border: 1px solid #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 1;

        &:hover {
          background-color: #F7F7F7;
        }

        i {
          font-size: 12px;
        }
      }
    }

    .calendars-wrapper {
      display: flex;
      gap: 40px;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 24px;
      }
    }

    .calendar {
      flex: 1;

      .month-header {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
        text-align: center;
        padding-top: 8px;
      }

      .weekday-header {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        margin-bottom: 8px;

        .weekday {
          font-size: 12px;
          color: #717171;
          text-align: center;
          padding: 4px 0;
        }
      }

      .days-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 2px;

        .day-cell {
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          position: relative;
          cursor: pointer;

          &:hover:not(.disabled):not(.selected) {
            background-color: #F7F7F7;
          }

          .day-number {
            font-size: 14px;
          }

          &.other-month {
            color: #B0B0B0;
          }

          &.disabled {
            color: #dddddd;
            cursor: default;
            text-decoration: line-through;
          }

          &.selected {
            background-color: #464646;
            color: white;
            z-index: 1;
          }

          &.in-range {
            background-color: #d9d9d9;
          }

          &.start-date:after,
          &.end-date:before {
            content: '';
            position: absolute;
            top: 0;
            height: 100%;
            width: 50%;
            z-index: -1;
          }

          &.start-date:after {
            right: 0;
          }

          &.end-date:before {
            left: 0;
          }
        }

      }
    }
  }

  .calendar-footer {
    display: flex;
    justify-content: space-between;
    padding: 16px 24px;
    border-top: 1px solid #EBEBEB;

    .clear-btn {
      background: none;
      border: none;
      text-decoration: underline;
      cursor: pointer;
      font-size: 16px;
      color: #222222;
      padding: 8px 0;

      &:hover {
        color: #000000;
      }
    }

    .close-btn {
      background-color: #222222;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 8px 16px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;

      &:hover {
        background-color: #000000;
      }
    }
  }
}


.report-listing {
  margin-top: 40px;
  text-align: center;

  a {
    color: #717171;
    text-decoration: underline;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 16px;
    }
  }
}

.loading {
  min-height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  color: #717171;
}

/* Amenities Modal */
.amenities-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 25px;
  border-radius: 10px;
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 0 10px;
}

.amenities-grid-modal {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.amenity-icon-modal {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.close-btn:hover {
  background-color: #f0f0f0;
  border-radius: 40%;

}



/* Description section */
.description {
  margin-bottom: 32px;
}

.description p {
  font-size: 16px;
  line-height: 1.5;
  color: #222222;
  margin: 0;
  white-space: pre-line;
  /* Preserves line breaks from the text */
}

.description p.truncated {
  display: -webkit-box;
  line-clamp: 3;
  /* Show 3 lines max */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.btn-read-more {
  background: none;
  border: none;
  padding: 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #222222;
  cursor: pointer;
  margin-top: 8px;
  display: inline-flex;
  align-items: center;

  &:hover {
    text-decoration: underline;
  }
}

/* Description Modal */
.description-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.description-modal .modal-content {
  background: white;
  padding: 25px;
  border-radius: 10px;
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.description-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.description-modal .modal-body {
  line-height: 1.6;
}

.btn-read-more {
  background: none;
  border: none;
  padding: 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #222222;
  cursor: pointer;
  margin-top: 8px;
  display: inline-flex;
  align-items: center;

  text-decoration: underline;

}











/* Reviews Modal Styles */
.reviews-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  overflow-y: auto;

  .modal-content {
    background-color: #fff;
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .modal-header {
      padding: 24px;
      border-bottom: 1px solid #e4e4e4;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: sticky;
      top: 0;
      background-color: #fff;
      z-index: 1;

      h3 {
        margin: 0;
        font-size: 22px;
        font-weight: 600;

        i {
          color: #FF385C;
        }
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        color: #222;

        &:hover {
          color: #000;
        }
      }
    }

    .modal-body {
      padding: 24px;
      overflow-y: auto;

      .reviews-modal-layout {
        display: flex;
        gap: 40px;

        @media (max-width: 768px) {
          flex-direction: column;
        }

        .rating-stats {
          flex: 1;
          max-width: 300px;

          @media (max-width: 768px) {
            max-width: 100%;
          }

          .overall-rating {
            margin-bottom: 24px;

            h4 {
              font-size: 16px;
              font-weight: 600;
              margin-bottom: 8px;
            }

            .rating-number {
              font-size: 32px;
              font-weight: 600;

              i {
                color: #FF385C;
                margin-right: 8px;
              }
            }
          }

          .rating-bars {
            .rating-bar-item {
              display: flex;
              align-items: center;
              margin-bottom: 12px;

              .rating-label {
                width: 20px;
                margin-right: 12px;
                text-align: right;
              }

              .rating-bar-container {
                flex: 1;
                height: 4px;
                background-color: #e4e4e4;
                border-radius: 2px;
                margin-right: 12px;

                .rating-bar-fill {
                  height: 100%;
                  background-color: #222;
                  border-radius: 2px;
                }
              }

              .rating-count {
                width: 30px;
                color: #717171;
                font-size: 14px;
              }
            }
          }
        }

        .reviews-list {
          flex: 2;
          display: flex;
          flex-direction: column;
          gap: 32px;

          .review-item {
            padding-bottom: 24px;
            border-bottom: 1px solid #e4e4e4;

            &:last-child {
              border-bottom: none;
            }

            .reviewer-info {
              display: flex;
              margin-bottom: 16px;

              .reviewer-avatar {
                margin-right: 12px;

                .avatar-placeholder {
                  width: 40px;
                  height: 40px;
                  border-radius: 50%;
                  background-color: #222;
                  color: #fff;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-weight: 600;
                }
              }

              .reviewer-details {
                h4 {
                  margin: 0 0 4px;
                  font-size: 16px;
                  font-weight: 600;
                }

                p {
                  margin: 0;
                  color: #717171;
                  font-size: 14px;
                }
              }
            }

            .review-rating {
              margin-bottom: 12px;

              i {
                color: #FF385C;
                margin-right: 2px;
              }
            }

            .review-comment-full {
              margin: 0;
              line-height: 1.5;
              white-space: pre-line;
              /* Preserves line breaks in reviews */
            }
          }
        }
      }
    }
  }
}

/* Style for the "Show all reviews" button in the main view */
.btn-all-reviews {
  margin-top: 24px;
  padding: 8px 16px;
  border: 1px solid #222;
  border-radius: 8px;
  background-color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #f7f7f7;
  }
}

/* Also modify the existing review card styles to add a "Show more" button */
.review-card {
  .review-comment {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    max-height: 4.5em;
    /* 3 lines at 1.5 line-height */
    line-height: 1.5;
  }


  .btn-show-more {
    margin-top: 8px;
    background: none;
    border: none;
    padding: 0;
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
    color: #222;

    &:hover {
      color: #000;
    }
  }
}











/* Add these styles to your property-details.component.scss file */

.meet-your-host-section {
  margin: 40px 0;

  .section-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 24px;
  }

  .host-card {
    display: flex;
    background-color: #fff;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  .host-profile {
    flex: 0 0 260px;
    padding: 30px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    border-radius: 16px;

    display: flex;
    flex-direction: column;
    align-items: center;
    height: 40%;
    text-align: center;
    position: relative;

    @media (max-width: 768px) {
      flex: auto;
      padding: 20px;
    }
  }

  .host-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 10px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .verified-badge {
      position: absolute;
      bottom: 10px;
      right: 17px;
      background-color: #FF5A5F;
      color: white;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid white;

      i {
        color: #008489;
        font-size: 16px;
      }
    }
  }

  .host-stats {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 20px;

    .stat-item {
      text-align: center;
      padding: 0 5px;
      flex: 1;

      .stat-value {
        font-size: 20px;
        font-weight: 600;
      }

      .stat-label {
        font-size: 14px;
        color: #717171;
      }
    }
  }

  .host-name {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;

    cursor: pointer;
  }

  .superhost-badge {
    display: inline-flex;
    align-items: center;
    color: #ff385c;
    font-weight: 500;

    i {
      margin-right: 5px;
    }
  }

  .host-info-details {
    flex: 1;
    padding: 30px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 12px;
    }
  }

  .superhost-info {
    margin-bottom: 24px;

    p {
      color: #222;
      line-height: 1.5;
    }
  }

  .co-hosts {
    margin-bottom: 24px;

    .co-host-list {
      display: flex;
      align-items: center;

      .co-host {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 20px;

        img {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          object-fit: cover;
          margin-bottom: 5px;
        }

        span {
          font-size: 14px;
        }
      }
    }
  }

  .host-details-section {
    margin-bottom: 24px;

    .response-details {
      p {
        margin: 5px 0;
      }
    }
  }

  .btn-message-host {
    display: inline-block;
    background-color: #222;
    color: #fff;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    margin-bottom: 24px;

    &:hover {
      background-color: #000;
    }
  }

  .host-attributes {
    margin-bottom: 24px;

    .host-attribute {
      margin: 10px 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 10px;
        width: 24px;
        text-align: center;
      }
    }
  }

  .host-bio {
    margin-bottom: 24px;
    line-height: 1.5;

    .show-more {
      display: inline-block;
      color: #222;
      font-weight: 600;
      cursor: pointer;
      text-decoration: underline;
      margin-top: 5px;
    }
  }

  .airbnb-protection-note {
    padding: 15px 0;
    border-top: 1px solid #eee;
    color: #717171;
    display: flex;
    align-items: center;

    i {
      margin-right: 10px;
      color: #ff385c;
    }
  }
}

/* Modal styles */
.host-bio-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .modal-content {
    background-color: #fff;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 85vh;
    overflow-y: auto;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid #eee;

      h3 {
        font-size: 20px;
        font-weight: 600;
        margin: 0;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        color: #717171;
      }
    }

    .modal-body {
      padding: 24px;

      p {
        line-height: 1.6;
        margin-bottom: 24px;
      }

      .host-details-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;

        .host-detail-item {
          .detail-label {
            font-weight: 600;
            margin-bottom: 5px;
          }

          .detail-value {
            color: #717171;
          }
        }
      }
    }
  }
}

.wishlist-btn {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 0, 0, 0.1);
  }

  .fa-heart {
    font-size: 1.2rem;

    &.fas {
      /* Solid heart */
      color: #ff385c;
      /* Airbnb red */
    }

    &.far {
      /* Regular heart */
      color: #717171;
    }
  }

  &.active {
    .fa-heart {
      color: #ff385c;
    }
  }
}

/* Add to your property-details.component.scss */

.property-map-section {
  margin: 40px 0;

  .section-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .location-text {
    margin-bottom: 16px;
    font-size: 16px;
  }

  .map-container {
    position: relative;
    border-radius: 12px;
    overflow: hidden;

    google-map {
      width: 100%;
      height: 400px;
      border-radius: 12px;
    }

    .location-notice {
      position: absolute;
      top: 40px;
      left: 50%;
      transform: translateX(-50%);
      background-color: white;
      padding: 10px 16px;
      border-radius: 18px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      font-size: 14px;
      z-index: 1;
    }
  }
}

/* Add this to customize Google Maps controls */
:host ::ng-deep {

  .gm-style-mtc,
  .gm-fullscreen-control {
    display: none !important;
  }

  .gm-control-active.gm-fullscreen-control {
    display: none !important;
  }

  .gm-style .gm-style-iw-c {
    padding: 12px !important;
    border-radius: 8px !important;
  }

  /* Custom Zoom Controls */
  .gm-bundled-control {
    top: auto !important;
    bottom: 24px !important;
    right: 24px !important;
  }

  .gm-style-cc {
    display: none !important;
  }
}












/* Add these styles to your property-details.component.scss file */

.property-map-section {
  margin: 30px 0;

  .section-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .location-text {
    margin-bottom: 15px;
    color: #333;
  }

  .map-container {
    position: relative;
    border-radius: 30px;
    overflow: hidden;

    #map {
      width: 100%;
      height: 400px;
      z-index: 1;
    }

    .location-notice {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background-color: white;
      padding: 10px 15px;
      border-radius: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      font-size: 14px;
      z-index: 2;
    }
  }
}

// .day-cell.unavailable {
//   text-decoration: line-through;
//   color: #ff6b6b;
//   cursor: not-allowed;
// }

// .day-cell.disabled {
//   color: #ccc;
//   cursor: not-allowed;
//   text-decoration: line-through;
// }

// .day-cell.unavailable {
//   color: #ff6b6b;
//   cursor: not-allowed;
//   position: relative;
// }

// .day-cell.unavailable::after {
//   content: "";
//   position: absolute;
//   width: 80%;
//   height: 1px;
//   background-color: #ff6b6b;
//   transform: rotate(45deg);
// }

.guests-dropdown {
  position: relative;

  label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 4px;
    text-transform: uppercase;
  }

  .guest-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    cursor: pointer;

    &:hover {
      border-color: #b0b0b0;
    }

    i {
      font-size: 12px;
      color: #717171;
    }
  }

  .guest-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 8px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.12);
    padding: 16px;
    z-index: 10;

    .guest-type {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;

      .guest-label {
        .guest-title {
          font-weight: 500;
          margin-bottom: 4px;
        }

        .guest-subtitle {
          font-size: 14px;
          color: #717171;
        }
      }

      .guest-counter {
        display: flex;
        align-items: center;

        .counter-btn {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: 1px solid #b0b0b0;
          background: white;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          &:hover:not(.disabled) {
            border-color: #222;
          }

          &.disabled {
            opacity: 0.4;
            cursor: not-allowed;
          }

          i {
            font-size: 12px;
          }
        }

        .guest-count {
          padding: 0 16px;
          font-size: 16px;
          min-width: 24px;
          text-align: center;
        }
      }
    }

    .dropdown-footer {
      display: flex;
      justify-content: flex-end;
      padding-top: 12px;
      border-top: 1px solid #ebebeb;
      margin-top: 8px;

      .close-dropdown {
        background: none;
        border: none;

        text-decoration: underline;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        padding: 8px;

        &:hover {
          color: #000;
        }
      }
    }
  }
}


/* Add these styles to your component's SCSS file */

.promo-code-section {
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ddd;
}

.promo-code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f8f8;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
  }

  span {
    font-size: 14px;
    font-weight: 500;
    color: #222;
  }
}

.promo-code-content {
  padding: 16px;
  border-top: 1px solid #eee;
  background-color: #fff;
}

.promo-input-container {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;

  &.success {
    border-color: #34c759;
  }

  &.error {
    border-color: #ff3b30;
  }
}

.promo-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 14px;

  &:disabled {
    background-color: #f8f8f8;
  }
}

.promo-apply-btn {
  padding: 0 16px;
  background-color: #222;
  color: #fff;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background-color: #000;
  }

  &:disabled {
    background-color: #bbb;
    cursor: not-allowed;
  }

  &.applied {
    background-color: #34c759;
  }
}

.promo-error-message {
  color: #ff3b30;
  font-size: 12px;
}

.promo-success {
  margin-top: 16px;
  padding: 12px;
  background-color: rgba(52, 199, 89, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.promo-badge {
  display: flex;
  align-items: center;
  background-color: #34c759;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  i {
    margin-right: 4px;
  }
}

.promo-info {
  display: flex;
  align-items: center;

  .discount-info {
    font-size: 14px;
    margin-right: 12px;
  }
}

.remove-promo {
  background-color: transparent;
  border: none;
  color: #555;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.discount-row {
  color: #34c759;
  font-weight: 500;

  .discount-amount {
    color: #34c759;
  }
}

/* property-details.component.css */

.toast-container {
  position: fixed;
  top: 1.5rem;
  left: 1.5rem;
  z-index: 1000;
}

.toast {
  background-color: #fff8f4;
  color: #222;
  border: 1px solid #f7c59f;
  margin-top: 0.5rem;
  padding: 1rem 1.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-family: 'Airbnb Cereal App', 'Helvetica Neue', sans-serif;
  font-size: 0.95rem;
  display: inline-block;
  max-width: 300px;
  animation: fadeIn 0.3s ease-out;
}

.toast a {
  color: #ff385c;
  font-weight: 600;
  text-decoration: none;
  margin-left: 0.5rem;
}

.toast a:hover {
  text-decoration: underline;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}


/* Host Restriction Styling */
.booking-card.host-view {
  position: relative;
  overflow: hidden;
}

.booking-card.host-view::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
}

.host-message-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2;
  padding: 24px;
  text-align: center;
}

.host-message-ribbon {
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  overflow: hidden;
  z-index: 3;
}

.host-message-ribbon::before {
  content: 'HOST VIEW';
  position: absolute;
  display: block;
  width: 225px;
  padding: 15px 0;
  background-color: #E61E4D;
  color: white;
  font-weight: bold;
  font-size: 14px;
  text-align: center;
  transform: rotate(45deg);
  right: -25px;
  top: 30px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.pending-message-ribbon {
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  overflow: hidden;
  z-index: 3;
}

.pending-message-ribbon::before {
  content: 'UNDER REVIEW';
  position: absolute;
  display: block;
  width: 225px;
  padding: 15px 0;
  background-color: #E61E4D;
  color: white;
  font-weight: bold;
  font-size: 16px;
  text-align: center;
  transform: rotate(45deg);
  right: -25px;
  top: 30px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.host-message-content {
  background-color: white;
  border-radius: 12px;
  padding: 32px 24px;
  box-shadow: 0px 6px 16px rgba(0, 0, 0, 0.12);
  max-width: 80%;
}

.host-message-icon {
  font-size: 48px;
  color: #E61E4D;
  margin-bottom: 16px;
}

.host-message-title {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #222222;
}

.host-message-text {
  font-size: 16px;
  color: #717171;
  margin-bottom: 24px;
}

.host-message-button {
  background-color: #222222;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.host-message-button:hover {
  background-color: #000000;
}

.host-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
}

/* Optional: Additional styling adjustments for the report button if needed */
::ng-deep .host-actions .report-btn {
  font-size: 14px;
  padding: 8px 12px;
}

.property-footer {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
}

.edit-delete-icons {
  display: flex;
  gap: 3px;
  align-items: center;
  margin-left: auto;
}

.edit-delete-icons i {
  font-size: 19px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: #ff385c;
  }
}