using System.ComponentModel.DataAnnotations;

namespace API.Models
{
    public enum TaskStatus
    {
        Pending,
        InProgress,
        Completed,
        Cancelled,
        OnHold
    }

    public enum TaskPriority
    {
        Low,
        Medium,
        High,
        Urgent
    }

    public enum TaskType
    {
        Cleaning,
        Maintenance,
        Inspection,
        Setup,
        Inventory,
        Repair
    }

    public class Staff
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; }
        
        [Required]
        [StringLength(100)]
        public string LastName { get; set; }
        
        [StringLength(255)]
        public string Email { get; set; }
        
        [StringLength(20)]
        public string PhoneNumber { get; set; }
        
        [StringLength(100)]
        public string Department { get; set; }
        
        [StringLength(100)]
        public string Position { get; set; }
        
        public decimal HourlyRate { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime HireDate { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual ICollection<HousekeepingTask> AssignedTasks { get; set; } = new List<HousekeepingTask>();
        public virtual ICollection<RoomMaintenance> MaintenanceTasks { get; set; } = new List<RoomMaintenance>();
        public virtual ICollection<StaffSchedule> Schedules { get; set; } = new List<StaffSchedule>();
    }

    public class HousekeepingTask
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        public int? RoomId { get; set; }
        public int? AssignedStaffId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; }
        
        [StringLength(1000)]
        public string Description { get; set; }
        
        public TaskType TaskType { get; set; }
        
        public TaskStatus Status { get; set; } = TaskStatus.Pending;
        
        public TaskPriority Priority { get; set; } = TaskPriority.Medium;
        
        public DateTime ScheduledDate { get; set; }
        
        public TimeSpan EstimatedDuration { get; set; }
        
        public DateTime? StartedAt { get; set; }
        
        public DateTime? CompletedAt { get; set; }
        
        [StringLength(1000)]
        public string Notes { get; set; }
        
        [StringLength(500)]
        public string CompletionNotes { get; set; }
        
        public decimal? ActualDuration { get; set; } // in hours
        
        public bool RequiresInspection { get; set; }
        
        public bool IsRecurring { get; set; }
        
        public int? RecurrenceIntervalDays { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Property Property { get; set; }
        public virtual Room Room { get; set; }
        public virtual Staff AssignedStaff { get; set; }
        public virtual ICollection<TaskChecklistItem> ChecklistItems { get; set; } = new List<TaskChecklistItem>();
        public virtual ICollection<TaskImage> TaskImages { get; set; } = new List<TaskImage>();
    }

    public class TaskChecklistItem
    {
        public int Id { get; set; }
        public int HousekeepingTaskId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Description { get; set; }
        
        public bool IsCompleted { get; set; }
        
        public bool IsRequired { get; set; } = true;
        
        public int SortOrder { get; set; }
        
        public DateTime? CompletedAt { get; set; }
        
        [StringLength(500)]
        public string Notes { get; set; }

        // Navigation Properties
        public virtual HousekeepingTask HousekeepingTask { get; set; }
    }

    public class TaskImage
    {
        public int Id { get; set; }
        public int HousekeepingTaskId { get; set; }
        
        [Required]
        [StringLength(500)]
        public string ImageUrl { get; set; }
        
        [StringLength(200)]
        public string Description { get; set; }
        
        public bool IsBeforeImage { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual HousekeepingTask HousekeepingTask { get; set; }
    }

    public class RoomMaintenance
    {
        public int Id { get; set; }
        public int RoomId { get; set; }
        public int? AssignedStaffId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string IssueTitle { get; set; }
        
        [StringLength(1000)]
        public string IssueDescription { get; set; }
        
        public TaskPriority Priority { get; set; } = TaskPriority.Medium;
        
        public TaskStatus Status { get; set; } = TaskStatus.Pending;
        
        public DateTime ReportedDate { get; set; } = DateTime.UtcNow;
        
        public DateTime? ScheduledDate { get; set; }
        
        public DateTime? CompletedDate { get; set; }
        
        public decimal EstimatedCost { get; set; }
        
        public decimal ActualCost { get; set; }
        
        [StringLength(1000)]
        public string ResolutionNotes { get; set; }
        
        public bool RequiresExternalVendor { get; set; }
        
        [StringLength(200)]
        public string VendorName { get; set; }
        
        public bool AffectsAvailability { get; set; }

        // Navigation Properties
        public virtual Room Room { get; set; }
        public virtual Staff AssignedStaff { get; set; }
        public virtual ICollection<MaintenanceImage> MaintenanceImages { get; set; } = new List<MaintenanceImage>();
    }

    public class MaintenanceImage
    {
        public int Id { get; set; }
        public int RoomMaintenanceId { get; set; }
        
        [Required]
        [StringLength(500)]
        public string ImageUrl { get; set; }
        
        [StringLength(200)]
        public string Description { get; set; }
        
        public bool IsBeforeImage { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual RoomMaintenance RoomMaintenance { get; set; }
    }

    public class StaffSchedule
    {
        public int Id { get; set; }
        public int StaffId { get; set; }
        
        public DateTime Date { get; set; }
        
        public TimeSpan StartTime { get; set; }
        
        public TimeSpan EndTime { get; set; }
        
        public bool IsAvailable { get; set; } = true;
        
        [StringLength(200)]
        public string Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Staff Staff { get; set; }
    }

    public class InventoryItem
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Name { get; set; }
        
        [StringLength(100)]
        public string Category { get; set; }
        
        [StringLength(50)]
        public string Unit { get; set; } // pieces, bottles, kg, etc.
        
        public int CurrentStock { get; set; }
        
        public int MinimumStock { get; set; }
        
        public int MaximumStock { get; set; }
        
        public decimal UnitCost { get; set; }
        
        [StringLength(100)]
        public string Supplier { get; set; }
        
        public DateTime LastRestocked { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Property Property { get; set; }
        public virtual ICollection<InventoryTransaction> Transactions { get; set; } = new List<InventoryTransaction>();
    }

    public class InventoryTransaction
    {
        public int Id { get; set; }
        public int InventoryItemId { get; set; }
        public int? StaffId { get; set; }
        
        public int Quantity { get; set; }
        
        [StringLength(50)]
        public string TransactionType { get; set; } // In, Out, Adjustment
        
        [StringLength(500)]
        public string Reason { get; set; }
        
        public DateTime TransactionDate { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual InventoryItem InventoryItem { get; set; }
        public virtual Staff Staff { get; set; }
    }
}
