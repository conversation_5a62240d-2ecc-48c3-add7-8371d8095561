using System.ComponentModel.DataAnnotations;

namespace API.Models
{
    public enum PricingRuleType
    {
        Seasonal,
        EventBased,
        DemandBased,
        CompetitorBased,
        LastMinute,
        EarlyBird,
        WeekendPremium,
        LengthOfStay
    }

    public enum PricingStrategy
    {
        Conservative,
        Moderate,
        Aggressive,
        Custom
    }

    public class PricingRule
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; }
        
        public PricingRuleType RuleType { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public int Priority { get; set; } = 1;
        
        public decimal MinPriceMultiplier { get; set; } = 0.5m;
        
        public decimal MaxPriceMultiplier { get; set; } = 3.0m;
        
        public DateTime ValidFrom { get; set; }
        
        public DateTime ValidTo { get; set; }
        
        [StringLength(1000)]
        public string Conditions { get; set; } // JSON string with conditions
        
        public decimal AdjustmentPercentage { get; set; }
        
        public decimal FixedAdjustment { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Property Property { get; set; }
        public virtual ICollection<PricingRuleCondition> Conditions_Navigation { get; set; } = new List<PricingRuleCondition>();
    }

    public class PricingRuleCondition
    {
        public int Id { get; set; }
        public int PricingRuleId { get; set; }
        
        [StringLength(100)]
        public string ConditionType { get; set; } // OccupancyRate, DaysInAdvance, SeasonalPeriod, etc.
        
        [StringLength(50)]
        public string Operator { get; set; } // GreaterThan, LessThan, Equals, Between
        
        [StringLength(200)]
        public string Value { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual PricingRule PricingRule { get; set; }
    }

    public class SeasonalPricing
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string SeasonName { get; set; }
        
        public DateTime StartDate { get; set; }
        
        public DateTime EndDate { get; set; }
        
        public decimal PriceMultiplier { get; set; } = 1.0m;
        
        public bool IsRecurring { get; set; } = true;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Property Property { get; set; }
    }

    public class EventPricing
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string EventName { get; set; }
        
        public DateTime EventStartDate { get; set; }
        
        public DateTime EventEndDate { get; set; }
        
        public decimal PriceMultiplier { get; set; } = 1.0m;
        
        public decimal RadiusKm { get; set; } = 10.0m; // Event impact radius
        
        public bool IsActive { get; set; } = true;
        
        [StringLength(500)]
        public string Description { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Property Property { get; set; }
    }

    public class CompetitorPricing
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string CompetitorName { get; set; }
        
        [StringLength(500)]
        public string CompetitorUrl { get; set; }
        
        public decimal CompetitorPrice { get; set; }
        
        public DateTime PriceDate { get; set; }
        
        public decimal OurPrice { get; set; }
        
        public decimal PriceDifference { get; set; }
        
        public decimal DistanceKm { get; set; }
        
        public int SimilarityScore { get; set; } // 1-100
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Property Property { get; set; }
    }

    public class DemandForecast
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        
        public DateTime ForecastDate { get; set; }
        
        public decimal PredictedOccupancy { get; set; }
        
        public decimal PredictedADR { get; set; } // Average Daily Rate
        
        public decimal PredictedRevPAR { get; set; } // Revenue Per Available Room
        
        public decimal ConfidenceScore { get; set; }
        
        [StringLength(1000)]
        public string Factors { get; set; } // JSON string with influencing factors
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Property Property { get; set; }
    }

    public class PricingStrategy_Model
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        
        public PricingStrategy Strategy { get; set; } = PricingStrategy.Moderate;
        
        public bool AutoAdjustPricing { get; set; } = false;
        
        public decimal MinOccupancyThreshold { get; set; } = 0.3m;
        
        public decimal MaxOccupancyThreshold { get; set; } = 0.9m;
        
        public decimal PriceIncreaseStep { get; set; } = 0.05m;
        
        public decimal PriceDecreaseStep { get; set; } = 0.05m;
        
        public int DaysInAdvanceThreshold { get; set; } = 7;
        
        public bool ConsiderCompetitors { get; set; } = true;
        
        public bool ConsiderEvents { get; set; } = true;
        
        public bool ConsiderSeasons { get; set; } = true;
        
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Property Property { get; set; }
    }

    public class PriceHistory
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        public int? RoomId { get; set; }
        
        public DateTime Date { get; set; }
        
        public decimal Price { get; set; }
        
        public decimal BasePrice { get; set; }
        
        public decimal AdjustmentAmount { get; set; }
        
        [StringLength(200)]
        public string AdjustmentReason { get; set; }
        
        public decimal OccupancyRate { get; set; }
        
        public int BookingsCount { get; set; }
        
        public decimal Revenue { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Property Property { get; set; }
        public virtual Room Room { get; set; }
    }
}
