import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../services/auth.service';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NgIf } from '@angular/common';
import { GoogleSigninButtonModule } from '@abacritt/angularx-social-login';
import { MainNavbarComponent } from '../../main-navbar/main-navbar.component';
import { ValidationErrorComponent } from '../../common/validation-error/validation-error.component';
import { InputValidators } from '../../../validators/input-validators';
import { HttpErrorResponse } from '@angular/common/http';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    NgIf,
    GoogleSigninButtonModule,
    MainNavbarComponent,
    ValidationErrorComponent
  ],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css']
})
export class RegisterComponent implements OnInit {
  registerForm: FormGroup;
  errorMessage: string = '';
  showPassword: boolean = false;
  isLoading: boolean = false;
  registrationSuccess: boolean = false;

  constructor(
    private authService: AuthService,
    private router: Router,
    private fb: FormBuilder
  ) {
    this.registerForm = this.fb.group({
      email: ['', [Validators.required, InputValidators.emailFormat()]],
      firstName: ['', [Validators.required, InputValidators.textLength(2, 50), InputValidators.lettersOnly()]],
      lastName: ['', [Validators.required, InputValidators.textLength(2, 50), InputValidators.lettersOnly()]],
      password: ['', [
        Validators.required,
        InputValidators.textLength(8, 128),
        InputValidators.strongPassword()
      ]],
      confirmPassword: ['', [Validators.required]]
    }, {
      validators: InputValidators.passwordMatch('password', 'confirmPassword')
    });
  }

  ngOnInit(): void {
    // Check if user is already logged in
    if (this.authService.isLoggedIn) {
      this.router.navigate(['/home']);
    }
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  onSubmit() {
    this.errorMessage = '';

    if (this.registerForm.valid) {
      this.isLoading = true;
      const { email, firstName, lastName, password } = this.registerForm.value;

      console.log('Submitting registration with data:', { email, firstName, lastName, password: '***' });

      const payload = {
        email,
        firstName,
        lastName,
        password
      };

      // Use HttpClient with proper error handling
      this.authService.register(email, firstName, lastName, password)
        .pipe(
          finalize(() => this.isLoading = false)
        )
        .subscribe({
          next: (response) => {
            console.log('Registration successful:', response);
            this.registrationSuccess = true;
            setTimeout(() => {
              this.router.navigate(['/login'], {
                queryParams: { registered: 'true' }
              });
            }, 1500);
          },
          error: (err: HttpErrorResponse) => {
            console.error('Registration error details:', {
              status: err.status,
              statusText: err.statusText,
              error: err.error,
              message: err.message,
              url: err.url
            });

            if (err.status === 409) {
              this.errorMessage = 'User already exists. Please use a different email.';
            } else if (err.status === 400) {
              if (err.error && typeof err.error === 'object') {
                // Try to extract validation errors
                const validationErrors = Object.values(err.error).flat();
                if (validationErrors.length > 0) {
                  this.errorMessage = validationErrors.join(', ');
                } else {
                  this.errorMessage = 'Invalid registration data. Please check your information.';
                }
              } else {
                this.errorMessage = 'Invalid registration data. Please check your information.';
              }
            } else if (err.status === 0) {
              this.errorMessage = 'Cannot connect to the server. Please check your internet connection.';
            } else if (err.status === 500) {
              this.errorMessage = 'Server error. Please try again later.';
            } else {
              this.errorMessage = 'An error occurred. Please try again later.';
            }
          }
        });
    } else {
      this.markFormGroupTouched(this.registerForm);
    }
  }

  // Helper method to mark all form controls as touched to trigger validation display
  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  goToLogin() {
    this.router.navigate(['/login']);
  }

  signInWithGoogle(): void {
    this.isLoading = true;
    try {
      this.authService.signInWithGoogle();
    } catch (error) {
      console.error('Google sign-in error:', error);
      this.errorMessage = 'Failed to initialize Google sign-in. Please try again.';
      this.isLoading = false;
    }
  }
}