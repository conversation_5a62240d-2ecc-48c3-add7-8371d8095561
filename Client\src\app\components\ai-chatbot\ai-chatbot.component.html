<div class="chatbot-container">
  <!-- Floating Chat Icon -->
  <button class="chatbot-icon" (click)="toggleChat()" aria-label="Open AI Chatbot">
    <span class="material-icons">smart_toy</span>
  </button>

  <!-- Chat Window -->
  <div class="chat-window" [class.hidden]="!isChatOpen">
    <div class="chat-header">
      <h3>AI Assistant</h3>
      <button class="close-btn" (click)="toggleChat()" aria-label="Close Chat">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="chat-messages" #chatMessages>
      <div *ngFor="let message of messages; let i = index" class="message" [class.user-message]="message.sender === 'You'" [class.ai-message]="message.sender === 'Assistant'">
        <div class="message-bubble">
          <div class="message-sender">{{ message.sender }}</div>
          <div class="message-content" [innerHTML]="formatMessage(message.content)"></div>
          <div *ngIf="message.audioUrl && message.sender === 'Assistant'" class="audio-controls">
            <button class="play-button" (click)="playAudio(message.audioUrl!, i)" [disabled]="isPlaying && currentPlayingMessageIndex === i" [class.playing]="isPlaying && currentPlayingMessageIndex === i">
              <span class="material-icons">
                {{ isPlaying && currentPlayingMessageIndex === i ? 'pause_circle' : 'play_circle' }}
              </span>
              <span class="play-text">
                {{ isPlaying && currentPlayingMessageIndex === i ? 'Playing...' : 'Listen' }}
              </span>
            </button>
          </div>
          <div class="message-timestamp">{{ message.timestamp | date:'shortTime' }}</div>
        </div>
      </div>
      <div *ngIf="isLoading" class="loading-indicator">
        <div class="dot-flashing"></div>
      </div>
    </div>
    <div class="chat-input">
      <textarea [(ngModel)]="userInput" (keyup.enter)="$event.preventDefault(); onEnterPress($event)" placeholder="Type your message..." rows="2" [disabled]="isLoading"></textarea>
      <div class="button-group">
        <button class="record-button" (click)="isRecording ? stopRecording() : startRecording()" [disabled]="isLoading">
          <span class="material-icons">{{ isRecording ? 'stop' : 'mic' }}</span>
        </button>
        <button class="clear-btn" (click)="clearChat()">
          <span class="material-icons">delete_outline</span>
        </button>
        <button class="send-btn" (click)="sendMessage()" [disabled]="isLoading || userInput.trim() === ''">
          <span class="material-icons">send</span>
        </button>
      </div>
    </div>
  </div>

  <audio #audioPlayer style="display: none"></audio>
  <audio #audioRecorder style="display: none"></audio>
</div> 