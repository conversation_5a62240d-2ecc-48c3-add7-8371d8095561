using System.ComponentModel.DataAnnotations;

namespace API.Models
{
    public enum RoomStatus
    {
        Available,
        Occupied,
        OutOfOrder,
        Maintenance,
        Cleaning
    }

    public enum RoomType
    {
        Standard,
        Deluxe,
        Suite,
        Presidential,
        Studio,
        OneBedroom,
        TwoBedroom,
        Penthouse
    }

    public class Room
    {
        public int Id { get; set; }
        public int PropertyId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string RoomNumber { get; set; }
        
        [Required]
        public RoomType RoomType { get; set; }
        
        public RoomStatus Status { get; set; } = RoomStatus.Available;
        
        public int Floor { get; set; }
        
        public decimal BasePrice { get; set; }
        
        public int MaxOccupancy { get; set; }
        
        public int Bedrooms { get; set; }
        
        public int Bathrooms { get; set; }
        
        public decimal SquareFootage { get; set; }
        
        public bool HasBalcony { get; set; }
        
        public bool HasKitchen { get; set; }
        
        public bool HasLivingRoom { get; set; }
        
        [StringLength(1000)]
        public string Description { get; set; }
        
        [StringLength(500)]
        public string ViewType { get; set; } // Ocean, City, Garden, etc.
        
        public bool IsAccessible { get; set; }
        
        public bool AllowsSmoking { get; set; }
        
        public bool AllowsPets { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Property Property { get; set; }
        public virtual ICollection<RoomAmenity> RoomAmenities { get; set; } = new List<RoomAmenity>();
        public virtual ICollection<RoomImage> RoomImages { get; set; } = new List<RoomImage>();
        public virtual ICollection<RoomAvailability> RoomAvailabilities { get; set; } = new List<RoomAvailability>();
        public virtual ICollection<RoomBooking> RoomBookings { get; set; } = new List<RoomBooking>();
        public virtual ICollection<RoomMaintenance> MaintenanceRecords { get; set; } = new List<RoomMaintenance>();
        public virtual ICollection<HousekeepingTask> HousekeepingTasks { get; set; } = new List<HousekeepingTask>();
    }

    public class RoomAmenity
    {
        public int RoomId { get; set; }
        public int AmenityId { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Room Room { get; set; }
        public virtual Amenity Amenity { get; set; }
    }

    public class RoomImage
    {
        public int Id { get; set; }
        public int RoomId { get; set; }
        
        [Required]
        [StringLength(500)]
        public string ImageUrl { get; set; }
        
        public bool IsPrimary { get; set; }
        
        [StringLength(100)]
        public string Category { get; set; } // Bedroom, Bathroom, Living Room, etc.
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Room Room { get; set; }
    }

    public class RoomAvailability
    {
        public int Id { get; set; }
        public int RoomId { get; set; }
        public DateTime Date { get; set; }
        public bool IsAvailable { get; set; } = true;
        public decimal Price { get; set; }
        public int MinNights { get; set; } = 1;
        public int MaxNights { get; set; } = 30;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Room Room { get; set; }
    }

    public class RoomBooking
    {
        public int Id { get; set; }
        public int BookingId { get; set; }
        public int RoomId { get; set; }
        public DateTime CheckInDate { get; set; }
        public DateTime CheckOutDate { get; set; }
        public decimal RoomRate { get; set; }
        public int Guests { get; set; }
        public string SpecialRequests { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Booking Booking { get; set; }
        public virtual Room Room { get; set; }
    }
}
