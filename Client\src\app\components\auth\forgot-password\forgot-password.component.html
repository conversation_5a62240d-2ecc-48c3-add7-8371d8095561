<div class="login-container">
    <div class="login-card">
        <h1>Forgot Password</h1>
        <h2>Reset your password</h2>

        <p class="instruction">
            Enter your email address and we'll send you a link to reset your password.
        </p>

        <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()">
            <div class="form-group">
                <label>Email</label>
                <input type="email" formControlName="email" class="form-control" placeholder="Enter your email address">
                <div *ngIf="forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched"
                    class="error-message">
                    <span *ngIf="forgotPasswordForm.get('email')?.errors?.['required']">Email is required</span>
                    <span *ngIf="forgotPasswordForm.get('email')?.errors?.['email']">Please enter a valid email</span>
                </div>
            </div>

            <button type="submit" class="btn-primary" [disabled]="forgotPasswordForm.invalid || isSubmitting">
                {{ isSubmitting ? 'Sending...' : 'Send Reset Link' }}
            </button>
        </form>

        <div *ngIf="successMessage" class="alert alert-success">
            {{ successMessage }}
        </div>

        <div *ngIf="errorMessage" class="alert alert-danger">
            {{ errorMessage }}
        </div>

        <div class="back-to-login">
            <button class="btn-link" (click)="goToLogin()">Back to Login</button>
        </div>
    </div>
</div>